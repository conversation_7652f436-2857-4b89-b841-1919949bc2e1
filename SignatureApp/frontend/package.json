{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@column-resizer/react": "^1.3.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "axios": "^1.7.9", "bootstrap": "^5.2.3", "date-fns": "^4.1.0", "fast-password-entropy": "^1.1.1", "formik": "^2.4.0", "jest-editor-support": "^31.1.2", "leaflet": "^1.9.4", "lodash": "^4.17.21", "papaparse": "^5.4.1", "re-resizable": "^6.9.16", "react": "^17.0.2", "react-dom": "^17.0.2", "react-leaflet": "^3.2.5", "react-modal": "^3.16.1", "react-rnd": "^10.5.2", "react-router-dom": "^6.2.1", "react-scripts": "5.0.0", "react-select": "^5.8.0", "recharts": "^2.6.2", "typescript": "^5.7.3", "web-vitals": "^4.2.4"}, "scripts": {"start": "set HTTPS=true && react-scripts start", "build:dev": "BUILD_PATH='./build_dev' env-cmd -f .env.development react-scripts build", "build:prod": "BUILD_PATH='./build_prod' env-cmd -f .env.production  react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "prestart": "node aspnetcore-https && node aspnetcore-react"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@azure/storage-blob": "^12.23.0", "@simbathesailor/use-what-changed": "^2.0.0", "@testing-library/cypress": "^10.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^14.6.1", "@types/bootstrap": "^5.2.10", "@types/geojson": "^7946.0.16", "@types/jest": "^29.5.14", "@types/leaflet": "^1.9.17", "@types/lodash": "^4.17.15", "@types/mssql": "^9.1.5", "@types/node": "^22.13.1", "@types/papaparse": "^5.3.7", "@types/react": "^17.0.60", "@types/react-dom": "^17.0.20", "@types/react-modal": "^3.16.0", "@types/react-router-dom": "^5.3.3", "@types/recharts": "^1.8.24", "cypress": "^14.3.3", "cypress-network-idle": "^1.15.0", "env-cmd": "^10.1.0", "http-proxy-middleware": "^2.0.6", "msnodesqlv8": "^4.2.1"}, "overrides": {"typescript": "^5.7.3"}}