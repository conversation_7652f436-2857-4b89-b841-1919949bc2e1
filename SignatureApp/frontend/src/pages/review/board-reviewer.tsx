import { SyntheticEvent, useCallback, useEffect, useRef, useState } from "react";
import { useNavigate, useParams, useLocation, Link } from "react-router-dom";
import { useAuthContext } from "../../AuthProvider";
import { useWorkService } from "../../services/work.service";
import { RegisteredVoterFlags, ValidationForm, WorkDto, WorkField, WorkStatus } from "../../types/work.types";
import { FormikProps } from "formik";
import { checkForRole, convertFieldValuesToStrings, convertFieldValuesToTypes, handleError } from "../../common/utils";
import WorkItem from "../../components/work-item.component";
import { Loader } from "../../components/loader";
import { InlineSpinner } from "../../components/inline-spinner";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { Roles } from "../../types/user.types";

export default function ReviewerBoard() {
  const { authenticatedUser } = useAuthContext();
  const { getWork, getNextWork, getPreviousWork, updateWork } = useWorkService();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [work, setWork] = useState<WorkDto | null>();
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isOnBreak, setIsOnBreak] = useState(false);
  const [isWorkCompleted, setIsWorkCompleted] = useState(false);
  const params = useParams();
  const workId = params?.workId;
  const formikRef = useRef<FormikProps<ValidationForm>>(null);
  const [previousPath, setPreviousPath] = useState<string>("");
  const [nextWorkText, setNextWorkText] = useState<string>("");
  const [roleId, setRoleId] = useState<number>();
  const [matterId, setMatterId] = useState<number>(0);
  const loadWork = useCallback(
    (workId) => {
      setErrorMessage("");
      setLoading(true);
      getWork(workId).then(
        (work) => {
          setLoading(false);
          work.fields = convertFieldValuesToStrings(work.fields);
          setWork(work);
          if(work.matterId > 0) {
            setMatterId(work.matterId);
          }
          navigate(`/work/${work?.workId}`);
        },
        (error) => {
          handleError(error, setErrorMessage, setLoading);
        }
      );
    },
    [getWork, navigate]
  );

  useEffect(() => {
    if (isInitialLoad && workId) {
      loadWork(workId);
    }
  }, [workId, isInitialLoad, loadWork]);

  useEffect(() => {
    setRoleId(authenticatedUser?.roleId);
    if (checkForRole(Roles.Manager, authenticatedUser?.roleId)) {
      setNextWorkText("Save");
    }
  }, [authenticatedUser, authenticatedUser?.roleId]);

  useEffect(() => {
    const from = location.state?.from;
    if (from && !/^\/work\/[^/]+$/.test(from)) {
      setPreviousPath(from);
    }
  }, [location.state, location.state?.from, location.state?.isLast]);

  const loadNextWork = useCallback(() => {
    setErrorMessage("");
    setLoading(true);
    setWork(null);
    getNextWork().then(
      (work) => {
        setIsInitialLoad(false);
        setLoading(false);
        if (work) {
          work.fields = convertFieldValuesToStrings(work.fields);
          setWork(work);
          navigate(`/work/${work?.workId}`);
        } else {
          // All work completed
          setIsWorkCompleted(true);
          navigate(previousPath);
        }
      },
      (error) => {
        handleError(error, setErrorMessage, setLoading);
      }
    );
  }, [getNextWork, navigate, previousPath]);

  const loadPreviousWork = (e: SyntheticEvent) => {
    e.preventDefault();

    if (work?.workId) {
      setErrorMessage("");
      setWork(null);
      if (checkForRole(Roles.Manager, roleId)) {
        return;
      }
      setLoading(true);
      getPreviousWork(work?.workId.toString()).then(
        (work) => {
          setIsInitialLoad(false);
          setLoading(false);
          if (work) {
            work.fields = convertFieldValuesToStrings(work.fields);
            setWork(work);
            navigate(`/work/${work?.workId}`);
          } else {
            navigate("/work");
          }
        },
        (error) => {
          handleError(error, setErrorMessage, setLoading);
        }
      );
    }
  };

  async function callUpdateWork(workId: string, workStatus: WorkStatus): Promise<void> {
    setLoading(true);
    setErrorMessage("");

    var work = {
      workStatusId: workStatus,
      fields: convertFieldValuesToTypes(formikRef.current?.values.fields!),
      externalDataRecordId: formikRef.current?.values.externalDataRecordId || null,
      registeredVoterFlags: formikRef.current?.values.registeredVoterFlags || RegisteredVoterFlags.Unknown,
    };
    await updateWork(workId, work);
  }

  const flagForReview = (e: SyntheticEvent) => {
    e.preventDefault();

    if (work?.workId) {
      callUpdateWork(work.workId.toString(), WorkStatus.Flagged).then(loadNextWork);
    }
  };

  const takeBreak = (e: SyntheticEvent) => {
    e.preventDefault();

    if (work?.workId) {
      callUpdateWork(work.workId.toString(), WorkStatus.Break).then(() => {
        setIsOnBreak(true);
        setWork(null);
      });
    }
  };

  const handleSaveWork = useCallback(
    (
      fields: WorkField[],
      externalDataRecordId?: string | null,
      registeredVoterFlags?: RegisteredVoterFlags | null
    ): Promise<void> => {
      if (!work?.workId) {
        return Promise.reject("No work");
      }
      setErrorMessage("");
      setLoading(true);
      return updateWork(work?.workId.toString(), {
        workStatusId: WorkStatus.Completed,
        fields: convertFieldValuesToTypes(fields),
        externalDataRecordId: externalDataRecordId || null,
        registeredVoterFlags: registeredVoterFlags || RegisteredVoterFlags.Unknown,
      }).then(
        () => {
          setLoading(false);
          setWork({ ...work, fields: convertFieldValuesToStrings(fields) });
        },
        (error) => {
          handleError(error, setErrorMessage, setLoading);
        }
      );
    },
    [updateWork, work]
  );

  const getBackButtonText = function () {
    if(!!previousPath){
      return "Back to Deficiencies";
    }
    return "Back to Matter";
  }

  const handleNextWork = useCallback(
    (fields: WorkField[], externalDataRecordId?: string | null, registeredVoterFlags?: RegisteredVoterFlags | null) => {
      if (work?.workId) {
        handleSaveWork(fields, externalDataRecordId, registeredVoterFlags).then(
          () => {
            loadNextWork();
          },
          (error) => {
            handleError(error, setErrorMessage, setLoading);
          }
        );
      }
    },
    [handleSaveWork, loadNextWork, work?.workId]
  );
  return (
    <div className="container">
      <div className="d-flex flex-row align-items-center mb-4">
        <div className="flex-shrink-0">
          {checkForRole(Roles.Admin, roleId) && (
            <Link
              to={previousPath || `/admin/matters/${matterId}/details`}
              className="btn btn-sw btn-sm btn-outline"
            >
              <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
              {getBackButtonText()}
            </Link>
          )}
        </div>

        <div className="flex-grow-1 d-flex justify-content-center">
          {work?.matterName && <span>{work?.matterName}</span>}
          {work?.sheetNumber && <span>{` - Sheet ${work?.sheetNumber}`}</span>}
        </div>
      </div>
      <div className="container bg-white">
        {!isOnBreak && !isWorkCompleted && workId && (
          <>
            {loading && <Loader />}

            {!loading && work && (
              <WorkItem
                work={work}
                onSave={handleSaveWork}
                onNext={handleNextWork}
                onPrevious={loadPreviousWork}
                onFlagForReview={flagForReview}
                onTakeBreak={takeBreak}
                formikRef={formikRef}
                errorMessage={errorMessage}
                nextLabel={nextWorkText}
                isReviewer={!checkForRole(Roles.Manager, roleId)}
              />
            )}
          </>
        )}

        {!isOnBreak && !isWorkCompleted && !workId && (
          <div className="row">
            <div className="col text-center mt-5">
              <h1>BEGIN TASK</h1>
              <div className="mb-3">Click continue to begin a set of tasks</div>
              <button
                type="button"
                className="btn btn-sw mb-5"
                disabled={loading}
                onClick={() => {
                  loadNextWork();
                }}
              >
                {loading && <InlineSpinner />}
                Continue
              </button>
            </div>
          </div>
        )}

        {isOnBreak && (
          <div className="row">
            <div className="col text-center mt-5">
              <h1>RESUME</h1>
              <div className="mb-3">Click resume to begin</div>
              <button
                type="button"
                className="btn btn-sw mb-5"
                onClick={() => {
                  setIsOnBreak(false);
                  loadNextWork();
                }}
              >
                {loading && <InlineSpinner />}
                Resume
              </button>
            </div>
          </div>
        )}

        {isWorkCompleted && (
          <div className="row">
            <div className="col text-center mt-5">
              <h1>ALL DONE</h1>
              <div className="mb-5">You've completed all task work</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
