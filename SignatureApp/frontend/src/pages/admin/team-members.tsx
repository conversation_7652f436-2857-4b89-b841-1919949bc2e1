import { faCircleLeft, faSquarePlus, faTrashCan } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useCallback, useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Roles, User } from "../../types/user.types";
import { handleError } from "../../common/utils";
import { useUserService } from "../../services/user.service";
import ConfirmModal from "../../components/confirm-modal";
import { useAuthGuard } from "../../hooks/useAuthGuard";

export default function TeamMembers() {
  useAuthGuard(Roles.Admin);

  const [members, setMembers] = useState<User[]>([]);
  const [loadingMembers, setLoadingMembers] = useState(true);
  const [userIdToDelete, setUserIdToDelete] = useState<string>();
  const [showConfirmDeleteUserModal, setShowConfirmDeleteUserModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { getMembers, deleteUser } = useUserService();

  const loadMembers = useCallback(() => {
    setErrorMessage("");
    setLoadingMembers(true);

    getMembers().then(
      (members) => {
        setMembers(members);
        setLoadingMembers(false);
      },
      (error) => {
        handleError(error, setErrorMessage, setLoadingMembers);
      }
    );
  }, [getMembers]);

  useEffect(() => {
    loadMembers();
  }, [loadMembers]);

  const handleDeleteMember = () => {
    if (userIdToDelete) {
      setErrorMessage("");
      setLoadingMembers(true);

      deleteUser(userIdToDelete).then(
        () => {
          setLoadingMembers(false);
          loadMembers();
        },
        (error) => {
          handleError(error, setErrorMessage, setLoadingMembers);
        }
      );
    }
  };

  return (
    <div className="container">
      <div className="d-flex flex-row mb-4">
        <div className="me-auto" style={{ marginLeft: "-12px" }}>
          <Link to="/admin" className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            Back to Admin
          </Link>
        </div>
      </div>
      <div className="row tablet-view">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-3">
            <span>Administrator</span>
            <h2>Invite</h2>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <div className="d-flex left-right">
              <h3>Team Members</h3>
              <Link to="/admin/invite" className="btn btn-sw btn-outline">
                <FontAwesomeIcon icon={faSquarePlus} className="me-2" />
                Invite Team Member
              </Link>
            </div>
            <div
              style={{
                maxHeight: 400,
                overflowY: "auto",
                overflowX: "hidden",
              }}
            >
              {members?.length > 0 && (
                <table className="sw">
                  <thead>
                    <tr className="d-flex">
                      <th scope="col" className="col-sm-5">
                        Name
                      </th>
                      <th scope="col" className="col-sm-6">
                        Email
                      </th>
                      <th scope="col" className="col-sm-1"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {members?.map((member) => (
                      <tr key={member.email} className="d-flex border-bottom">
                        <td className="col-sm-5">{member.fullName}</td>
                        <td className="col-sm-6">{member.email}</td>
                        <td className="col-sm-1">
                          <a
                            onClick={() => {
                              setShowConfirmDeleteUserModal(true);
                              setUserIdToDelete(member.id.toString());
                            }}
                          >
                            <FontAwesomeIcon icon={faTrashCan} />
                          </a>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
              {!members?.length && !loadingMembers && (
                <div className="list-group-item list-group-item-light">There are no reviewers</div>
              )}
              <ConfirmModal
                message="Are you sure you want to delete this member?"
                action={handleDeleteMember}
                isOpen={showConfirmDeleteUserModal}
                close={() => {
                  setShowConfirmDeleteUserModal(false);
                }}
              />
            </div>
          </div>
          {errorMessage && (
            <div className="row">
              <div className="col">
                <div className="form-group">
                  <div className="alert alert-danger" role="alert">
                    {errorMessage}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
