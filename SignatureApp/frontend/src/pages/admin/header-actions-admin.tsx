import { Link } from "react-router-dom";
import { useMatterService } from "../../services/matter.service";

export default function HeaderActionsAdmin() {
  useMatterService();

  return (
    <div className="d-flex flex-row gap-2">
      <div className="ms-auto" style={{ marginRight: "-12px" }}>
        <div className="dropdown">
          <button
            className="btn btn-sw btn-sm btn-outline dropdown-toggle"
            type="button"
            id="dropdownMenuButton"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
          >
            Actions
          </button>
          <div className="dropdown-menu" aria-labelledby="dropdownMenuButton">
            <Link to="/admin/members" className="dropdown-item">
              Team Members
            </Link>
            <Link to="/admin/states" className="dropdown-item">
              Reference Data
            </Link>
            <a
              href={`${process.env.REACT_APP_BACKEND_URL?.replace("/api", "")}healthchecks-ui#/healthchecks`}
              target="_blank"
              rel="noopner noreferrer"
              className="dropdown-item"
            >
              Health Check
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
