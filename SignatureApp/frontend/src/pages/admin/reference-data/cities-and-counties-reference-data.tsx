import { Link, useNavigate, useParams } from "react-router-dom";
import { useReferenceDataService } from "../../../services/reference-data-service";
import { useEffect, useState } from "react";
import { UsState } from "../../../types/reference-data.types";
import { Boundary } from "../../../types/boundary.types";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";

export default function CitiesAndCountiesReferenceData() {
  useAuthGuard(Roles.Admin);
  const {
    getStateById,
    getCountiesByStateId,
    getCitiesByStateId,
    getZipCodesByStateId,
  } = useReferenceDataService();
  const [state, setState] = useState<UsState>();
  const [counties, setCounties] = useState<Boundary[]>([]);
  const [cities, setCities] = useState<Boundary[]>([]);
  const [zipCodes, setZipCodes] = useState<Boundary[]>([]);
  const navigate = useNavigate();
  const params = useParams();
  const stateId = params?.stateId;

  useEffect(() => {
    if (!stateId) return navigate("/admin/states");

    getStateById(stateId).then((data) => setState(data));
    getCountiesByStateId(stateId).then((data) => setCounties(data));
    getCitiesByStateId(stateId).then((data) => setCities(data));
    getZipCodesByStateId(stateId).then((data) => setZipCodes(data));
  }, [getStateById, getCountiesByStateId, getZipCodesByStateId, stateId]);

  return (
    <div className="container">
      <div className="row mb-4">
        <div className="col-12" style={{ marginLeft: "-12px" }}>
          <Link to="/admin/states" className="btn btn-sw btn-sm btn-outline">
            Back to States
          </Link>
        </div>
      </div>
      <div className="row tablet-view justify-content-start">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-2">
            <div className="mb-auto">
              <span>Administrator</span>
              <h2>Reference Data</h2>
              <h2>{state?.name}</h2>
            </div>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <div className="mt-3">
              {counties?.length > 0 && (
                <table className="sw">
                  <thead>
                    <tr className="d-flex">
                      <th scope="col" className="col-sm-12">
                        {`Counties (${counties.length})`}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {counties?.map((county) => (
                      <tr key={county.id} className="d-flex border-bottom">
                        <td className="col-sm-8">{county.name}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
            <div className="mt-3">
              {cities?.length > 0 && (
                <table className="sw">
                  <thead>
                    <tr className="d-flex">
                      <th scope="col" className="col-sm-12">
                        {`Cities (${cities.length})`}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {cities?.map((city) => (
                      <tr key={city.id} className="d-flex border-bottom">
                        <td className="col-sm-8">{city.name}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
            <div className="mt-3">
              {zipCodes?.length > 0 && (
                <table className="sw">
                  <thead>
                    <tr className="d-flex">
                      <th scope="col" className="col-sm-12">
                        {`ZipCodes (${zipCodes.length})`}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {zipCodes?.map((zip) => (
                      <tr key={zip.id} className="d-flex border-bottom">
                        <td className="col-sm-8">{zip.name}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
