import { Link, useNavigate } from "react-router-dom";
import { useReferenceDataService } from "../../../services/reference-data-service";
import { useEffect, useState } from "react";
import { UsState } from "../../../types/reference-data.types";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";

export default function StatesReferenceData() {
  useAuthGuard(Roles.Admin);

  const { getStates } = useReferenceDataService();
  const [states, setStates] = useState<UsState[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    getStates().then((data) => setStates(data));
  }, [getStates]);

  return (
    <div className="container">
      <div className="row mb-4">
        <div className="col-12" style={{ marginLeft: "-12px" }}>
          <Link to="/admin" className="btn btn-sw btn-sm btn-outline">
            Back to Admin
          </Link>
        </div>
      </div>
      <div className="row tablet-view justify-content-start">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-2">
            <div className="mb-auto">
              <span>Administrator</span>
              <h2>Reference Data</h2>
            </div>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <div className="mt-3">
              {states?.length > 0 && (
                <table className="sw">
                  <thead>
                    <tr className="d-flex">
                      <th scope="col" className="col-sm-8">
                        Name
                      </th>
                      <th scope="col" className="col-sm-4">
                        Abbreviation
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {states?.map((state) => (
                      <tr key={state.id} className="d-flex border-bottom">
                        <td
                          className="col-sm-8"
                          onClick={() => navigate(`/admin/states/${state.id}/cities-and-counties`)}
                        >
                          {state.name}
                        </td>
                        <td className="col-sm-4">{state.abbreviation}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
