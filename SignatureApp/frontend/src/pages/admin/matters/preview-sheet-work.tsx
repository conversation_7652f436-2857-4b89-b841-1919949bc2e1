import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { PreviewRowWorkItems } from "../../../components/preview-row-work-items";
import { useWorkService } from "../../../services/work.service";
import { Work } from "../../../types/work.types";
import { PreviewFieldWorkItems } from "../../../components/preview-field-work-items";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";

export interface PreviewSheetWorkProps {
  isRelease: boolean;
}

export default function PreviewSheetWork({ isRelease }: PreviewSheetWorkProps) {
  useAuthGuard(Roles.Admin);

  const { matterId, sheetNumber } = useParams();
  const navigate = useNavigate();

  const { getUnavailableWork, releaseUnavailableWork } = useWorkService();
  const [rowWorks, setRowWorks] = useState<Work[][]>();
  const [fieldWorks, setFieldWorks] = useState<Work[]>();
  const [isRowWorkReleasable, setIsRowWorkReleasable] = useState<boolean[][]>(
    []
  );
  const [isFieldWorkReleasable, setIsFieldWorkReleasable] = useState<boolean[]>(
    []
  );

  useEffect(() => {
    if (matterId && sheetNumber) {
      getUnavailableWork(matterId, sheetNumber).then((works) => {
        // are there any row work items?
        const rowWorks = works.filter((work) => work.rowNumber > 0);
        if (rowWorks.length > 0) {
          // group works by rowNumber
          const groupedWorks = works.reduce(
            (acc: { [key: number]: Work[] }, work) => {
              if (!acc[work.rowNumber!]) {
                acc[work.rowNumber!] = [];
              }
              acc[work.rowNumber!].push(work);
              return acc;
            },
            {}
          );

          const rowWorksArray = Object.values(groupedWorks);
          setRowWorks(rowWorksArray);
          setIsRowWorkReleasable(
            rowWorksArray.map((rowWork) => rowWork.map(() => true))
          );
        } else {
          setRowWorks([]);
          setFieldWorks(works);
          setIsFieldWorkReleasable(works.map(() => true));
        }
      });
    }
  }, [getUnavailableWork, matterId, sheetNumber]);

  function handleRelease(): void {
    const selectedWorkIds: number[] = [];

    if (rowWorks) {
      rowWorks.forEach((rowWork, index) => {
        rowWork.forEach((work, idx) => {
          if (isRowWorkReleasable[index][idx]) {
            if (work.id) {
              selectedWorkIds.push(work.id);
            }
          }
        });
      });
    }

    if (fieldWorks) {
      fieldWorks.forEach((work, index) => {
        if (isFieldWorkReleasable[index]) {
          if (work.id) {
            selectedWorkIds.push(work.id);
          }
        }
      });
    }

    // Call releaseUnavailableWork with selected work IDs
    if (selectedWorkIds.length > 0 && matterId && sheetNumber) {
      releaseUnavailableWork(matterId, sheetNumber, selectedWorkIds).then(
        (remainingWork) => {
          // are there any more work items?
          const fieldWork = remainingWork.filter((w) => w.fieldNumber > 0);
          if (fieldWork.length > 0) {
            setRowWorks([]);
            setFieldWorks(fieldWork);
            setIsFieldWorkReleasable(fieldWork.map((w) => true));
          } else {
            // navigate to back to the matter details page
            navigate(`/admin/matters/${matterId}/details`);
          }
        }
      );
    }
  }

  return (
    <div className="container-fluid bg-sw-secondary">
      <div className="row bg-sw-primary ps-4 pe-4">
        <div className="col align-self-start mb-4">
          <Link
            to={`/admin/matters/${matterId}/sheets/${sheetNumber}`}
            className="btn btn-sw btn-sm btn-outline"
          >
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            Back to Sheet
          </Link>
        </div>
      </div>

      <div className="tablet-buffer bg-sw-secondary pt-0">
        {rowWorks?.map((rowWork, index) => (
          <PreviewRowWorkItems
            key={rowWork[0].rowNumber}
            isRelease={true}
            rowNumber={rowWork[0].rowNumber}
            workReleasable={isRowWorkReleasable[index]}
            setWorkReleasable={(updater) => {
              setIsRowWorkReleasable((prev) => {
                const newState = [...prev];
                newState[index] = updater(newState[index]);
                return newState;
              });
            }}
          />
        ))}
        {fieldWorks && (
          <PreviewFieldWorkItems
            isRelease={true}
            workItems={fieldWorks}
            workReleasable={isFieldWorkReleasable}
            setWorkReleasable={setIsFieldWorkReleasable}
          />
        )}

        <div className="text-center mt-4">
          <button className="btn btn-sw" onClick={handleRelease}>
            Release
          </button>
        </div>
      </div>
    </div>
  );
}
