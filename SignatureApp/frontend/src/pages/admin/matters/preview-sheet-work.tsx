import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { PreviewWorkItems } from "../../../components/preview-work-items";
import { useWorkService } from "../../../services/work.service";
import { UnreleasedWorkType, WorkDto } from "../../../types/work.types";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";
import { Loader } from "../../../components/loader";
import HeaderNavBar from "../../../components/header-nav-bar";

export interface PreviewSheetWorkProps {
  isRelease: boolean;
}

export default function PreviewSheetWork({ isRelease }: PreviewSheetWorkProps) {
  useAuthGuard(Roles.Admin);

  const { matterId, sheetNumber } = useParams();
  const navigate = useNavigate();

  const { getUnavailableWork, releaseUnavailableWork, getPreviewWorkItems } = useWorkService();
  const [allWorkItems, setAllWorkItems] = useState<WorkDto[]>([]);
  const [workReleasable, setWorkReleasable] = useState<boolean[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isReleasing, setIsReleasing] = useState(false);

  useEffect(() => {
    document.documentElement.style.backgroundColor = "white";
    return () => {
      document.documentElement.style.backgroundColor = "";
    };
  }, []);

  useEffect(() => {
    if (matterId && sheetNumber) {
      setIsLoading(true);
      getUnavailableWork(matterId, sheetNumber).then(async (unreleased) => {
        console.log("Unreleased work type:", unreleased.unreleasedWorkType);
        const works = unreleased.works;
        if (works.length > 0) {
          const workDtos = await getPreviewWorkItems(works);
          setAllWorkItems(workDtos);
          setWorkReleasable(workDtos.map(() => true));
        } else {
          setAllWorkItems([]);
          setWorkReleasable([]);
        }
        setIsLoading(false);
      });
    }
  }, [getUnavailableWork, getPreviewWorkItems, matterId, sheetNumber]);

  function handleRelease(): void {
    const selectedWorkIds: number[] = [];

    allWorkItems.forEach((work, index) => {
      if (workReleasable[index] && work.workId) {
        selectedWorkIds.push(work.workId);
      }
    });

    // Call releaseUnavailableWork with selected work IDs
    if (selectedWorkIds.length > 0 && matterId && sheetNumber) {
      setIsReleasing(true);
      releaseUnavailableWork(matterId, sheetNumber, selectedWorkIds)
        .then(async (remainingWork) => {
          if (remainingWork.unreleasedWorkType !== UnreleasedWorkType.None && remainingWork.works.length > 0) {
            const remainingWorkDtos = await getPreviewWorkItems(remainingWork.works);
            setAllWorkItems(remainingWorkDtos);
            setWorkReleasable(remainingWorkDtos.map(() => true));
          } else {
            // navigate to back to the matter details page
            navigate(`/admin/matters/${matterId}/details`);
          }
        })
        .finally(() => {
          setIsReleasing(false);
        });
    }
  }

  return (
    <div className="container-fluid bg-sw-secondary">
      <HeaderNavBar backTo="sheet" variant="container-fluid" />

      <div className="tablet-buffer bg-sw-secondary pt-0">
        {isLoading ? (
          <Loader />
        ) : (
          <>
            <PreviewWorkItems
              isRelease={isRelease}
              isLoading={false}
              workItems={allWorkItems}
              workReleasable={isRelease ? workReleasable : undefined}
              setWorkReleasable={isRelease ? setWorkReleasable : undefined}
            />

            {allWorkItems.length > 0 && (
              <div className="text-center mt-4">
                <button className="btn btn-sw" onClick={handleRelease} disabled={isReleasing}>
                  {isReleasing ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Releasing...
                    </>
                  ) : (
                    "Release"
                  )}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
