import { faCircleLeft, faNewspaper } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ErrorMessage, Field, Form, Formik } from "formik";
import { useState } from "react";
import { Link, useParams } from "react-router-dom";
import { Roles } from "../../../types/user.types";
import { useExternalDataService } from "../../../services/external-data.service";
import { VoterSearchDTO } from "../../../types/external-data.types";
import { InlineSpinner } from "../../../components/inline-spinner";
import { useAuthGuard } from "../../../hooks/useAuthGuard";

interface FormValues {
  firstName: string;
  lastName: string;
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  voterId: number;
  party: string;
  congressionalDistrict: string;
  legislativeDistrict: string;
}

export default function FreeFormVoterSearch() {
  useAuthGuard(Roles.Admin);

  const { freeFormVoterSearch } = useExternalDataService();
  const { matterId } = useParams();
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const [voterSearchResults, setVoterSearchResults] = useState<VoterSearchDTO[]>([]);
  const [totalHits, setTotalHits] = useState<number>(0);

  const handleSave = async (formValues: FormValues, actions: any) => {
    const {
      firstName,
      lastName,
      streetAddress,
      city,
      state,
      zipCode,
      voterId,
      party,
      congressionalDistrict,
      legislativeDistrict,
    } = formValues;

    setSuccessMessage("");
    setErrorMessage("");
    setLoading(true);
    if (matterId == null) {
      return;
    }

    const criteria = {
      firstName,
      lastName,
      streetAddress,
      city,
      state,
      zipCode,
      voterId,
      party,
      congressionalDistrict,
      legislativeDistrict,
    };
    const result = await freeFormVoterSearch(matterId, criteria);
    setVoterSearchResults(result.results);
    setTotalHits(result.totalHits);
    setLoading(false);
  };

  return (
    <div className="container">
      <div className="d-flex flex-row mb-4">
        <div className="me-auto" style={{ marginLeft: "-12px" }}>
          <Link to={`/admin/matters/${matterId}/details`} className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            Back to Matter
          </Link>
        </div>
      </div>
      <div className="row tablet-view justify-content-start">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-2">
            <div className="mb-auto">
              <h2>Voter Search</h2>
            </div>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <Formik
              initialValues={{
                firstName: "",
                lastName: "",
                streetAddress: "",
                city: "",
                state: "",
                zipCode: "",
                voterId: 0,
                party: "",
                congressionalDistrict: "",
                legislativeDistrict: "",
              }}
              onSubmit={(formValues, actions) => {
                handleSave(formValues, actions);
              }}
            >
              <Form>
                <div className="row">
                  <div className="col-6 form-group mb-3">
                    <label htmlFor="firstName">First Name</label>
                    <Field name="firstName" type="text" placeholder="First Name" className="form-control" />
                    <ErrorMessage name="firstName" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="col-6 form-group mb-3">
                    <label htmlFor="lastName">Last Name</label>
                    <Field name="lastName" type="text" placeholder="Last Name" className="form-control" />
                    <ErrorMessage name="lastName" component="div" className="alert alert-danger p-2" />
                  </div>
                </div>
                <div className="row">
                  <div className="form-group mb-3">
                    <label htmlFor="address">Address</label>
                    <Field name="streetAddress" type="text" placeholder="Address" className="form-control" />
                    <ErrorMessage name="streetAddress" component="div" className="alert alert-danger p-2" />
                  </div>
                </div>
                <div className="row">
                  <div className="col-sm-4">
                    <div className="form-group mb-3">
                      <button type="submit" className="btn btn-sw" disabled={loading}>
                        {loading && <InlineSpinner />}
                        <FontAwesomeIcon icon={faNewspaper} className="me-2" />
                        Search
                      </button>
                    </div>
                  </div>
                  <div className="col-sm-8 pt-2 text-end">
                    {totalHits > 0 && (
                      <span className="text-muted">
                        Showing {voterSearchResults?.length} of {totalHits} possible matches
                      </span>
                    )}
                  </div>
                </div>
                {errorMessage && (
                  <div className="form-group mb-3">
                    <div className="alert alert-danger" role="alert">
                      {errorMessage}
                    </div>
                  </div>
                )}
                {successMessage && (
                  <div className="form-group mb-3">
                    <div className="alert alert-success" role="alert">
                      {successMessage}
                    </div>
                  </div>
                )}
              </Form>
            </Formik>
            <div
              style={{
                maxHeight: 400,
                overflowY: "auto",
                overflowX: "hidden",
                marginTop: "1.5 rem",
              }}
            >
              {voterSearchResults?.length > 0 && (
                <table className="table table-borderless">
                  <tbody>
                    {voterSearchResults?.map((voterSearchResult, index) => (
                      <tr key={index} className="mb-3 border">
                        <td className="card-body">
                          <div className="row">
                            <div className="col-md-12">
                              <p className="card-text mb-0">
                                <strong>
                                  {voterSearchResult?.firstName} {voterSearchResult?.lastName}
                                </strong>
                                &nbsp;-- Voter ID: {voterSearchResult?.voterId || ""}
                              </p>
                            </div>
                          </div>

                          <div className="row">
                            <div className="col-md-12">
                              <p className="card-text mb-0">
                                <strong>Party:</strong> {voterSearchResult?.party || ""}
                                <strong>, Congressional:</strong> {voterSearchResult?.congressionalDistrict || ""}
                                <strong>, Legislative:</strong> {voterSearchResult?.legislativeDistrict || ""}
                              </p>
                            </div>
                          </div>

                          <div className="row">
                            <div className="col-md-12">
                              <p className="card-text mb-0 border-bottom-0">
                                <strong>Address:</strong> {voterSearchResult?.streetAddress},{" "}
                                {voterSearchResult?.city || ""}, {voterSearchResult?.state || ""},{" "}
                                {voterSearchResult?.zipCode || ""}
                              </p>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
