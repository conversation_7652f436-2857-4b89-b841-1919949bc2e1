import {
  faCircleLeft,
  faPlayCircle,
  faPenToSquare,
} from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { formatDistance, subSeconds } from "date-fns";
import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { formatDateString, handleError } from "../../../common/utils";
import { useMatterService } from "../../../services/matter.service";
import { useRuleService } from "../../../services/rule.service";
import { Matter, MatterVariable } from "../../../types/matter.types";
import { Roles } from "../../../types/user.types";
import { useProgressWithRefresh } from "../../../hooks/useProgressWithRefresh";
import { useChecklistWithRefresh } from "../../../hooks/useChecklistWithRefresh";
import MatterChecklist from "../../../components/matter-checklist.component";
import MatterProgress from "../../../components/matter-progress.component";
import MatterUploadCirculators from "../../../components/upload-circulators.component";
import MatterUploadSignatures from "../../../components/matter-upload-signatures.component";
import MatterParameters from "../../../components/matter-parameters.component";
import MatterVoterStatus from "../../../components/matter-voter-status.component";
import { useMatterParametersService } from "../../../services/matter-parameters.service";
import { ExecutionStatus } from "../../../types/background-operation.types";
import { InlineSpinner } from "../../../components/inline-spinner";
import MatterDetailsActions from "../../../components/matter-details-actions";
import { useAuthGuard } from "../../../hooks/useAuthGuard";

export default function MatterDetails() {
  useAuthGuard(Roles.Admin);

  const { getMatterById } = useMatterService();
  const { runRules, getLastRunRulesOperation, getBackgroundOperationStatus } =
    useRuleService();
  const { getMatterParameters } = useMatterParametersService();

  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [isRunningRules, setIsRunningRules] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [matter, setMatter] = useState<Matter | null>(null);
  const { checklist, refreshChecklist } = useChecklistWithRefresh();
  const [matterParameters, setMatterParameters] = useState<MatterVariable[]>(
    []
  );

  const [backgroundId, setBackgroundId] = useState<number>(0);

  const [isTimerActive, setIsTimerActive] = useState(false);
  const interval = useRef<NodeJS.Timeout | null>(null);
  const [timeSinceLastRun, setTimeSinceLastRun] = useState<string>("");

  const {
    progress,
    isLoading: progressIsLoading,
    errorMessage: progressErrorMessage,
    refreshProgress,
  } = useProgressWithRefresh();

  const params = useParams();
  const matterId = params?.matterId || "";
  const isNew = matterId === "new";

  const loadSecondsSinceRuleRunComplete = useCallback(() => {
    getLastRunRulesOperation(matterId).then((lastRun) => {
      if (!lastRun) {
        setTimeSinceLastRun("");
        return;
      }
      if (lastRun.executionStatus === ExecutionStatus.Running) {
        const pastDate = subSeconds(
          new Date(),
          lastRun.secondsSinceStarted || 1
        );
        const timeAgo = formatDistance(pastDate, new Date(), {
          addSuffix: true,
        });
        setTimeSinceLastRun(`Started: ${timeAgo}`);
      } else if (
        lastRun.executionStatus === ExecutionStatus.Succeded ||
        lastRun.executionStatus === ExecutionStatus.Failed
      ) {
        const pastDate = subSeconds(
          new Date(),
          lastRun.secondsSinceCompletion || 1
        );
        const timeAgo = formatDistance(pastDate, new Date(), {
          addSuffix: true,
        });
        setTimeSinceLastRun(
          `${ExecutionStatus[lastRun.executionStatus]}: ${timeAgo}`
        );
      }
    });
  }, [getLastRunRulesOperation, matterId]);

  useEffect(() => {
    loadSecondsSinceRuleRunComplete();
  }, [loadSecondsSinceRuleRunComplete]);

  const loadParameters = useCallback(() => {
    setErrorMessage("");
    setLoading(true);

    getMatterParameters(matterId?.toString() || "").then(
      (parameters) => {
        setLoading(false);
        setMatterParameters(parameters);
      },
      (error) => {
        handleError(error, setErrorMessage, setLoading);
      }
    );
  }, [matterId, getMatterParameters]);

  useEffect(() => {
    if (!isNew && matterId) {
      setLoading(true);
      getMatterById(matterId).then(
        (matter) => {
          setLoading(false);
          setMatter(matter);
          loadParameters();
          refreshProgress();
        },
        (error) => {
          handleError(error, setErrorMessage, setLoading);
        }
      );
    }
  }, [
    isNew,
    matterId,
    getMatterById,
    navigate,
    refreshProgress,
    loadParameters,
  ]);

  const handleRefresh = async () => {
    refreshChecklist();
    loadParameters();
  };

  const handleRunRules = async () => {
    setIsRunningRules(true);
    const localBackgroundId = await runRules(matterId!);
    setBackgroundId(localBackgroundId);
    setIsTimerActive(true);
  };

  const handleRunRulesForced = async () => {
    setIsRunningRules(true);
    const localBackgroundId = await runRules(matterId!, true);
    setBackgroundId(localBackgroundId);
    setIsTimerActive(true);
  };

  const checkRunRuleProgress = useCallback(async () => {
    try {
      const executionStatus = await getBackgroundOperationStatus(backgroundId);
      if (executionStatus !== ExecutionStatus.Running) {
        setIsTimerActive(false);
        setIsRunningRules(false);
        loadSecondsSinceRuleRunComplete();
      }
    } catch (error) {
      return error;
    }
  }, [
    backgroundId,
    getBackgroundOperationStatus,
    loadSecondsSinceRuleRunComplete,
  ]);

  useEffect(() => {
    if (isTimerActive) {
      interval.current = setInterval(checkRunRuleProgress, 3000);
    } else {
      if (interval.current) {
        clearInterval(interval.current);
      }
      interval.current = null;
    }

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, [checkRunRuleProgress, isTimerActive]);

  return isNew || matter ? (
    <div className="container">
      <div className="d-flex flex-row mb-4">
        <div className="me-auto" style={{ marginLeft: "-12px" }}>
          <Link to="/admin" className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            Back to Dashboard
          </Link>
        </div>
        {!isNew && <MatterDetailsActions />}
      </div>
      <div className="row tablet-view">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-3">
            <div>
              <span>Matter</span>
              <h2>{matter?.name || "New"}</h2>
              {!isNew && (
                <>
                  <span>
                    {matter?.numberSignaturesRequired} due by{" "}
                    {formatDateString(matter?.dueDate?.toString())}
                  </span>
                  <Link role="button" to={`/admin/matters/${matterId}`}>
                    <FontAwesomeIcon icon={faPenToSquare} className="ms-2" />
                  </Link>
                </>
              )}
            </div>
            {!isNew && (
              <>
                <div className="mt-4 mb-4">
                  <MatterChecklist
                    checklist={checklist}
                    refreshChecklist={refreshChecklist}
                  />
                </div>
                <div>
                  <button
                    className="btn btn-light btn-outline"
                    onClick={handleRunRules}
                  >
                    {isRunningRules ? (
                      <InlineSpinner />
                    ) : (
                      <FontAwesomeIcon icon={faPlayCircle} className="me-2" />
                    )}
                    Run Rules
                  </button>
                </div>
                {!!timeSinceLastRun && (
                  <div className="mt-2">{timeSinceLastRun}</div>
                )}
              </>
            )}
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <MatterProgress
              progress={progress}
              isLoading={progressIsLoading}
              errorMessage={progressErrorMessage}
            />
            <MatterVoterStatus />

            <MatterUploadSignatures
              matter={matter}
              refreshChecklist={handleRefresh}
            />

            <MatterParameters
              refreshChecklist={handleRefresh}
              matterParameters={matterParameters}
            />
            <MatterUploadCirculators refreshChecklist={refreshChecklist} />
          </div>
        </div>
      </div>
    </div>
  ) : (
    <>{!loading && "This matter is invalid"}</>
  );
}
