import { faPenToSquare, faPlayCircle } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { formatDistance, subSeconds } from "date-fns";
import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { formatDateString, handleError } from "../../../common/utils";
import { InlineSpinner } from "../../../components/inline-spinner";
import MatterChecklist from "../../../components/matter-checklist.component";
import MatterDetailsActions from "../../../components/matter-details-actions";
import HeaderNavBar from "../../../components/header-nav-bar";
import MatterParameters from "../../../components/matter-parameters.component";
import MatterProgress from "../../../components/matter-progress.component";
import MatterUploadSignatures from "../../../components/matter-upload-signatures.component";
import MatterVoterStatus from "../../../components/matter-voter-status.component";
import MatterUploadCirculators from "../../../components/upload-circulators.component";
import { useMatterContext } from "../../../contexts/MatterContext";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { useChecklistWithRefresh } from "../../../hooks/useChecklistWithRefresh";
import { useProgressWithRefresh } from "../../../hooks/useProgressWithRefresh";
import { useMatterParametersService } from "../../../services/matter-parameters.service";
import { useRuleService } from "../../../services/rule.service";
import { ExecutionStatus } from "../../../types/background-operation.types";
import { MatterVariable } from "../../../types/matter.types";
import { Roles } from "../../../types/user.types";
import { Loader } from "../../../components/loader";

export default function MatterDetails() {
  useAuthGuard(Roles.Admin);
  const { matter, error: matterError } = useMatterContext();
  const { runRules, getLastRunRulesOperation, getBackgroundOperationStatus } = useRuleService();
  const { getMatterParameters } = useMatterParametersService();

  const [loading, setLoading] = useState(false);
  const [isRunningRules, setIsRunningRules] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { checklist, refreshChecklist } = useChecklistWithRefresh();
  const [matterParameters, setMatterParameters] = useState<MatterVariable[]>([]);

  const [backgroundId, setBackgroundId] = useState<number>(0);

  const [isTimerActive, setIsTimerActive] = useState(false);
  const interval = useRef<NodeJS.Timeout | null>(null);
  const [timeSinceLastRun, setTimeSinceLastRun] = useState<string>("");
  const [ruleRunCompletionTimeStamp, setRuleRunCompletionTimeStamp] = useState<Date | undefined>(undefined);

  const {
    progress,
    isLoading: isProgressLoading,
    errorMessage: progressErrorMessage,
    refreshProgress,
  } = useProgressWithRefresh();

  const params = useParams();
  const matterId = params?.matterId || "";
  const isNew = matterId === "new";

  const loadSecondsSinceRuleRunComplete = useCallback(() => {
    getLastRunRulesOperation(matterId).then((lastRun) => {
      if (!lastRun) {
        setTimeSinceLastRun("");
        return;
      }
      if (lastRun.executionStatus === ExecutionStatus.Running) {
        const pastDate = subSeconds(new Date(), lastRun.secondsSinceStarted || 1);
        const timeAgo = formatDistance(pastDate, new Date(), {
          addSuffix: true,
        });
        setTimeSinceLastRun(`Started: ${timeAgo}`);
      } else if (
        lastRun.executionStatus === ExecutionStatus.Succeded ||
        lastRun.executionStatus === ExecutionStatus.Failed
      ) {
        const pastDate = subSeconds(new Date(), lastRun.secondsSinceCompletion || 1);
        const timeAgo = formatDistance(pastDate, new Date(), {
          addSuffix: true,
        });
        setTimeSinceLastRun(`${ExecutionStatus[lastRun.executionStatus]}: ${timeAgo}`);
      }
    });
  }, [getLastRunRulesOperation, matterId]);

  useEffect(() => {
    loadSecondsSinceRuleRunComplete();
  }, [loadSecondsSinceRuleRunComplete]);

  const loadParameters = useCallback(() => {
    setErrorMessage("");
    setLoading(true);

    getMatterParameters(matterId?.toString() || "").then(
      (parameters) => {
        setLoading(false);
        setMatterParameters(parameters);
      },
      (error) => {
        handleError(error, setErrorMessage, setLoading);
      }
    );
  }, [matterId, getMatterParameters]);

  useEffect(() => {
    if (matterError) {
      setErrorMessage(matterError);
    }
  }, [matterError]);

  useEffect(() => {
    if (matter && matterId && matterId !== "new") {
      loadParameters();
      refreshProgress();
    }
  }, [matter, matterId, loadParameters, refreshProgress]);

  const handleRefresh = async () => {
    refreshChecklist();
    loadParameters();
  };

  const handleRunRules = async () => {
    setIsRunningRules(true);
    const localBackgroundId = await runRules(matterId!);
    setBackgroundId(localBackgroundId);
    setIsTimerActive(true);
  };

  const checkRunRuleProgress = useCallback(async () => {
    try {
      const executionStatus = await getBackgroundOperationStatus(backgroundId);
      if (executionStatus !== ExecutionStatus.Running) {
        setIsTimerActive(false);
        setIsRunningRules(false);
        setRuleRunCompletionTimeStamp(new Date());
        loadSecondsSinceRuleRunComplete();
        refreshProgress();
      }
    } catch (error) {
      return error;
    }
  }, [backgroundId, getBackgroundOperationStatus, loadSecondsSinceRuleRunComplete, refreshProgress]);

  useEffect(() => {
    if (isTimerActive) {
      interval.current = setInterval(checkRunRuleProgress, 3000);
    } else {
      if (interval.current) {
        clearInterval(interval.current);
      }
      interval.current = null;
    }

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, [checkRunRuleProgress, isTimerActive]);

  if (!isNew && (!matterId || !matter)) {
    return <Loader />;
  }

  return (
    <div className="container">
      <HeaderNavBar
        backTo="dashboard"
        shouldHideMatter={true}
        rightContent={!isNew ? <MatterDetailsActions ruleRunCompletionTimeStamp={ruleRunCompletionTimeStamp}/> : undefined}
      />
      <div className="row tablet-view">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-3">
            <div>
              <span>Matter</span>
              <h2>{matter?.name || "New"}</h2>
              {!isNew && (
                <>
                  <span>
                    {matter?.numberSignaturesRequired} due by {formatDateString(matter?.dueDate?.toString())}
                  </span>
                  <Link role="button" to={`/admin/matters/${matterId}`}>
                    <FontAwesomeIcon icon={faPenToSquare} className="ms-2" />
                  </Link>
                </>
              )}
            </div>
            {!isNew && (
              <>
                <div className="mt-4 mb-4">
                  <MatterChecklist checklist={checklist} refreshChecklist={refreshChecklist} />
                </div>
                <div>
                  <button className="btn btn-light btn-outline" onClick={handleRunRules}>
                    {isRunningRules ? <InlineSpinner /> : <FontAwesomeIcon icon={faPlayCircle} className="me-2" />}
                    Run Rules
                  </button>
                </div>
                {!!timeSinceLastRun && <div className="mt-2">{timeSinceLastRun}</div>}
              </>
            )}
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <MatterProgress progress={progress} isLoading={isProgressLoading} errorMessage={progressErrorMessage} />
            <MatterVoterStatus />

            <MatterUploadSignatures matter={matter} refreshChecklist={handleRefresh} />

            <MatterParameters refreshChecklist={handleRefresh} matterParameters={matterParameters} />
            <MatterUploadCirculators refreshChecklist={refreshChecklist} />
          </div>
        </div>
      </div>
    </div>
  );
}
