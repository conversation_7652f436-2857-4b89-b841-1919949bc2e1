import { useCallback, useEffect, useMemo, useState } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { useDeficiencyService } from "../../../services/deficiency.service";
import { DeficiencyView } from "../../../components/deficiency-view.component";
import Pagination from "../../../components/pagination-control";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { WorkDto } from "../../../types/work.types";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";

export default function DeficiencyReview() {
  useAuthGuard(Roles.Admin);

  const navigate = useNavigate();
  const location = useLocation();
  const { matterId, deficiencyId } = useParams();
  const [work, setWork] = useState<WorkDto | null>();
  const [backButtonText, setBackButtonText] =
    useState<string>("Back to Matter");
  const [backButtonUrl, setBackButtonUrl] = useState<string>(
    `/admin/matters/${matterId}/details`
  );

  const [deficiencyIds, setDeficiencyIds] = useState<number[] | null>(null);
  const { getReviewableDeficiencyIdsByMatter } = useDeficiencyService();

  useEffect(() => {
    document.documentElement.style.backgroundColor = "white";
    return () => {
      document.documentElement.style.backgroundColor = "";
    };
  }, []);

  useEffect(() => {
    let text = "Back to Matter";
    let url = `/admin/matters/${matterId}/details`;
    if (location.state?.from) {
      url = location.state?.from;
      if (location.state?.from.includes("deficiencies")) {
        text = "Back to Deficiencies";
      }
    }
    setBackButtonText(text);
    setBackButtonUrl(url);
  }, [location.state?.from, matterId]);

  useEffect(() => {
    if (!matterId) return;

    if (!deficiencyIds) {
      getReviewableDeficiencyIdsByMatter(matterId)
        .then((deficiencyIds) => {
          setDeficiencyIds(deficiencyIds);
          if (!deficiencyId && deficiencyIds.length > 0) {
            navigate(
              `/admin/matters/${matterId}/deficiencies/review/${deficiencyIds[0]}`
            );
          }
        })
        .catch(() => setDeficiencyIds([]));
    }
  }, [
    matterId,
    getReviewableDeficiencyIdsByMatter,
    deficiencyIds,
    deficiencyId,
    navigate,
  ]);

  const getNextDeficiencyId = useCallback(
    (deficiencyId: string) => {
      if (!deficiencyId || !deficiencyIds?.length) {
        return null;
      }
      const deficiencyNumber = parseInt(deficiencyId, 10);
      const currentIndex = deficiencyIds?.indexOf(deficiencyNumber);
      if (currentIndex >= 0 && currentIndex < deficiencyIds?.length - 1)
        return deficiencyIds?.[currentIndex + 1].toString();
      return null;
    },
    [deficiencyIds]
  );

  const handlePaginationNavigate = useCallback(
    (deficiencyId: number) => {
      navigate(
        `/admin/matters/${matterId}/deficiencies/review/${deficiencyId}`
      );
    },
    [matterId, navigate]
  );

  const handleFinish = useCallback(() => {
    navigate(backButtonUrl);
  }, [backButtonUrl, navigate]);

  const nextDeficiencyId = useMemo(() => {
    return deficiencyId ? getNextDeficiencyId(deficiencyId) : null;
  }, [deficiencyId, getNextDeficiencyId]);

  if (!matterId || !deficiencyIds) return null;

  return (
    <div className="container-fluid" style={{ padding: 0 }}>
      <div className="d-flex flex-row mb-4">
        <div className="me-auto">
          <Link
            to={backButtonUrl}
            className="btn btn-sw btn-sm btn-outline me-3"
          >
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            {backButtonText}
          </Link>
        </div>

        <div className="justify-content-center">
          <span>{work?.matterName}</span>
        </div>

        <div className="ms-auto">
          {deficiencyId && (
            <p>
              <Link
                to={`/admin/matters/${matterId}/sheets/${work?.sheetNumber}?deficiencyId=${deficiencyId}`}
                className="btn btn-sw btn-sm btn-outline"
              >
                Adjust Sheet
              </Link>
            </p>
          )}
        </div>
      </div>

      <div className="bg-sw-secondary">
        <Pagination
          sheetNumbers={deficiencyIds}
          onNavigate={handlePaginationNavigate}
        />
        {!!deficiencyId && (
          <DeficiencyView
            matterId={matterId}
            deficiencyId={deficiencyId}
            nextDeficiencyId={nextDeficiencyId}
            onNavigate={handlePaginationNavigate}
            onFinished={handleFinish}
            onWorkLoaded={setWork}
          />
        )}
      </div>
    </div>
  );
}
