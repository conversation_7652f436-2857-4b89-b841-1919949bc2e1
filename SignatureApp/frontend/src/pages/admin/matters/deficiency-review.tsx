import { useCallback, useEffect, useMemo, useState } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { DeficiencyView } from "../../../components/deficiency-view.component";
import Pagination from "../../../components/pagination-control";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { WorkDto } from "../../../types/work.types";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";
import HeaderNavBar from "../../../components/header-nav-bar";
import { useDeficiencyReviewService } from "../../../services/deficiency-review.service";
import { DeficiencyReview } from "../../../types/deficiency.types";

export default function ReviewDeficiency() {
  useAuthGuard(Roles.Admin);

  const navigate = useNavigate();
  const location = useLocation();
  const { matterId, deficiencyId } = useParams();
  const [work, setWork] = useState<WorkDto | null>();
  const [backButtonText, setBackButtonText] = useState<string>("Back to Matter");
  const [backButtonUrl, setBackButtonUrl] = useState<string>(`/admin/matters/${matterId}/details`);
  const [deficiencyReview, setDeficiencyReview] = useState<DeficiencyReview | null>();
  const { getNextReviewableDeficiencyByMatter } = useDeficiencyReviewService();

  useEffect(() => {
    document.documentElement.style.backgroundColor = "white";
    return () => {
      document.documentElement.style.backgroundColor = "";
    };
  }, []);

  useEffect(() => {
    let text = "Back to Matter";
    let url = `/admin/matters/${matterId}/details`;
    if (location.state?.from) {
      url = location.state?.from;
      if (location.state?.from.includes("deficiencies")) {
        text = "Back to Deficiencies";
      }
    }
    setBackButtonText(text);
    setBackButtonUrl(url);
  }, [location.state?.from, matterId]);

  const getNextDeficiencyReview = useCallback(() => {
    if (!matterId) {
      return;
    }
    getNextReviewableDeficiencyByMatter(matterId)
      .then((deficiencyReview) => {
        if (!deficiencyId && deficiencyReview) {
          navigate(`/admin/matters/${matterId}/deficiencies/review/${deficiencyReview.deficiencyId}`);
        } else {
          setDeficiencyReview(deficiencyReview);
        }
      })
      .catch(() => setDeficiencyReview(null));
  }, [deficiencyId, getNextReviewableDeficiencyByMatter, matterId, navigate]);

  useEffect(() => {
    if (!matterId) return;

    if (!deficiencyId) {
      getNextDeficiencyReview();
    }
  }, [deficiencyId, getNextDeficiencyReview, matterId]);

  const handlePaginationNavigate = useCallback(
    (deficiencyId: number) => {
      navigate(`/admin/matters/${matterId}/deficiencies/review/${deficiencyId}`);
    },
    [matterId, navigate]
  );

  const handleFinish = useCallback(() => {
    navigate(backButtonUrl);
  }, [backButtonUrl, navigate]);

  if (!matterId || !deficiencyId) return null;

  return (
    <div className="container-fluid bg-sw-secondary">
      <HeaderNavBar
        variant="container-fluid"
        leftContent={
          <Link to={backButtonUrl} className="btn btn-sw btn-sm btn-outline me-3">
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            {backButtonText}
          </Link>
        }
      />

      <div className="bg-sw-secondary pt-4">
        {/*
        <Pagination sheetNumbers={deficiencyIds} onNavigate={handlePaginationNavigate} />
        */}
        {!!deficiencyId && (
          <DeficiencyView
            matterId={matterId}
            deficiencyId={deficiencyId}
            getNextDeficiencyReview={getNextDeficiencyReview}
            onNavigate={handlePaginationNavigate}
            onFinished={handleFinish}
            onWorkLoaded={setWork}
          />
        )}
      </div>
    </div>
  );
}
