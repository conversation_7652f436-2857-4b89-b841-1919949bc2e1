import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { Link, useLocation, useParams, useSearchParams } from "react-router-dom";

import InvalidSheetsView from "../../../components/invalid-sheets-view.component";
import { useMatterInvalidSheets } from "../../../hooks/useMatterInvalidSheets";
import { Roles } from "../../../types/user.types";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { useEffect, useState } from "react";
import HeaderNavBar from "../../../components/header-nav-bar";

export default function MatterInvalidSheets() {
  useAuthGuard(Roles.Admin);

  const { matterId } = useParams();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [url, setUrl] = useState<string>();
  const [buttonText, setButtonText] = useState<string>();

  const { templateId, invalidFrontImage, invalidBackImage, currentInvalidSheet, setCurrentInvalidSheet } =
    useMatterInvalidSheets();

  useEffect(() => {
    document.documentElement.style.backgroundColor = "white";
    return () => {
      document.documentElement.style.backgroundColor = "";
    };
  }, []);

  useEffect(() => {
    const from = location.pathname.split("/")[1];
    const workId = searchParams.get("workId");
    let url = "";
    let buttonText = "";
    const deficiencyId = searchParams.get("deficiencyId");
    if (from === "admin") {
      url = `/admin/matters/${matterId}/`;
      if (deficiencyId) {
        url += `deficiencies/review/${deficiencyId}`;
        buttonText = "Back to Deficiency Review";
      } else if (workId) {
        url += `review/transcription/${workId}`;
        buttonText = "Back to Transcription Review";
      } else {
        url += `details`;
        buttonText = "Back to Matter";
      }
    } else if (from === "manager") {
      url = `/manager/${matterId}/flagged/`;
      buttonText = "Back to Transcription Review";
      if (workId) {
        url += workId;
      }
    }
    setUrl(url);
    setButtonText(buttonText);
  }, [location.pathname, matterId, searchParams]);

  return (
    <div className="container-fluid bg-sw-secondary">
      <HeaderNavBar
        variant="container-fluid"
        leftContent={
          url ? (
            <Link to={url} className="btn btn-sw btn-sm btn-outline">
              <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
              {buttonText}
            </Link>
          ) : undefined
        }
      />
      <div className="tablet-buffer bg-sw-secondary">
        <InvalidSheetsView
          currentInvalidSheet={currentInvalidSheet}
          setCurrentInvalidSheet={setCurrentInvalidSheet}
          invalidFrontImage={invalidFrontImage}
          invalidBackImage={invalidBackImage}
          templateId={templateId}
          matterId={matterId}
        />
      </div>
    </div>
  );
}
