import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useUploadService } from "../../../services/upload-signaturesheets.service";
import { UploadHistoryDTO } from "../../../types/signature-sheet.types";
import { Roles } from "../../../types/user.types";
import { InlineSpinner } from "../../../components/inline-spinner";
import { useAuthGuard } from "../../../hooks/useAuthGuard";

export default function SignatureSheetUploadHistory() {
  useAuthGuard(Roles.Admin);

  const { getUploadHistory, postRequeueUncompleted } = useUploadService();
  const { matterId } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [history, setHistory] = useState<UploadHistoryDTO[]>([]);

  useEffect(() => {
    // Fetch upload history when the page loads
    const fetchUploadHistory = async () => {
      if (!matterId) return;
      setIsLoading(true);
      try {
        const historyData: UploadHistoryDTO[] = await getUploadHistory(matterId);
        const history = historyData.map((result) => {
          const indexOf = result.uploadUrl.indexOf("?");
          return {
            ...result,
            uploadUrl: indexOf >= 0 ? result.uploadUrl.substring(0, indexOf) : result.uploadUrl,
          };
        });
        setHistory(history);
      } catch (error) {
        setErrorMessage("Failed to load upload history.");
        console.error("Error fetching upload history:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUploadHistory();
  }, [matterId, getUploadHistory]);

  async function handleRequeue() {
    if (!matterId) return;
    setIsLoading(true);
    try {
      await postRequeueUncompleted(matterId);
    } finally {
      setIsLoading(false);
    }
    navigate(`/admin/matters/${matterId}/details`);
  }

  return (
    <div className="container">
      <div className="d-flex flex-row mb-4">
        <div className="me-auto" style={{ marginLeft: "-12px" }}>
          <Link to={`/admin/matters/${matterId}/details`} className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            Back to Matter
          </Link>
        </div>
      </div>

      <div className="row tablet-view justify-content-start">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-2">
            <div className="mb-auto">
              <h2>Upload History</h2>
            </div>
          </div>
        </div>

        <div className="col-8 tablet-right">
          <div className="mt-3" style={{ maxHeight: 800, overflowY: "auto", overflowX: "auto" }}>
            {history?.length > 0 && (
              <table className="table table-borderless" style={{ tableLayout: "fixed", width: "100%" }}>
                <tbody>
                  {history?.map((result, index) => (
                    <tr key={index} className="card mb-3 border">
                      <td className="card-body" style={{ wordWrap: "break-word", maxWidth: "100%" }}>
                        <p className="card-text mb-0">
                          <strong>Upload URL: </strong>
                          <span
                            style={{
                              display: "inline-block",
                              whiteSpace: "pre-wrap",
                              wordBreak: "break-word",
                            }}
                          >
                            {result.uploadUrl}
                          </span>
                        </p>
                        <p className="card-text mb-0">
                          <strong>Uploaded By:</strong> {result?.uploadedBy || ""}
                          <strong>, Uploaded On:</strong> {result?.uploadedOn || ""}
                        </p>
                        <p className="card-text mb-0">
                          <strong>Template Name:</strong> {result?.templateName || ""}
                        </p>
                        <p className="card-text mb-0">
                          {`DownloadFiles: Downloaded ${result?.status.numFilesDownloaded} files.`}
                        </p>
                        <p className="card-text mb-0">
                          {`SplitAndMerge: ${result?.status.numFilesIntoSplitAndMerge} files in, ${result?.status.numFilesSplitOrMerged} files written.`}
                          <button className="ms-2 btn btn-sm btn-sw" onClick={handleRequeue} disabled={isLoading}>
                            {isLoading && <InlineSpinner />}
                            Requeue
                          </button>
                        </p>
                        <p className="card-text mb-0">
                          {`DocumentIntelligence: ${result?.status.numFilesIntoValidation} files submitted, ${result?.status.numFilesValid} valid, ${result?.status.numFilesInvalid} invalid, ${result?.status.numFilesDuplicated} duplicated.`}
                        </p>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
