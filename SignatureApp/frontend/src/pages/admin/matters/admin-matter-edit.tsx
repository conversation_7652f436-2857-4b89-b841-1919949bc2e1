import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useParams } from "react-router-dom";
import { formatDateString } from "../../../common/utils";
import { Roles } from "../../../types/user.types";
import MatterEdit from "../../../components/matter-edit.component";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import HeaderNavBar from "../../../components/header-nav-bar";
import { useMatterContext } from "../../../contexts/MatterContext";

export default function AdminMatterEdit() {
  useAuthGuard(Roles.Admin);
  const { matter, loading } = useMatterContext();

  const params = useParams();
  const matterId = params?.matterId;
  const isNew = matterId === "new";

  return isNew || matter ? (
    <div className="container">
      <HeaderNavBar />
      <div className="row tablet-view">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-3">
            <div>
              <span>Matter</span>
              <h2>{matter?.name || "New"}</h2>
              {!isNew && (
                <>
                  <span>
                    {matter?.numberSignaturesRequired} due by {formatDateString(matter?.dueDate?.toString())}
                  </span>
                  <a role="button" onClick={() => {}}>
                    <FontAwesomeIcon icon={faPenToSquare} className="ms-2" />
                  </a>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <MatterEdit matter={matter} />
          </div>
        </div>
      </div>
    </div>
  ) : (
    <>{!loading && "This matter is invalid"}</>
  );
}
