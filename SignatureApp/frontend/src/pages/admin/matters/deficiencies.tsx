import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { useAuthContext } from "../../../AuthProvider";
import { handleError, checkForRole } from "../../../common/utils";
import { useDeficiencyService } from "../../../services/deficiency.service";
import { useRuleService } from "../../../services/rule.service";
import { Deficiency, FullSheetDeficiency, RecordIdType } from "../../../types/deficiency.types";
import { RuleSummary } from "../../../types/rule.types";
import { Roles } from "../../../types/user.types";
import { ViolationCriterion } from "../../../types/work.types";
import { EditDeficienciesModal } from "../../../components/edit-deficiencies-modal";
import DeficienciesByRule from "../../../components/deficiencies-by-rule.component";
import DeficienciesBySheet from "../../../components/deficiencies-by-sheet.component";
import TabsSwitcher from "../../../components/sheets-tabs.component";
import { Tab } from "../../../types/signature-sheet.types";
import { useAuthGuard } from "../../../hooks/useAuthGuard";

export default function Deficiencies() {
  useAuthGuard(Roles.Admin);

  const navigate = useNavigate();
  const location = useLocation();
  const {
    getOnlyMatterDeficienciesById,
    getDeficienciesByRuleId,
    getFullSheetDeficienciesById,
    getDeficiencyCsv,
    getDeficiencyWord,
    getDeficiencyPdf,
    postMatterDeficiency,
    postSheetDeficiency,
    deleteMatterDeficiency,
    deleteSheetDeficiency,
  } = useDeficiencyService();
  const { getRules, getMatterViolations, getSheetViolations } = useRuleService();
  const params = useParams();
  const [sheetDeficiencies, setSheetDeficiencies] = useState<FullSheetDeficiency[]>([]);
  const [ruleDeficiencies, setRuleDeficiencies] = useState<Deficiency[]>([]);

  const [loadingSheetDeficiencies, setLoadingSheetDeficiencies] = useState(false);
  const [loadingRuleDeficiencies, setLoadingRuleDeficiencies] = useState(false);

  const [errorSheetDeficiencies, setErrorSheetDeficiencies] = useState("");
  const [errorRuleDeficiencies, setErrorRuleDeficiencies] = useState("");
  const [errorRules, setErrorRules] = useState("");
  const [errorExport, setErrorExport] = useState("");
  const [activeTab, setActiveTab] = useState<Tab>(Tab.RULE);

  const [sheetDeficienciesLoaded, setSheetDeficienciesLoaded] = useState(false);

  const [matterDeficiencies, setMatterDeficiencies] = useState<Deficiency[]>([]);
  const [rules, setRules] = useState<RuleSummary[]>([]);
  const csvLink = useRef<HTMLAnchorElement>(null);
  const [showMatterDeficiencyModal, setShowMatterDeficiencyModal] = useState(false);
  const [matterViolations, setMatterViolations] = useState<ViolationCriterion[]>([]);

  const [sheetViolations, setSheetViolations] = useState<ViolationCriterion[]>([]);

  const [errorMatterDeficiency, setErrorMatterDeficiency] = useState("");

  const matterId = params?.matterId;

  const loadRulesWithDeficiency = useCallback(() => {
    const getRulesStart = performance.now();
    getRules(matterId!).then(
      (r) => {
        const duration = performance.now() - getRulesStart;
        console.log(`getRules(${matterId}) took ${(duration / 1000).toFixed(2)}s`);
        const now = performance.now();
        setRules(r);
        console.log(`setRules took ${performance.now() - now}ms`);
      },
      (error) => {
        handleError(error, setErrorRules, undefined);
      }
    );
  }, [getRules, matterId]);

  const loadOnlyMatterDeficiencies = useCallback(() => {
    const perfGetOnlyMatterDeficienciesStart = performance.now();
    getOnlyMatterDeficienciesById(matterId!).then((data) => {
      console.log(
        `getOnlyMatterDeficienciesById(${matterId}) took ${
          (performance.now() - perfGetOnlyMatterDeficienciesStart) / 1000
        }s`
      );
      setMatterDeficiencies(data);
    });
  }, [getOnlyMatterDeficienciesById, matterId]);

  const loadFullSheetsDeficiencies = useCallback(() => {
    setErrorSheetDeficiencies("");
    setLoadingSheetDeficiencies(true);
    // Log the start time
    const perfGetFullSheetsStart = performance.now();
    getFullSheetDeficienciesById(matterId!).then(
      (deficiencies) => {
        const duration = performance.now() - perfGetFullSheetsStart;
        console.log(`getFullSheetDeficienciesById(${matterId}) took ${(duration / 1000).toFixed(2)}s`);
        setLoadingSheetDeficiencies(false);
        const now = performance.now();
        setSheetDeficiencies(deficiencies);
        console.log(`setSheetDeficiencies took ${performance.now() - now}ms`);
        setSheetDeficienciesLoaded(true);
        // Log the end time and calculate the duration
      },
      (error) => {
        handleError(error, setErrorSheetDeficiencies, setLoadingSheetDeficiencies);
      }
    );
  }, [getFullSheetDeficienciesById, matterId]);
  // Set active tab based on URL hash
  useEffect(() => {
    if (location.hash.substring(1) === "sheet") {
      setActiveTab(Tab.SHEET);
      loadFullSheetsDeficiencies();
    } else {
      setActiveTab(Tab.RULE);
    }
  }, [location, loadFullSheetsDeficiencies]);

  useEffect(() => {
    if (matterId) {
      setErrorRules("");
      loadRulesWithDeficiency();
      loadOnlyMatterDeficiencies();
      const perfGetSheetViolations = performance.now();
      getSheetViolations(matterId).then(
        (r) => {
          const duration = performance.now() - perfGetSheetViolations;
          console.log(`getSheetViolations(${matterId}) took ${(duration / 1000).toFixed(2)}s`);
          setSheetViolations(r);
        },
        (error) => {
          handleError(error, setErrorRules, undefined);
        }
      );
      const perfGetMatterViolations = performance.now();
      getMatterViolations(matterId).then(
        (r) => {
          const duration = performance.now() - perfGetMatterViolations;
          console.log(`getMatterViolations(${matterId}) took ${(duration / 1000).toFixed(2)}s`);
          setMatterViolations(r);
        },
        (error) => {
          handleError(error, setErrorRules, undefined);
        }
      );
    }
  }, [
    matterId,
    getOnlyMatterDeficienciesById,
    getFullSheetDeficienciesById,
    getRules,
    navigate,
    loadRulesWithDeficiency,
    getMatterViolations,
    loadOnlyMatterDeficiencies,
    getSheetViolations,
  ]);

  const loadRuleDeficiencies = (ruleId: string) => {
    if (ruleId === "") {
      setRuleDeficiencies([]);
      setActiveTab(Tab.SHEET);
      return;
    }
    setErrorRuleDeficiencies("");
    setLoadingRuleDeficiencies(true);
    setActiveTab(Tab.RULE);
    getDeficienciesByRuleId(matterId!, ruleId).then(
      (deficiencies) => {
        setRuleDeficiencies(deficiencies);
        setLoadingRuleDeficiencies(false);
      },
      (error) => {
        handleError(error, setErrorRuleDeficiencies, setLoadingRuleDeficiencies);
      }
    );
  };

  const exportDeficiencyCsvFile = () => {
    setErrorExport("");
    getDeficiencyCsv(matterId!).then(
      (csv) => {
        csvLink.current?.setAttribute("download", `Matter${matterId}Deficiencies.csv`);
        csvLink.current?.setAttribute("href", URL.createObjectURL(new Blob([csv], { type: "text/csv" })));
        csvLink.current?.click();
      },
      (error) => {
        handleError(error, setErrorExport);
      }
    );
  };

  const exportDeficiencyWordFile = async () => {
    try {
      setErrorExport("");

      const result = await getDeficiencyWord(matterId!);
      const url = window.URL.createObjectURL(result);
      const a = document.createElement("a");
      a.href = url;
      a.download = `DeficienciesBySheet_${matterId}.docx`;
      a.click();

      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error exporting deficiency Word document:", error);
      setErrorExport("An error occurred while exporting the Word document.");
    }
  };

  const exportDeficiencyPdfFile = async () => {
    try {
      setErrorExport("");

      const result = await getDeficiencyPdf(matterId!);
      const url = window.URL.createObjectURL(result);
      const a = document.createElement("a");
      a.href = url;
      a.download = `DeficienciesBySheet_${matterId}.pdf`;
      a.click();

      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error exporting deficiency Pdf document:", error);
      setErrorExport("An error occurred while exporting the Pdf document.");
    }
  };

  const handleAddMatterDeficiency = (violationId: number, violationNote: string | null) => {
    setErrorMatterDeficiency("");
    postMatterDeficiency(matterId!, violationId, violationNote).then(
      (deficiency) => {
        loadOnlyMatterDeficiencies();
        loadRulesWithDeficiency();
        if (activeTab === Tab.SHEET) {
          loadFullSheetsDeficiencies();
        }
      },
      (error) => {
        handleError(error, setErrorMatterDeficiency);
      }
    );
  };

  const handleAddSheetDeficiency = (sheetId: number, violationId: number, violationNote: string | null) => {
    setErrorMatterDeficiency("");
    postSheetDeficiency(sheetId, violationId, violationNote).then(
      () => {
        loadRulesWithDeficiency();
        loadFullSheetsDeficiencies();
      },
      (error) => {
        handleError(error, setErrorMatterDeficiency);
      }
    );
  };

  const handleDeleteMatterDeficiency = (ruleId: number) => {
    setErrorMatterDeficiency("");
    deleteMatterDeficiency(matterId!, ruleId).then(
      (data) => {
        loadOnlyMatterDeficiencies();
        if (activeTab === Tab.SHEET) {
          loadFullSheetsDeficiencies();
        }
      },
      (error) => {
        handleError(error, setErrorMatterDeficiency);
      }
    );
  };

  const handleDeleteSheetDeficiency = (sheetId: number, violationId: number) => {
    setErrorMatterDeficiency("");
    deleteSheetDeficiency(sheetId, violationId).then(
      (data) => {
        loadRulesWithDeficiency();
        loadFullSheetsDeficiencies();
      },
      (error) => {
        handleError(error, setErrorMatterDeficiency);
      }
    );
  };

  return (
    <div className="container-fluid bg-sw-secondary">
      <div className="row bg-sw-primary">
        <div className="sw-buffer">
          <div className="d-flex flex-row mb-4">
            <div className="me-auto">
              <Link to={`/admin/matters/${matterId}/details`} className="btn btn-sw btn-sm btn-outline">
                <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
                Back to Matter
              </Link>
            </div>

            <div>Matter Name</div>

            <div className="ms-auto" style={{ marginRight: "30px" }}>
              <div className="dropdown">
                <button
                  className="btn btn-sw btn-sm btn-outline dropdown-toggle"
                  type="button"
                  id="dropdownMenuButton"
                  data-bs-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  Actions
                </button>
                <div
                  className="dropdown-menu dropdown-menu-end"
                  aria-labelledby="dropdownMenuButton"
                  data-bs-popper="true" // Enable dynamic positioning using Popper.js
                >
                  <button type="button" onClick={exportDeficiencyCsvFile} className="dropdown-item">
                    Export CSV
                  </button>
                  <a ref={csvLink} style={{ display: "none" }} target="_blank"></a>

                  <button type="button" onClick={exportDeficiencyWordFile} className="dropdown-item">
                    Export Word
                  </button>
                  <button type="button" onClick={exportDeficiencyPdfFile} className="dropdown-item">
                    Export Pdf
                  </button>

                  <button type="button" onClick={() => setShowMatterDeficiencyModal(true)} className="dropdown-item">
                    Matter Deficiency
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="tablet-buffer bg-sw-secondary">
        <div className="row">
          <div className="col-sm-6">
            <div className="form-group">
              <TabsSwitcher
                name="deficiency-category"
                activeTab={activeTab}
                switchTabs={(tab) => {
                  navigate(tab);
                  if (!sheetDeficienciesLoaded) {
                    loadFullSheetsDeficiencies();
                  }
                }}
                options={[
                  { label: "By Rule", value: "" },
                  { label: "By Sheet", value: "#sheet" },
                ]}
              />
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col">
            <div className="form-group">
              {errorExport && (
                <div className="alert alert-danger" role="alert">
                  {errorExport}
                </div>
              )}
            </div>
          </div>
        </div>

        {activeTab === Tab.RULE ? (
          <DeficienciesByRule
            rules={rules}
            errorRules={errorRules}
            loadRuleDeficiencies={loadRuleDeficiencies}
            loadingRuleDeficiencies={loadingRuleDeficiencies}
            errorRuleDeficiencies={errorRuleDeficiencies}
            ruleDeficiencies={ruleDeficiencies}
          />
        ) : (
          <DeficienciesBySheet
            matterId={matterId || ""}
            loadingSheetDeficiencies={loadingSheetDeficiencies}
            errorSheetDeficiencies={errorSheetDeficiencies}
            sheetDeficiencies={sheetDeficiencies}
            sheetViolations={sheetViolations}
            addSheetDeficiency={handleAddSheetDeficiency}
            deleteSheetDeficiency={handleDeleteSheetDeficiency}
          />
        )}

        <EditDeficienciesModal
          isOpen={showMatterDeficiencyModal}
          deficiencies={matterDeficiencies.map((def) => ({
            description: def.ruleName,
            ruleId: def.ruleId,
          }))}
          type={RecordIdType.Matter}
          close={() => {
            setShowMatterDeficiencyModal(false);
          }}
          addDeficiency={(form) => {
            handleAddMatterDeficiency(form.violationId as number, form.violationNote);
          }}
          deleteDeficiency={handleDeleteMatterDeficiency}
          errorMessage={errorMatterDeficiency}
          violationCriteria={matterViolations}
        />
      </div>
    </div>
  );
}
