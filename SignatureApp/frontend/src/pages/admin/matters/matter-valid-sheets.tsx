import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { useState } from "react";
import { Link, useLocation, useSearchParams } from "react-router-dom";

import ValidSheetsView from "../../../components/valid-sheets-view.component";
import { useMatterSheets } from "../../../hooks/useMatterSheets";
import DeficienciesModalContainer from "../../../components/deficiencies-modal-container";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";

export default function MatterValidSheets() {
  useAuthGuard(Roles.Admin);

  const location = useLocation();
  const [searchParams] = useSearchParams();

  const {
    matterId,
    sheetNumber,
    sheetHasUnavailableWork,
    showNoSheets,
    sheetNumbers,
    tableData,
    setTableData,
    postAdjustedSheet,
  } = useMatterSheets();

  const [showMatterDeficiencyModal, setShowMatterDeficiencyModal] =
    useState(false);

  const navButton = () => {
    const from = location.pathname.split("/")[1];
    const workId = searchParams.get("workId");
    let url = "";
    let buttonText = "";
    const deficiencyId = searchParams.get("deficiencyId");
    if (from === "admin") {
      url = `/admin/matters/${matterId}/`;
      if (deficiencyId) {
        url += `deficiencies/review/${deficiencyId}`;
        buttonText = "Back to Deficiency Review";
      } else if (workId) {
        url += `review/transcription/${workId}`;
        buttonText = "Back to Transcription Review";
      } else {
        url += `details`;
        buttonText = "Back to Matter";
      }
    } else if (from === "manager") {
      url = `/manager/${matterId}/flagged/`;
      buttonText = "Back to Transcription Review";
      if (workId) {
        url += workId;
      }
    }
    return (
      <Link to={url} className="btn btn-sw btn-sm btn-outline">
        <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
        {buttonText}
      </Link>
    );
  };

  return (
    <div className="container-fluid bg-sw-secondary">
      <div className="row bg-sw-primary ps-4 pe-4">
        <div className="col align-self-start mb-4">{navButton()}</div>
        <div className="col align-self-end text-end mb-4 pe-0">
          <div id="menu-items" className="d-flex gap-2 justify-content-end">
            {sheetHasUnavailableWork && (
              <Link
                to={`/admin/matters/${matterId}/release/${sheetNumber}`}
                className="btn btn-sw btn-sm btn-outline"
              >
                Release Work
              </Link>
            )}
            <div className="dropdown">
              <button
                className="btn btn-sw btn-sm btn-outline dropdown-toggle"
                type="button"
                id="dropdownMenuButton"
                data-bs-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                Actions
              </button>
              <div
                className="dropdown-menu"
                aria-labelledby="dropdownMenuButton"
              >
                <button
                  onClick={() => setShowMatterDeficiencyModal(true)}
                  className="dropdown-item"
                >
                  Add Sheet Deficiency
                </button>
                {!sheetHasUnavailableWork && (
                  <Link
                    to={`/admin/matters/${matterId}/preview/${sheetNumber}/row/1`}
                    className="dropdown-item"
                  >
                    Preview Signature Table Work
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="tablet-buffer bg-sw-secondary pt-0">
        {!!sheetNumbers ? (
          <ValidSheetsView
            matterId={matterId}
            sheetNumber={sheetNumber}
            showNoSheets={showNoSheets}
            onSaveAdjustedSheet={postAdjustedSheet}
            tableData={tableData}
            setTableData={setTableData}
            sheetNumbers={sheetNumbers}
          />
        ) : (
          <span className="spinner-boarder spinner-boarder-sm me-1" />
        )}
        <DeficienciesModalContainer
          showDeficienciesModal={showMatterDeficiencyModal}
          setShowDeficienciesModal={setShowMatterDeficiencyModal}
          signatureSheetId={tableData?.signatureSheetId}
        />
      </div>
    </div>
  );
}
