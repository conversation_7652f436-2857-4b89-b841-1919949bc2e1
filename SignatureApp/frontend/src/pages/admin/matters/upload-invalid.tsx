import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import UploadFileWithHeaders from "../../../components/upload-file-with-headers.component";
import { useRuleService } from "../../../services/rule.service";
import { ExecutionStatus } from "../../../types/background-operation.types";
import { RuleContextType } from "../../../types/rule.types";
import { ViolationCriterion } from "../../../types/work.types";
import Select, { SingleValue } from "react-select";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";
import HeaderNavBar from "../../../components/header-nav-bar";

export default function UploadInvalid() {
  useAuthGuard(Roles.Admin);

  const { matterId } = useParams();

  const [voidingType, setVoidingType] = useState("");
  const [csvHeader, setCsvHeader] = useState<string[]>([]);
  const [csvRows, setCsvRows] = useState<string[][]>([]);
  const [filePath, setFilePath] = useState<string>("");
  const [additionalData] = useState<{ [key: string]: string }>({});
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [possibleHeaderChoices, setPossibleHeaderChoices] = useState<string[]>([]);
  const [headerChoices, setHeaderChoices] = useState<string[][]>([]);
  const [ruleChoices, setRuleChoices] = useState<ViolationCriterion[]>([]);
  const [selectedRule, setSelectedRule] = useState<ViolationCriterion | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingLog, setProcessingLog] = useState<string>();
  const [backgroundId, setBackgroundId] = useState<number>();
  const [processingIndex, setProcessingIndex] = useState<number | null>(null);

  const { getBackgroundOperationStatus, deleteBackgroundOperation } = useRuleService();
  const interval = useRef<NodeJS.Timeout | null>(null);

  const { getNonTaskByType } = useRuleService();

  const possibleCirculatorHeaderChoices: string[] = [
    "",
    "CirculatorId",
    "CirculatorName",
    "CandidateOrBallotMeasure",
    "CompanyWorkingFor",
    "OutOfState",
    "Paid ",
    "DateRegistered",
    "DateUnregistered",
    "Jurisdiction ",
  ];
  const possibleVoterHeaderChoices: string[] = [
    "",
    "VoterId",
    "Status",
    "LastName",
    "FirstName",
    "MiddleName",
    "StreetNumber",
    "Direction",
    "StreetName",
    "StreetType",
    "StreetAddress",
    "ApartmentNumber",
    "City",
    "ZipCode",
    "RegistrationDate",
    "Party",
    "BirthYear",
    "FedIdOnly",
    "County",
    "CongressionalDistrict",
    "LegislativeDistrict",
  ];

  useEffect(() => {
    document.documentElement.style.backgroundColor = "white";
    return () => {
      document.documentElement.style.backgroundColor = "";
    };
  }, []);

  function initializeSelectedValues(headerFields: string[]) {
    const currentSelectedValues = new Array(headerFields.length).fill("");
    let index = 0;
    for (const header of headerFields) {
      const headerNoSpaces = header.replaceAll(" ", "").toLowerCase();

      var headerChoice = possibleHeaderChoices.find((s) => s.replaceAll(" ", "").toLowerCase() === headerNoSpaces);
      if (headerChoice) {
        currentSelectedValues[index] = headerChoice;
        additionalData[headerChoice] = header;
      }
      index++;
    }
    return currentSelectedValues;
  }

  function initializeHeaderChoices(headerFields: string[]) {
    const choices = new Array(headerFields.length);
    for (let index = 0; index < headerFields.length; index++) {
      choices[index] = [...possibleHeaderChoices];
    }
    return choices;
  }

  function handleSetHeaderFields(headerFields: string[]) {
    const selectedValues = initializeSelectedValues(headerFields);
    setSelectedValues(selectedValues);

    const currentHeaderChoices = initializeHeaderChoices(headerFields);
    removeSelectedChoiceFromUnchosenSelects(selectedValues, currentHeaderChoices);
    setCsvHeader(headerFields);
  }

  useEffect(() => {
    additionalData["matterId"] = matterId || "";
  }, [additionalData, matterId]);

  const handleVoidingTypeChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const type = e.target.value;
    setVoidingType(type);
    setPossibleHeaderChoices(type === "Voters" ? possibleVoterHeaderChoices : possibleCirculatorHeaderChoices);
    const result = await getNonTaskByType(
      matterId || "",
      type === "Voters" ? RuleContextType.SignatureRow : RuleContextType.Circulator
    );
    setRuleChoices(result);
    setSelectedRule(null);
    setSelectedValues([]);
    setCsvHeader([]);
    setFilePath("");
  };

  const uploadUrlPath = useMemo(() => {
    return `upload/${matterId}/invalid${voidingType}?ruleId=${selectedRule?.ruleId}`.toLowerCase();
  }, [matterId, selectedRule, voidingType]);

  const handleRuleChange = (newRule: SingleValue<ViolationCriterion>) => {
    setSelectedRule(newRule);
  };

  const handleHeaderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const headerIndex = csvHeader.findIndex((s) => s === e.target.name);
    let currentSelectedValues = [...selectedValues];
    if (headerIndex >= 0) {
      currentSelectedValues[headerIndex] = e.target.value;
      setSelectedValues(currentSelectedValues);
    }

    removeSelectedChoiceFromUnchosenSelects(currentSelectedValues, [...headerChoices]);
    additionalData[e.target.value] = e.target.name;
  };

  const handleSetCsvRows = (rows: string[][]) => {
    setCsvRows(rows);
  };

  function removeSelectedChoiceFromUnchosenSelects(currentSelectedValues: string[], currentHeaderChoices: string[][]) {
    const nonBlankSelectedValues = currentSelectedValues.filter((sv) => sv && sv.length);
    const remainingChoices = possibleHeaderChoices.filter((x) => !nonBlankSelectedValues.includes(x));

    let index = 0;
    for (const selectedValue of currentSelectedValues) {
      if (selectedValue.length === 0) {
        currentHeaderChoices[index] = remainingChoices;
      }
      index++;
    }
    setHeaderChoices(currentHeaderChoices);
  }

  const handleUploadComplete = (id: number) => {
    setBackgroundId(id);
    setIsProcessing(true);
  };

  const handleUploadError = (msg: string) => {
    setProcessingLog(msg);
  };

  const checkUploadProgress = useCallback(async () => {
    if (!backgroundId) {
      return;
    }
    try {
      const executionStatus = await getBackgroundOperationStatus(backgroundId);
      if (executionStatus !== ExecutionStatus.Running) {
        setIsProcessing(false);
      }
      if (executionStatus === ExecutionStatus.Succeded) {
        const success = await deleteBackgroundOperation(backgroundId);
      }
    } catch (error) {
      return error;
    }
  }, [backgroundId, deleteBackgroundOperation, getBackgroundOperationStatus]);

  useEffect(() => {
    if (backgroundId) {
      checkUploadProgress();
    }
  }, [backgroundId, checkUploadProgress]);

  useEffect(() => {
    if (isProcessing) {
      interval.current = setInterval(checkUploadProgress, 2000);
    } else {
      if (interval.current) {
        clearInterval(interval.current);
      }
      interval.current = null;
    }

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, [checkUploadProgress, isProcessing]);

  return (
    <div className="container-fluid bg-sw-secondary">
      <HeaderNavBar backTo="dashboard" variant="container-fluid" />

      <h3>Upload Invalid {voidingType ?? ""}</h3>
      <div className="container">
        <div className="row gy-2 mb-2">
          <div className="col-sm-12">
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="radio"
                name="radioCirculatorVoter"
                id="radioCirculator"
                onChange={handleVoidingTypeChange}
                value="Circulators"
              />
              <label className="form-check-label" htmlFor="radioCirculator">
                Circulators
              </label>
            </div>
            <div className="form-check form-check-inline">
              <input
                className="form-check-input"
                type="radio"
                name="radioCirculatorVoter"
                id="radioVoter"
                onChange={handleVoidingTypeChange}
                value="Voters"
              />
              <label className="form-check-label" htmlFor="radioVoter">
                Voters
              </label>
            </div>
          </div>
        </div>
        <div className="row gy-2 mb-2">
          <Select
            name="rule"
            onChange={handleRuleChange}
            value={selectedRule}
            options={ruleChoices}
            getOptionLabel={(vc: ViolationCriterion) => vc.name}
            getOptionValue={(vc: ViolationCriterion) => vc.ruleId.toString()}
          />
        </div>
        <UploadFileWithHeaders
          filePath={filePath}
          possibleHeaderChoices={possibleHeaderChoices}
          urlPath={uploadUrlPath}
          urlIndex={0}
          isValid={selectedRule != null && voidingType !== ""}
          additionalData={additionalData}
          isCsvHeaderSet={csvHeader?.length > 0}
          onSetRows={handleSetCsvRows}
          onSetHeaderFields={handleSetHeaderFields}
          onUploadComplete={handleUploadComplete}
          onUploadError={handleUploadError}
          isProcessing={isProcessing}
          processingLog={processingLog}
          processingIndex={processingIndex}
        />
        {csvHeader?.length > 0 && (
          <table className="excel">
            <thead>
              <tr>
                <th></th>
                {csvHeader.map((h, i) => (
                  <th key={i}>
                    <select name={h} onChange={handleHeaderChange} value={selectedValues[i]}>
                      {headerChoices[i].map((c, i) => (
                        <option key={i.toString() + c} value={c}>
                          {c}
                        </option>
                      ))}
                    </select>
                  </th>
                ))}
              </tr>
              <tr>
                <th></th>
                {csvHeader.map((h) => (
                  <th key={h}>{h}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {csvRows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  <td>{rowIndex + 1}</td>
                  {row.map((v, i) => (
                    <td key={rowIndex * 100 + i}>{v}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}
