import { useCallback, useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import UploadFileWithHeaders from "../../../components/upload-file-with-headers.component";
import { useDataProcessingLogsService } from "../../../services/data-processing-logs.service";
import { filter, isEqual, sortBy } from "lodash";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";
import HeaderNavBar from "../../../components/header-nav-bar";

export default function UploadCirculatorInfo() {
  useAuthGuard(Roles.Admin);

  const { getExternalDataLogsByPartId } = useDataProcessingLogsService();
  const { matterId } = useParams();

  const [csvHeader, setCsvHeader] = useState<string[]>([]);
  const [csvRows, setCsvRows] = useState<string[][]>([]);

  const [additionalData] = useState<{ [key: string]: string }>({});
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [headerChoices, setHeaderChoices] = useState<string[][]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingLog, setProcessingLog] = useState<string>();
  const [backgroundId, setBackgroundId] = useState<number>();
  const [processingIndex, setProcessingIndex] = useState<number | null>(null);

  const interval = useRef<NodeJS.Timeout | null>(null);

  const possibleHeaderChoices: string[] = [
    "",
    "CirculatorId",
    "CirculatorName",
    "CandidateOrBallotMeasure",
    "CompanyWorkingFor",
    "OutOfState",
    "Paid ",
    "DateRegistered",
    "DateUnregistered",
    "Jurisdiction ",
  ];

  useEffect(() => {
    document.documentElement.style.backgroundColor = "white";
    return () => {
      document.documentElement.style.backgroundColor = "";
    };
  }, []);

  useEffect(() => {
    additionalData["matterId"] = matterId || "";
  }, [additionalData, matterId]);

  function initializeSelectedValues(headerFields: string[]) {
    const currentSelectedValues = new Array(headerFields.length).fill("");
    let index = 0;
    for (const header of headerFields) {
      const headerNoSpaces = header.replaceAll(" ", "").toLowerCase();

      var headerChoice = possibleHeaderChoices.find((s) => s.replaceAll(" ", "").toLowerCase() === headerNoSpaces);
      if (headerChoice) {
        currentSelectedValues[index] = headerChoice;
        additionalData[headerChoice] = header;
      }
      index++;
    }
    return currentSelectedValues;
  }

  function initializeHeaderChoices(headerFields: string[]) {
    const choices = new Array(headerFields.length);
    //console.log(`possibleHeaderChoices`, possibleHeaderChoices);
    for (let index = 0; index < headerFields.length; index++) {
      choices[index] = [...possibleHeaderChoices];
    }
    // console.log(`initialized header choices`, choices);
    return choices;
  }

  function handleSetHeaderFields(headerFields: string[]) {
    // console.log(`headerfields`, headerFields);
    const selectedValues = initializeSelectedValues(headerFields);
    setSelectedValues(selectedValues);

    const currentHeaderChoices = initializeHeaderChoices(headerFields);
    removeSelectedChoiceFromUnchosenSelects(selectedValues, currentHeaderChoices);
    setCsvHeader(headerFields);
  }

  const handleHeaderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const headerIndex = csvHeader.findIndex((s) => s === e.target.name);
    let currentSelectedValues = [...selectedValues];
    if (headerIndex >= 0) {
      currentSelectedValues[headerIndex] = e.target.value;
      setSelectedValues(currentSelectedValues);
    }

    removeSelectedChoiceFromUnchosenSelects(currentSelectedValues, [...headerChoices]);
    additionalData[e.target.value] = e.target.name;
  };

  const handleSetCsvRows = (rows: string[][]) => {
    setCsvRows(rows);
  };

  function removeSelectedChoiceFromUnchosenSelects(currentSelectedValues: string[], currentHeaderChoices: string[][]) {
    const nonBlankSelectedValues = currentSelectedValues.filter((sv) => sv && sv.length);
    const remainingChoices = possibleHeaderChoices.filter((x) => !nonBlankSelectedValues.includes(x));
    // console.log(`removing selected choice from unchosen`, nonBlankSelectedValues, currentHeaderChoices, remainingChoices);

    let index = 0;
    for (const selectedValue of currentSelectedValues) {
      if (selectedValue.length === 0) {
        currentHeaderChoices[index] = remainingChoices;
      }
      index++;
    }
    // console.log(`new header choices`, currentHeaderChoices);
    setHeaderChoices(currentHeaderChoices);
  }

  const handleUploadComplete = (id: number) => {
    setBackgroundId(id);
    setIsProcessing(true);
  };

  const handleUploadError = (msg: string) => {
    setProcessingLog(msg);
    setProcessingIndex(null);
  };

  const checkUploadProgress = useCallback(async () => {
    if (!backgroundId) {
      return;
    }

    const logs = await getExternalDataLogsByPartId(backgroundId.toString());
    if (logs && logs.length) {
      const lastLog = logs[logs.length - 1];
      if (lastLog.operation.includes("finished")) {
        setIsProcessing(false);
      }
      setProcessingLog(lastLog.operation);
    }
  }, [backgroundId, getExternalDataLogsByPartId]);

  const checkSelectedHeaders = () => {
    const choices = filter(sortBy(possibleHeaderChoices), (v) => v.length > 0);
    const selected = filter(sortBy(selectedValues), (v) => v.length > 0);
    return isEqual(choices, selected);
  };

  useEffect(() => {
    if (isProcessing) {
      interval.current = setInterval(checkUploadProgress, 2000);
    } else {
      if (interval.current) {
        clearInterval(interval.current);
      }
      interval.current = null;
    }

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, [checkUploadProgress, isProcessing]);

  return (
    <div className="container-fluid bg-sw-secondary">
      <HeaderNavBar variant="container-fluid" backTo="details" />
      <h3>Upload Circulator Info Files</h3>
      <div className="container">
        <UploadFileWithHeaders
          possibleHeaderChoices={possibleHeaderChoices}
          urlPath={`upload/externaldata/circulators?matterId=${matterId}`}
          isValid={checkSelectedHeaders()}
          additionalData={additionalData}
          isCsvHeaderSet={csvHeader?.length > 0}
          onSetRows={handleSetCsvRows}
          onSetHeaderFields={handleSetHeaderFields}
          onUploadComplete={handleUploadComplete}
          onUploadError={handleUploadError}
          isProcessing={isProcessing}
          processingLog={processingLog}
          processingIndex={processingIndex}
        />
        {csvHeader?.length > 0 && (
          <table className="excel">
            <thead>
              <tr>
                <th></th>
                {csvHeader.map((h, i) => (
                  <th key={i}>
                    <select name={h} onChange={handleHeaderChange} value={selectedValues[i]}>
                      {headerChoices[i].map((c, i) => (
                        <option key={i.toString() + c} value={c}>
                          {c}
                        </option>
                      ))}
                    </select>
                  </th>
                ))}
              </tr>
              <tr>
                <th></th>
                {csvHeader.map((h) => (
                  <th key={h}>{h}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {csvRows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  <td>{rowIndex + 1}</td>
                  {row.map((v, i) => (
                    <td key={rowIndex * 100 + i}>{v}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}
