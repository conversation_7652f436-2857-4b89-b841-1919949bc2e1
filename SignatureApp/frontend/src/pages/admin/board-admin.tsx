import AdminMatters from "../../components/board-admin-matters.component";
import AdminTemplates from "../../components/board-admin-templates.component";
import AdminDataFiles from "../../components/board-admin-datafiles.component";
import { UploadType } from "../../types/external-data.types";
import { Roles } from "../../types/user.types";
import { useAuthGuard } from "../../hooks/useAuthGuard";
import HeaderNavBar from "../../components/header-nav-bar";
import HeaderActionsAdmin from "./header-actions-admin";

export default function AdminBoard() {
  useAuthGuard(Roles.Admin);

  return (
    <div className="container">
      <HeaderNavBar backTo="manager" contextType="admin-dashboard" rightContent= {<HeaderActionsAdmin/>} />
      <div className="row tablet-view justify-content-start">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-2">
            <div className="mb-auto">
              <span>Administrator</span>
              <h2>Home</h2>
            </div>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <AdminMatters />
            <AdminTemplates />
            <AdminDataFiles uploadType={UploadType.Voter} />
          </div>
        </div>
      </div>
    </div>
  );
}
