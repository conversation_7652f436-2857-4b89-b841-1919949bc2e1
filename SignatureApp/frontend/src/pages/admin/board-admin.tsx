import { Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowUpRightFromSquare } from "@fortawesome/free-solid-svg-icons";
import AdminMatters from "../../components/board-admin-matters.component";
import AdminTemplates from "../../components/board-admin-templates.component";
import AdminDataFiles from "../../components/board-admin-datafiles.component";
import { UploadType } from "../../types/external-data.types";
import { Roles } from "../../types/user.types";
import { useAuthGuard } from "../../hooks/useAuthGuard";

export default function AdminBoard() {
  useAuthGuard(Roles.Admin);

  return (
    <div className="container">
      <div className="row mb-4">
        <div className="col-12" style={{ marginLeft: "-12px" }}>
          <Link to="/manager" className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={faArrowUpRightFromSquare} className="me-2" />
            Manager Dashboard
          </Link>
        </div>
      </div>
      <div className="row tablet-view justify-content-start">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-2">
            <div className="mb-auto">
              <span>Administrator</span>
              <h2>Home</h2>
            </div>
            <div className="row">
              <div className="col-6">
                <Link to="/admin/members" className="btn btn-light btn-outline">
                  Team Members
                </Link>
              </div>
              <div className="col-6">
                <Link to="/admin/states" className="btn btn-light btn-outline">
                  Reference Data
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <AdminMatters />
            <AdminTemplates />
            <AdminDataFiles uploadType={UploadType.Voter} />
          </div>
        </div>
      </div>
    </div>
  );
}
