import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useFormik } from "formik";
import { Link, useNavigate, useParams } from "react-router-dom";
import { Fragment, useEffect, useMemo, useRef, useState } from "react";
import { checkForRole } from "../../../common/utils";
import { Roles } from "../../../types/user.types";
import { useTemplateService } from "../../../services/template.service";
import { Template, TemplateIgnoredWordsDTO } from "../../../types/template.types";
import { Loader } from "../../../components/loader";
import { TemplateIgnoredWordsColumn } from "../../../components/template/template-ignored-words-column";
import { TemplateIgnoredWordRow } from "../../../components/template/template-ignored-word-row";
import { useAuthGuard } from "../../../hooks/useAuthGuard";

type FormValues = {
  word: string;
};

export type NewWord = {
  id: number;
  word: string;
};

export default function TemplateIgnoredWords() {
  useAuthGuard(Roles.Admin);

  const navigate = useNavigate();
  const { templateId } = useParams();
  const { getTemplateById, updateTemplateIgnoredWords, getIgnoredWordsByTemplateId } = useTemplateService();

  const formik = useFormik<FormValues>({
    initialValues: {
      word: "",
    },
    onSubmit: handleAddWord,
  });

  const [isLoading, setIsLoading] = useState(true);
  const [template, setTemplate] = useState<Template | null>(null);
  const [errorMessage, setErrorMessage] = useState("");
  const [ignoredWords, setIgnoredWords] = useState<TemplateIgnoredWordsDTO[]>([]);

  const [newWords, setNewWords] = useState<NewWord[]>([]);
  const [wordIdsToDelete, setWordIdsToDelete] = useState<number[]>([]);

  const initialIgnoredWords = useRef<TemplateIgnoredWordsDTO[]>([]);

  useEffect(() => {
    if (!templateId) {
      return;
    }

    Promise.all([getTemplateById(templateId), getIgnoredWordsByTemplateId(templateId)])
      .then(([template, ignoredWords]) => {
        setTemplate(template);

        setIgnoredWords(ignoredWords);
        initialIgnoredWords.current = ignoredWords;
      })
      .catch((err) => setErrorMessage("Failed to load ignored words."))
      .finally(() => {
        setIsLoading(false);
      });
  }, [templateId, getTemplateById, getIgnoredWordsByTemplateId]);

  const wordsToDelete = useMemo(() => {
    return initialIgnoredWords.current.filter(({ id }) => wordIdsToDelete.includes(id));
  }, [wordIdsToDelete]);

  function handleAddWord(values: FormValues) {
    const word = values.word.trim();
    if (!word) {
      return;
    }

    const alreadyExists = ignoredWords.find(() => {
      const newWord = word.toLowerCase();

      return (
        ignoredWords.some((w) => w.word.toLowerCase() === newWord) ||
        newWords.some((w) => w.word.toLowerCase() === newWord)
      );
    });

    if (alreadyExists) {
      formik.resetForm();
      return;
    }

    const newWord = {
      id: Date.now(),
      word,
    };

    setNewWords((prev) => [...prev, newWord]);

    formik.resetForm();
  }

  function handleDelete(wordId: number) {
    setWordIdsToDelete((prev) => [...prev, wordId]);
    setIgnoredWords((prev) => prev.filter(({ id }) => id !== wordId));
  }

  async function handleSaveAll() {
    if (newWords.length === 0 && wordIdsToDelete.length === 0) {
      return;
    }

    if (!templateId) {
      console.error("Could not save ignored words, missing templateId");
      return;
    }

    const addedWords = newWords.map(({ word }) => word);
    const updatedIgnoredWords = await updateTemplateIgnoredWords(templateId, addedWords, wordIdsToDelete);

    resetPage(updatedIgnoredWords);
    initialIgnoredWords.current = updatedIgnoredWords;
  }

  function resetPage(ignoredWords = initialIgnoredWords.current) {
    setNewWords([]);
    setWordIdsToDelete([]);
    setIgnoredWords(ignoredWords);
    formik.resetForm();
  }

  return (
    <div className="container bg-sw-secondary">
      <div className="row bg-sw-primary">
        <div className="d-flex flex-row mb-4" style={{ marginLeft: "-12px" }}>
          <div className="me-auto">
            <Link to="/admin" className="btn btn-sw btn-sm btn-outline">
              <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
      <div className="tablet-buffer bg-sw-secondary">
        {isLoading && <Loader />}
        {!isLoading && (
          <div className="row">
            <h3 className="mb-4">{template?.name ? `${template.name} - Ignored Words` : "Ignored Words"}</h3>
            {errorMessage && <span className="error">{errorMessage}</span>}
            {!errorMessage && (
              <>
                <div className="col-sm-12 col-lg-8">
                  <div className="row">
                    {ignoredWords.length === 0 && <span>No words to ignore for template.</span>}
                    {ignoredWords.length > 0 && (
                      <>
                        <TemplateIgnoredWordsColumn
                          words={ignoredWords.filter((_, i) => i % 2 === 0)}
                          onDelete={handleDelete}
                        />
                        <TemplateIgnoredWordsColumn
                          words={ignoredWords.filter((_, i) => i % 2 === 1)}
                          onDelete={handleDelete}
                        />
                      </>
                    )}
                  </div>
                  <div className="row mt-2">
                    {newWords.length > 0 && (
                      <div className="col-sm-12 col-lg-6">
                        <h5>Words to Add</h5>
                        <ul className="list-group">
                          {newWords.map(({ id, word }) => (
                            <Fragment key={id}>
                              <TemplateIgnoredWordRow id={id} word={word} />
                            </Fragment>
                          ))}
                        </ul>
                      </div>
                    )}
                    {wordsToDelete.length > 0 && (
                      <div className="col-sm-12 col-lg-6">
                        <h5>Words to Delete</h5>
                        <ul className="list-group">
                          {wordsToDelete.map(({ id, word }) => (
                            <Fragment key={id}>
                              <TemplateIgnoredWordRow id={id} word={word} />
                            </Fragment>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
                <div className="col-sm-12 col-lg-4 mt-4 mt-lg-0">
                  <form className="text-center" onSubmit={formik.handleSubmit}>
                    <div className="input-group">
                      <input
                        type="text"
                        name="word"
                        className="form-control"
                        autoComplete="off"
                        placeholder="Add word..."
                        onChange={formik.handleChange}
                        value={formik.values.word}
                      />
                      <button type="submit" className="btn btn-sw">
                        Add
                      </button>
                    </div>
                  </form>
                  <div className="d-flex mt-4">
                    <button className="btn btn-sw mx-auto" type="button" onClick={handleSaveAll}>
                      Save All
                    </button>
                    <button className="btn btn-sw mx-auto" type="button" onClick={() => resetPage()}>
                      Reset
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
