import { useEffect, useMemo, useState } from "react";
import { ResizableTable } from "../../../components/resizable-table";
import { useParams } from "react-router-dom";
import { TemplateSignatureTable } from "../../../types/signature-sheet.types";
import { useTemplateService } from "../../../services/template.service";
import { InlineSpinner } from "../../../components/inline-spinner";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { useAuthGuard } from "../../../hooks/useAuthGuard";
import { Roles } from "../../../types/user.types";

export default function TemplateTableReview() {
  useAuthGuard(Roles.Admin);

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const params = useParams();
  const templateId = params?.templateId;

  const [tableData, setTableData] = useState<TemplateSignatureTable>();
  const { getTableByTemplateId, postAdjustedTable } = useTemplateService();

  useEffect(() => {
    if (!templateId) return;
    getTableByTemplateId(templateId).then((table) => {
      console.debug(
        `intitial table ${table.columns[0].left} ${table.columns[3].left} ${
          table.columns[3].right
        } ${table.columns[table.columns.length - 1].right} `
      );
      setTableData(table);
    });
  }, [templateId, getTableByTemplateId]);

  async function handleSaveAdjustedTemplate(): Promise<void> {
    if (!templateId || !tableData) {
      return;
    }
    setIsSubmitting(true);
    await postAdjustedTable(templateId, tableData);
    setIsSubmitting(false);
  }

  const [width, height] = useMemo(() => {
    const width = window.innerWidth - 60;
    const height = width / (tableData?.imageAspectRatio || 1);
    return [width, height];
  }, [tableData]);

  if (!tableData) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <ResizableTable
        tableData={tableData}
        page={tableData.template.pages[0]}
        widthInPx={width}
        heightInPx={height}
      />
      <div className="row text-center">
        <div className="col-sm-12 mt-1">
          <button
            type="submit"
            className="btn btn-sw"
            onClick={handleSaveAdjustedTemplate}
            disabled={isSubmitting}
          >
            {!isSubmitting && (
              <FontAwesomeIcon icon={faArrowRight} className="me-2" />
            )}
            {isSubmitting && <InlineSpinner />}
            Next
          </button>
        </div>
      </div>
    </>
  );
}
