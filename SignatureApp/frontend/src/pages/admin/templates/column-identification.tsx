import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { handleError } from "../../../common/utils";
import { useTemplateService } from "../../../services/template.service";
import { GetNextSignatureTableColumnDTO } from "../../../types/template.types";
import { Roles } from "../../../types/user.types";
import { InlineSpinner } from "../../../components/inline-spinner";
import { useAuthGuard } from "../../../hooks/useAuthGuard";

interface ColumnIdentificationForm {
  fieldName: string;
  isSkipped: boolean;
  canBeInvalid: boolean;
  isHandwritten: boolean;
  isSignature: boolean;
  isName: boolean;
  isAddress: boolean;
  isDate: boolean;
  isVoterId: boolean;
}

export default function ColumnIdentification() {
  useAuthGuard(Roles.Admin);

  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [image, setImage] = useState("");
  const [transcribableField, setTranscribableField] = useState<GetNextSignatureTableColumnDTO>();
  const params = useParams();
  const templateId = params?.templateId;
  const fieldId = params?.fieldId;
  const formikRef = useRef<FormikProps<ColumnIdentificationForm>>(null);
  const [isComplete, setIsComplete] = useState(false);
  const navigate = useNavigate();
  const { getNextSignatureTableColumn, updateSignatureTableColumn } = useTemplateService();

  const getTheNextSignatureTableColumn = useCallback(() => {
    setErrorMessage("");
    setLoading(true);
    setIsComplete(false);

    getNextSignatureTableColumn(templateId!, fieldId).then(
      (tf) => {
        setLoading(false);
        setImage(tf.image.fileContents);
        setTranscribableField(tf);
        const vals = {
          fieldName: tf.name || "",
          ...tf,
          isSkipped: false,
        };

        formikRef.current?.setValues(vals);
      },
      (error) => {
        handleError(error, setErrorMessage, setLoading);
      }
    );
  }, [fieldId, templateId, getNextSignatureTableColumn]);

  useEffect(() => {
    if (templateId) {
      getTheNextSignatureTableColumn();
    }
  }, [getTheNextSignatureTableColumn, templateId]);

  const handleNext = (formValues: ColumnIdentificationForm) => {
    const { fieldName, isSkipped, isHandwritten, canBeInvalid, isSignature, isName, isAddress, isDate, isVoterId } =
      formValues;
    setErrorMessage("");
    setLoading(true);

    if (templateId && transcribableField?.id) {
      updateSignatureTableColumn(templateId, transcribableField?.id, {
        name: fieldName,
        isSkipped,
        isHandwritten,
        canBeInvalid,
        isSignature,
        isName,
        isAddress,
        isDate,
        isVoterId,
      }).then(
        (tf) => {
          if (tf && tf.id) {
            formikRef.current?.resetForm();
            navigate(`/admin/templates/${templateId}/column-identification/${transcribableField?.id}`);
          } else {
            setIsComplete(true);
          }
        },
        (error) => {
          handleError(error, setErrorMessage, setLoading);
        }
      );
    }
  };

  return (
    <div className="container bg-sw-secondary">
      <div className="row bg-sw-primary">
        <div className="d-flex flex-row mb-4" style={{ marginLeft: "-12px" }}>
          <div className="me-auto">
            <Link to="/admin" className="btn btn-sw btn-sm btn-outline">
              <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
      <div className="tablet-buffer bg-sw-secondary">
        <div className="row">
          <div className="col-sm-12 col-lg-8">
            {!loading && <img alt="signature-form" style={{ maxWidth: 650 }} src={`data:image/png;base64,${image}`} />}
          </div>
          <div className="col-sm-12 col-lg-4">
            {isComplete ? (
              <div className="alert alert-success" role="alert">
                You've completed the column identification!{" "}
                <Link to={"/admin/templates/" + templateId}>Click here</Link> to return to the template.
              </div>
            ) : (
              <Formik
                innerRef={formikRef}
                initialValues={
                  {
                    fieldName: "",
                    isSkipped: false,
                    isHandwritten: false,
                    canBeInvalid: true,
                    isSignature: false,
                    isName: false,
                    isAddress: false,
                    isDate: false,
                    isVoterId: false,
                  } as ColumnIdentificationForm
                }
                validate={(values) => {
                  const errors = {} as any;
                  if (values.fieldName === "") {
                    errors.fieldName = "Field name is required or skip";
                  }
                  return errors;
                }}
                onSubmit={handleNext}
              >
                <Form>
                  <div className="form-group">
                    <label htmlFor="fieldName">Name</label>
                    <Field name="fieldName" type="text" placeholder="Enter field name" className="form-control" />
                    <ErrorMessage name="fieldName" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isHandwritten"
                      name="isHandwritten"
                      placeholder="Is Handwritten"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isHandwritten">
                      Is Handwritten? (Requires human review)
                    </label>
                    <ErrorMessage name="isHandwritten" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="canBeInvalid"
                      name="canBeInvalid"
                      placeholder="Can Be Invalid"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="canBeInvalid">
                      Can Be Invalid?
                    </label>
                    <ErrorMessage name="canBeInvalid" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isSignature"
                      name="isSignature"
                      placeholder="Is Signature"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isSignature">
                      Is Signature?
                    </label>
                    <ErrorMessage name="isSignature" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isName"
                      name="isName"
                      placeholder="Is Name"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isName">
                      Is Name?
                    </label>
                    <ErrorMessage name="isName" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isAddress"
                      name="isAddress"
                      placeholder="Is Address"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isAddress">
                      Is Address?
                    </label>
                    <ErrorMessage name="isAddress" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isDate"
                      name="isDate"
                      placeholder="Is Date"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isDate">
                      Is Date?
                    </label>
                    <ErrorMessage name="isDate" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isVoterId"
                      name="isVoterId"
                      placeholder="Is Voter Id"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isVoterId">
                      Is Voter Id?
                    </label>
                    <ErrorMessage name="isVoterId" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group">
                    <button type="submit" className="btn btn-sw" disabled={loading}>
                      {!loading && <FontAwesomeIcon icon={faArrowRight} className="me-2" />}
                      {loading && <InlineSpinner />}
                      Next
                    </button>
                    <button
                      type="button"
                      style={{
                        fontWeight: "bold",
                        background: "none",
                        border: "none",
                        outline: "none",
                        cursor: "pointer",
                      }}
                      className="ms-3"
                      onClick={() => {
                        formikRef.current?.resetForm();
                        handleNext({
                          fieldName: "",
                          isSkipped: true,
                        } as ColumnIdentificationForm);
                      }}
                    >
                      SKIP THIS FIELD
                    </button>
                  </div>
                  {errorMessage && (
                    <div className="form-group">
                      <div className="alert alert-danger" role="alert">
                        {errorMessage}
                      </div>
                    </div>
                  )}
                </Form>
              </Formik>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
