import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { handleError } from "../../../common/utils";
import { useTemplateService } from "../../../services/template.service";
import { GetTranscribableFieldDTO, InputType } from "../../../types/template.types";
import { Roles } from "../../../types/user.types";
import { InlineSpinner } from "../../../components/inline-spinner";
import { useAuthGuard } from "../../../hooks/useAuthGuard";

interface FieldIdentificationForm {
  isSkipped: boolean;
  fieldName: string;
  isHandwritten: boolean;
  canBeInvalid: boolean;
  isSignature: boolean;
  isDate: boolean;
  isPrefixed: boolean;
  prefix: string;
  inputType: InputType;
  groupName: string;
}

export default function FieldIdentification() {
  useAuthGuard(Roles.Admin);

  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [image, setImage] = useState("");
  const [transcribableField, setTranscribableField] = useState<GetTranscribableFieldDTO>();
  const params = useParams();
  const templateId = params?.templateId;
  const fieldId = params?.fieldId;
  const formikRef = useRef<FormikProps<FieldIdentificationForm>>(null);
  const [isComplete, setIsComplete] = useState(false);
  const navigate = useNavigate();
  const { getNextTranscribableField, updateTranscribableField } = useTemplateService();

  useEffect(() => {
    document.documentElement.style.backgroundColor = "white";
    return () => {
      document.documentElement.style.backgroundColor = "";
    };
  }, []);

  const getTheNextTranscribableField = useCallback(() => {
    setErrorMessage("");
    setLoading(true);
    setIsComplete(false);

    getNextTranscribableField(templateId!, fieldId).then(
      (tf) => {
        setLoading(false);
        setImage(tf.image.fileContents);
        setTranscribableField(tf);
        formikRef.current?.setValues({
          isSkipped: tf.isSkipped || false,
          fieldName: tf.name || "",
          isHandwritten: tf.isHandwritten,
          canBeInvalid: tf.canBeInvalid ?? true,
          isSignature: tf.isSignature,
          isDate: tf.isDate,
          isPrefixed: tf.isPrefixed,
          prefix: tf.prefix || "",
          inputType: tf.inputType || InputType.Text,
          groupName: tf.groupName || "",
        });
      },
      (error) => {
        handleError(error, setErrorMessage, setLoading);
      }
    );
  }, [templateId, fieldId, getNextTranscribableField]);

  useEffect(() => {
    if (templateId) {
      getTheNextTranscribableField();
    }
  }, [templateId, getTheNextTranscribableField]);

  const handleNext = (formValues: FieldIdentificationForm) => {
    const {
      isSkipped,
      fieldName,
      isHandwritten,
      canBeInvalid,
      isSignature,
      isDate,
      isPrefixed,
      prefix,
      inputType,
      groupName,
    } = formValues;

    setErrorMessage("");
    setLoading(true);
    if (templateId && transcribableField?.id) {
      updateTranscribableField(templateId, transcribableField?.id, {
        isSkipped,
        name: fieldName,
        isHandwritten,
        canBeInvalid,
        isSignature,
        isDate,
        isPrefixed,
        prefix,
        inputType: Number(inputType),
        groupName,
      }).then(
        (tf) => {
          if (tf && tf.id) {
            formikRef.current?.resetForm();
            navigate(`/admin/templates/${templateId}/field-identification/${transcribableField?.id}`);
          } else {
            setIsComplete(true);
          }
        },
        (error) => {
          handleError(error, setErrorMessage, setLoading);
        }
      );
    }
  };

  return (
    <div className="container-fluid bg-sw-secondary">
      <div className="row bg-sw-primary">
        <div className="sw-buffer">
          <div className="d-flex flex-row mb-4">
            <div className="me-auto">
              <Link to="/admin" className="btn btn-sw btn-sm btn-outline">
                <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
                Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </div>
      <div className="tablet-buffer bg-sw-secondary">
        <div className="row">
          <div className="col-sm-12 col-lg-8">
            {!loading && <img alt="signature-form" style={{ maxWidth: 650 }} src={`data:image/png;base64,${image}`} />}
          </div>
          <div className="col-sm-12 col-lg-4">
            {isComplete ? (
              <div className="alert alert-success" role="alert">
                You've completed the field identification! <Link to={"/admin/templates/" + templateId}>Click here</Link>{" "}
                to return to the template.
              </div>
            ) : (
              <Formik
                innerRef={formikRef}
                initialValues={
                  {
                    isSkipped: false,
                    fieldName: "",
                    isHandwritten: true,
                    canBeInvalid: true,
                    isSignature: false,
                    isDate: false,
                    isPrefixed: false,
                    inputType: InputType.Text,
                    groupName: "",
                    prefix: "",
                  } as FieldIdentificationForm
                }
                validate={(values) => {
                  const errors = {} as any;
                  if (values.fieldName === "") {
                    errors.fieldName = "Field name is required or skip";
                  }

                  return errors;
                }}
                onSubmit={handleNext}
              >
                <Form>
                  <div className="form-group">
                    <label htmlFor="fieldName">Name</label>
                    <Field name="fieldName" type="text" placeholder="Enter field name" className="form-control" />
                    <ErrorMessage name="fieldName" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group">
                    <label htmlFor="inputType">Input type</label>
                    <Field as="select" name="inputType" className="form-control">
                      {Object.entries(InputType)
                        .filter((e) => isNaN(Number(e[0])) === false)
                        .map((e) => (
                          <option key={e[0]} value={e[0]}>
                            {e[1]}
                          </option>
                        ))}
                    </Field>
                    <ErrorMessage name="inputType" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group">
                    <label htmlFor="groupName">Group name</label>
                    <Field name="groupName" type="text" placeholder="Enter Group Name" className="form-control" />
                    <ErrorMessage name="groupName" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field id="isHandwritten" name="isHandwritten" className="form-check-input" type="checkbox" />
                    <label className="form-check-label" htmlFor="isHandwritten">
                      Is Handwritten? (Requires human review)
                    </label>
                    <ErrorMessage name="isHandwritten" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="canBeInvalid"
                      name="canBeInvalid"
                      placeholder="Can Be Invalid"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="canBeInvalid">
                      Can Be Invalid?
                    </label>
                    <ErrorMessage name="canBeInvalid" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isSignature"
                      name="isSignature"
                      placeholder="Is Signature"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isSignature">
                      Is Signature?
                    </label>
                    <ErrorMessage name="isSignature" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isDate"
                      name="isDate"
                      placeholder="Is Date"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isDate">
                      Is Date?
                    </label>
                    <ErrorMessage name="isDate" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group form-check">
                    <Field
                      id="isPrefixed"
                      name="isPrefixed"
                      placeholder="Is Prefixed"
                      className="form-check-input"
                      type="checkbox"
                    />
                    <label className="form-check-label" htmlFor="isPrefixed">
                      Is Prefixed?
                    </label>
                    <ErrorMessage name="isPrefixed" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group">
                    <label htmlFor="prefix">Prefix</label>
                    <Field name="prefix" type="text" placeholder="Enter Prefix" className="form-control" />
                    <ErrorMessage name="prefix" component="div" className="alert alert-danger p-2" />
                  </div>

                  <div className="form-group">
                    <button type="submit" className="btn btn-sw" disabled={loading}>
                      {!loading && <FontAwesomeIcon icon={faArrowRight} className="me-2" />}
                      {loading && <InlineSpinner />}
                      Next
                    </button>
                    <button
                      type="button"
                      style={{
                        fontWeight: "bold",
                        background: "none",
                        border: "none",
                        outline: "none",
                        cursor: "pointer",
                      }}
                      className="ms-3"
                      onClick={() => {
                        formikRef.current?.resetForm();
                        handleNext({
                          ...formikRef.current?.values,
                          isSkipped: true,
                        } as FieldIdentificationForm);
                      }}
                    >
                      SKIP THIS FIELD
                    </button>
                  </div>
                  {errorMessage && (
                    <div className="form-group">
                      <div className="alert alert-danger" role="alert">
                        {errorMessage}
                      </div>
                    </div>
                  )}
                </Form>
              </Formik>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
