import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useEffect, useMemo, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useAuthContext } from "../../AuthProvider";
import { useMatterService } from "../../services/matter.service";
import { Matter } from "../../types/matter.types";
import { checkForRole, handleError } from "../../common/utils";
import { Roles } from "../../types/user.types";
import { useWorkService } from "../../services/work.service";
import { TaskWorkStatus } from "../../types/work.types";
import CircleCounterIcon from "../../components/circle-counter-icon";
import TaskStatusTable from "../../components/task-status-table";

export default function ManagerMatterDetails() {
  const { authenticatedUser } = useAuthContext();
  const { getMatterById } = useMatterService();
  const { getTaskWorkStatus } = useWorkService();
  const [isLoading, setIsLoading] = useState(true);
  const [matter, setMatter] = useState<Matter | null>(null);
  const [tasks, setTasks] = useState<TaskWorkStatus[]>([]);
  const [flaggedWorkCount, setFlaggedWorkCount] = useState(0);

  const navigate = useNavigate();
  const params = useParams();
  const matterId = params.matterId || "";

  const { getFlaggedWorkCount } = useWorkService();

  useEffect(() => {
    getFlaggedWorkCount(matterId).then((flaggedWorkCount) => {
      setFlaggedWorkCount(flaggedWorkCount);
    });
  }, [getFlaggedWorkCount, matterId]);

  const isManager = useMemo(
    () => checkForRole(Roles.Manager, authenticatedUser?.roleId),
    [authenticatedUser?.roleId]
  );

  useEffect(() => {
    if (!isManager) {
      navigate("/login");
    }
  }, [isManager, navigate]);

  useEffect(() => {
    if (!isManager || !matterId) {
      return;
    }

    setIsLoading(true);
    getMatterById(matterId)
      .then((matter) => {
        setMatter(matter);
      })
      .catch((err) => handleError(err))
      .finally(() => setIsLoading(false));
  }, [matterId, getMatterById, isManager]);

  useEffect(() => {
    if (!isManager || !matterId) {
      return;
    }

    setIsLoading(true);
    getTaskWorkStatus(matterId)
      .then((tasks) => {
        setTasks(tasks);
      })
      .catch((err) => handleError(err))
      .finally(() => setIsLoading(false));
  }, [isManager, matterId, getTaskWorkStatus]);

  const isInvalid = !matter && !isLoading;

  return (
    <div className="container">
      <div className="d-flex flex-row mb-4">
        <div className="me-auto" style={{ marginLeft: "-12px" }}>
          <Link to="/manager" className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            Back to Dashboard
          </Link>
        </div>
        <div className="ms-auto" style={{ marginRight: "-12px" }}>
          <Link
            to={`/manager/${matterId}/flagged`}
            className="btn btn-sw btn-sm btn-outline"
          >
            <div className="ps-2">
              <CircleCounterIcon count={flaggedWorkCount} />
              Transcription Review
            </div>
          </Link>
        </div>
      </div>
      <div className="row tablet-view">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-3">
            <div>
              {isInvalid && <span>This matter is invalid</span>}
              {!isInvalid && (
                <>
                  <span>Matter</span>
                  <h2>{matter?.name}</h2>
                </>
              )}
            </div>
            <div className="mt-4 mb-4"></div>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer">
            <TaskStatusTable matter={matter} tasks={tasks} />
          </div>
        </div>
      </div>
    </div>
  );
}
