import { Link } from "react-router-dom";
import { useMatterService } from "../../services/matter.service";
import { faSquarePlus } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export default function HeaderActionsManager() {
  useMatterService();

  return (
    <div className="d-flex flex-row gap-2">
        <Link to="/manager/invite" className="btn btn-sw btn-outline">
                        <FontAwesomeIcon icon={faSquarePlus} className="me-2" />
                        Invite Multiple Reviewers
                      </Link>
    </div>
  );
}
