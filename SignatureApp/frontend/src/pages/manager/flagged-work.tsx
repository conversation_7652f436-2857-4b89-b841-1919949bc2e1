import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { useAuthContext } from "../../AuthProvider";
import { useWorkService } from "../../services/work.service";
import { WorkDto, ValidationForm, WorkStatus, WorkField, RegisteredVoterFlags } from "../../types/work.types";
import { Roles } from "../../types/user.types";
import { FormikProps } from "formik";
import { handleError, convertFieldValuesToStrings, convertFieldValuesToTypes, checkForRole } from "../../common/utils";
import WorkItem from "../../components/work-item.component";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { Loader } from "../../components/loader";
import HeaderNavBar from "../../components/header-nav-bar";

export default function FlaggedWork() {
  const location = useLocation();
  const params = useParams();
  const navigate = useNavigate();
  const { authenticatedUser } = useAuthContext();
  const { getNextFlaggedWork, updateWork } = useWorkService();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [work, setWork] = useState<WorkDto | null>();
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [previousPage, setPreviousPage] = useState("");
  const [backButtonText, setBackButtonText] = useState("");
  const formikRef = useRef<FormikProps<ValidationForm>>(null);

  const getPreviousPage = useCallback(() => {
    const from = location.pathname.split("/")[1];
    if (from === "admin") {
      const matterId = params.matterId;
      if (matterId) {
        setPreviousPage(`/admin/matters/${matterId}/details`);
        setBackButtonText(`Back to Matter`);
      }
    } else {
      setPreviousPage("/manager");
      setBackButtonText(`Back to Manager Dashboard`);
    }
  }, [location.pathname, params.matterId]);

  const getAdjustSheetUrl = () => {
    const from = location.pathname.split("/")[1];
    return `/${from}/matters/${work?.matterId}/sheets/${work?.sheetNumber}?workId=${work?.workId}`;
  };

  const loadNextFlaggedWork = useCallback(() => {
    const matterId = params.matterId;
    if (!matterId) {
      return;
    }
    setErrorMessage("");
    setLoading(true);
    setWork(null);
    getNextFlaggedWork(matterId).then(
      (work) => {
        setLoading(false);
        if (work) {
          setIsInitialLoad(false);
          work.fields = convertFieldValuesToStrings(work.fields);
          setWork(work);
          const from = location.pathname.split("/")[1];
          if (from === "admin") {
            navigate(`/admin/matters/${matterId}/review/transcription/${work?.workId}`);
          } else {
            navigate(`/manager/${matterId}/flagged/${work?.workId}`);
          }
        } else {
          // All work completed
          navigate(previousPage);
        }
      },
      (error) => {
        handleError(error, setErrorMessage, setLoading);
      }
    );
  }, [getNextFlaggedWork, location.pathname, navigate, params.matterId, previousPage]);

  useEffect(() => {
    if (!checkForRole(Roles.Manager, authenticatedUser?.roleId)) {
      navigate("/login");
    } else if (!work && isInitialLoad) {
      getPreviousPage();
      loadNextFlaggedWork();
    }
  }, [authenticatedUser?.roleId, getPreviousPage, isInitialLoad, loadNextFlaggedWork, navigate, work]);

  const handleSaveWork = (
    fields: WorkField[],
    externalDataRecordId?: string | null,
    registeredVoterFlags?: RegisteredVoterFlags | null
  ): Promise<void> => {
    if (!work?.workId) {
      return Promise.reject("No work");
    }
    setErrorMessage("");
    setLoading(true);
    return updateWork(work?.workId.toString(), {
      workStatusId: WorkStatus.Completed,
      fields: convertFieldValuesToTypes(fields),
      externalDataRecordId: externalDataRecordId || null,
      registeredVoterFlags: registeredVoterFlags || RegisteredVoterFlags.Unknown,
    }).then(
      () => {
        setLoading(false);
        setWork({ ...work, fields: convertFieldValuesToStrings(fields) });
      },
      (error) => {
        handleError(error, setErrorMessage, setLoading);
      }
    );
  };

  const handleNextFlaggedWork = (
    fields: WorkField[],
    externalDataRecordId?: string | null,
    registeredVoterFlags?: RegisteredVoterFlags | null
  ) => {
    if (work?.workId) {
      handleSaveWork(fields, externalDataRecordId, registeredVoterFlags).then(
        () => {
          loadNextFlaggedWork();
        },
        (error) => {
          handleError(error, setErrorMessage, setLoading);
        }
      );
    }
  };

  return (
    <div className="container">
      <HeaderNavBar
        leftContent={
          <Link to={previousPage} className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            {backButtonText}
          </Link>
        }
        rightContent={
          <Link to={getAdjustSheetUrl()} className="btn btn-sw btn-sm btn-outline">
            Adjust Sheet
          </Link>
        }
      />

      {loading && <Loader />}

      {!loading && work && (
        <WorkItem
          work={work}
          onSave={handleSaveWork}
          onNext={handleNextFlaggedWork}
          formikRef={formikRef}
          errorMessage={errorMessage}
        />
      )}

      {errorMessage && (
        <div className="row">
          <div className="col">
            <div className="form-group">
              <div className="alert alert-danger" role="alert">
                {errorMessage}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
