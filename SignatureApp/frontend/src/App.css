:root {
  --primary-light-bg: #E5E5E5;
  --secondary-light-bg: #FFFFFF;
  --primary-dark-bg: #3D7068;
  --secondary-dark-bg: #741D38;
  --primary-dark-fg: #14453D;
  --secondary-dark-fg: #741D38;
  --primary-accent-bg: #741D38;

  /*
  --primary-light-bg: #EBE3D6; /* cream * /
  --primary-dark-bg: #1D2437; /* navy * /
  --secondary-dark-bg: #CF8660; /* terra cotta * /
  --secondary-dark-bg: #5C8DAD; /* sky blue * /
  --primary-dark-fg: #1D2437; /* navy * /
  */
}

html {
  background-color: var(--primary-light-bg);
}

body {
  padding: 0;
  margin: 0;
}

.App {
  background-color: var(--primary-light-bg);
}

.App .navbar {
  padding: 0;
}

.row.tablet-view {
  min-height: 768px;
}

.container .navbar {
  padding: 0;
}
.navbar .navbar-brand {
  padding: 0;
}

.sw-buffer {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}

.tablet-left {
  background-color: var(--primary-dark-bg);
  color: var(--secondary-light-bg);
}
.tablet-right {
  background-color: var(--secondary-light-bg);
  color: var(--primary-dark-fg);
}
.tablet-buffer {
  margin: 1.5rem;
  padding-top: 15px;
  padding-bottom: 15px;
}

.left-right {
  justify-content: space-between;
}

.card-container.card {
  width: 368px !important;
  padding: 40px 40px;
  box-shadow: 0px 20px 30px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}
.card {
  padding: 20px 25px;
  margin: 0 auto 25px;
}
.card.tablet-left {
  background-color: var(--primary-dark-bg);
  color: var(--secondary-light-bg);
}

.checklist-link {
  color: var(--secondary-light-bg);
  text-decoration: underline;
}

.bg-sw-primary {
  background-color: var(--primary-light-bg);
}
.bg-sw-secondary {
  background-color: var(--secondary-light-bg);
}

.tablet-left a {
  color: var(--secondary-light-bg);
}
.tablet-left a:hover {
  color: var(--secondary-light-bg);
}

.btn.btn-sw {
  color: var(--primary-dark-bg);
  border-color: var(--primary-dark-bg);
}

.btn.btn-sw-m {
  color: var(--secondary-dark-bg);
  border-color: var(--secondary-dark-bg);
}

.btn.btn-light {
  color: var(--secondary-light-bg);
  background-color: transparent;
  border-color: var(--secondary-light-bg);
}
.btn.btn-light:hover {
  color: var(--primary-dark-bg);
}

.sw-maroon {
  color: var(--secondary-dark-bg);
}
.sw-maroon:hover {
  color: var(--secondary-dark-bg);
}

.sw-green {
  color: var(--primary-dark-bg);
}
.sw-green:hover {
  color: var(--primary-dark-bg);
}

input[type="checkbox"] {
  accent-color: var(--primary-accent-bg);
}
input[type="checkbox"]:checked {
  background-color: var(--primary-accent-bg);
  border-color: var(--primary-accent-bg);
}
.form-check-input:checked {
  background-color: var(--primary-accent-bg);
  border-color: var(--primary-accent-bg);
}

.error {
  color: red;
}

.password-strength {
  height: 0.5rem !important;
}
.password-strength-score {
  font-size: 0.8rem;
}
.password-strength > .weakest {
  background-color: red;
}
.password-strength > .weak {
  background-color: orange;
}
.password-strength > .fair {
  background-color: yellow;
}
.password-strength > .good {
  background-color: lime;
}
.password-strength > .strong {
  background-color: green;
}

table.excel {
  border-collapse: collapse;
}

table.excel th,
table.excel tr td:first-child {
  font-weight: bold;
  background-color: #ddd;
}

table.excel th,
table.excel td {
  padding: 0.5em;
  border: 1px solid #ccc;
  white-space: nowrap;
}

table.sw {
  border-collapse: collapse;
  width: 100%;
  cursor: pointer;
}
/* .table.sw>:not(:first-child) {
  border-bottom: 1px solid var(--primary-dark-bg);
}
*/
table.sw th {
  border-bottom: 1px solid var(--primary-dark-bg);
}
table.sw td, table.sw th {
  padding: .25rem;
  border-top: 0;
}

.row-hover:hover {
  color: #ffffff;
  background-color: var(--primary-dark-bg);
  -moz-border-radius-topleft: 3px;
  -moz-border-radius-topright: 3px;
  -moz-border-radius-bottomright: 3px;
  -moz-border-radius-bottomleft: 3px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}

.ReactModal__Overlay {
  opacity: 0;
  transition: opacity 200ms ease-in-out;
}
.ReactModal__Overlay--after-open {
  opacity: 1;
}
.ReactModal__Overlay--before-close {
  opacity: 0;
}
.ReactModal__Overlay,
.ReactModal__Overlay--after-open {
  background-color: rgba(0, 0, 0, 0.25) !important;
}
