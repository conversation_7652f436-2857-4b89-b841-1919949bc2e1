import "./App.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap";

import { BrowserRouter, Routes, Route, Link } from "react-router-dom";
import { useAuthContext } from "./AuthProvider";
import { useEffect } from "react";

import Register from "./pages/auth/register";
import Login from "./pages/auth/login";
import ForgotPassword from "./pages/auth/forgot-password";
import ResetPassword from "./pages/auth/reset-password";

import AdminBoard from "./pages/admin/board-admin";
import InviteManager from "./pages/admin/invite-manager";
import TeamMembers from "./pages/admin/team-members";

import TemplateDetails from "./pages/admin/templates/template-details";
import FieldIdentification from "./pages/admin/templates/field-identification";
import ColumnIdentification from "./pages/admin/templates/column-identification";
import TemplateTableReview from "./pages/admin/templates/template-table-review";
import TemplateIgnoredWords from "./pages/admin/templates/template-ignored-words";

import AdminMatterEdit from "./pages/admin/matters/admin-matter-edit";
import MatterDetails from "./pages/admin/matters/matter-details";
import MatterSheets from "./pages/admin/matters/matter-valid-sheets";
import MatterVoterFiles from "./pages/admin/matters/matter-voter-files";
import Deficiencies from "./pages/admin/matters/deficiencies";
import UploadInvalid from "./pages/admin/matters/upload-invalid";
import FreeFormVoterSearch from "./pages/admin/matters/free-form-voter-search";
import SignatureSheetUploadHistory from "./pages/admin/matters/signature-sheet-upload-history";

import UploadVoters from "./pages/admin/voters/upload-voters";

import ManagerBoard from "./pages/manager/board-manager";
import InviteReviewers from "./pages/manager/invite-reviewers";
import GroupDetails from "./pages/manager/group-details";
import FlaggedWork from "./pages/manager/flagged-work";

import ReviewerBoard from "./pages/review/board-reviewer";
import ReviewDeficiency from "./pages/admin/matters/deficiency-review";
import ManagerMatterDetails from "./pages/manager/manager-matter-details";
import UploadCirculatorInfo from "./pages/admin/matters/upload-circulator-info";
import StatesReferenceData from "./pages/admin/reference-data/states-reference-data";
import CitiesAndCountiesReferenceData from "./pages/admin/reference-data/cities-and-counties-reference-data";
import { PreviewRowWorkItems } from "./components/preview-row-work-items";
import { PreviewFieldWorkItems } from "./components/preview-field-work-items";
import MatterValidSheets from "./pages/admin/matters/matter-valid-sheets";
import MatterInvalidSheets from "./pages/admin/matters/matter-invalid-sheets";
import PreviewSheetWork from "./pages/admin/matters/preview-sheet-work";
import { MatterProvider } from "./contexts/MatterContext";

export default function App() {
  const { authenticatedUser, setAuthenticatedUser } = useAuthContext();

  const logOut = () => {
    setAuthenticatedUser(null);
  };

  useEffect(() => {}, [setAuthenticatedUser]);

  return (
    <div className="App">
      <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <nav className="navbar navbar-expand">
          <Link to={"/"} className="navbar-brand">
            <img width={60} src="/vp-logo.svg" alt="Veracity Petitions Logo" />
          </Link>

          {authenticatedUser && (
            <div className="navbar-nav ms-auto">
              <div className="d-flex align-items-center sw-maroon me-2">Logged in as {authenticatedUser.fullName}</div>
              <a href="/login" className="nav-link pe-0 me-4" onClick={logOut}>
                <button type="button" className="btn btn-sw-m">
                  Log Out
                </button>
              </a>
            </div>
          )}
        </nav>
        <div className="mt-4">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />

            <Route path="/admin" element={<AdminBoard />} />
            <Route path="/admin/members" element={<TeamMembers />} />
            <Route path="/admin/invite" element={<InviteManager />} />
            <Route path="/admin/states" element={<StatesReferenceData />} />
            <Route path="/admin/states/:stateId/cities-and-counties" element={<CitiesAndCountiesReferenceData />} />

            <Route path="/admin/templates/:templateId" element={<TemplateDetails />} />
            <Route path="/admin/templates/:templateId/table-review" element={<TemplateTableReview />} />
            <Route path="/admin/templates/:templateId/field-identification" element={<FieldIdentification />} />
            <Route
              path="/admin/templates/:templateId/field-identification/:fieldId"
              element={<FieldIdentification />}
            />
            <Route path="/admin/templates/:templateId/column-identification" element={<ColumnIdentification />} />
            <Route
              path="/admin/templates/:templateId/column-identification/:fieldId"
              element={<ColumnIdentification />}
            />
            <Route path="/admin/templates/:templateId/ignored-words" element={<TemplateIgnoredWords />} />

            <Route
              path="/admin/matters/:matterId"
              element={
                <MatterProvider>
                  <AdminMatterEdit />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/details"
              element={
                <MatterProvider>
                  <MatterDetails />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/sheets"
              element={
                <MatterProvider>
                  <MatterValidSheets />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/sheets/:sheetNumber"
              element={
                <MatterProvider>
                  <MatterValidSheets />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/sheets/invalid"
              element={
                <MatterProvider>
                  <MatterInvalidSheets />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/sheets/invalid/:sheetNumber"
              element={
                <MatterProvider>
                  <MatterInvalidSheets />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/release/:sheetNumber"
              element={
                <MatterProvider>
                  <PreviewSheetWork isRelease={true} />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/preview/:sheetNumber/rows"
              element={
                <MatterProvider>
                  <PreviewRowWorkItems isRelease={false} />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/preview/:sheetNumber/fields"
              element={
                <MatterProvider>
                  <PreviewFieldWorkItems isRelease={false} />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/voters"
              element={
                <MatterProvider>
                  <MatterVoterFiles />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/deficiencies"
              element={
                <MatterProvider>
                  <Deficiencies />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/review/transcription"
              element={
                <MatterProvider>
                  <FlaggedWork />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/review/transcription/:workId"
              element={
                <MatterProvider>
                  <FlaggedWork />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/deficiencies/review"
              element={
                <MatterProvider>
                  <ReviewDeficiency />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/deficiencies/review/:deficiencyId"
              element={
                <MatterProvider>
                  <ReviewDeficiency />
                </MatterProvider>
              }
            />

            <Route
              path="/admin/matters/:matterId/upload-invalid"
              element={
                <MatterProvider>
                  <UploadInvalid />
                </MatterProvider>
              }
            />
            <Route
              path="/admin/matters/:matterId/free-form-voter-search"
              element={
                <MatterProvider>
                  <FreeFormVoterSearch />
                </MatterProvider>
              }
            />

            <Route
              path="/admin/matters/:matterId/signature-sheet-upload-history"
              element={
                <MatterProvider>
                  <SignatureSheetUploadHistory />
                </MatterProvider>
              }
            />

            <Route
              path="/admin/matters/:matterId/circulator-info-upload"
              element={
                <MatterProvider>
                  <UploadCirculatorInfo />
                </MatterProvider>
              }
            />

            <Route path="/admin/voters/:dataSourceId" element={<UploadVoters />} />

            <Route path="/manager" element={<ManagerBoard />} />
            <Route path="/manager/invite" element={<InviteReviewers />} />
            <Route path="/manager/:matterId/flagged" element={<FlaggedWork />} />
            <Route path="/manager/:matterId/flagged/:workId" element={<FlaggedWork />} />
            <Route path="/manager/groups/:groupId" element={<GroupDetails />} />
            <Route path="/manager/matters/:matterId/details" element={<ManagerMatterDetails />} />
            <Route path="/manager/matters/:matterId/sheets/:sheetNumber" element={<MatterSheets />} />

            <Route path="/work" element={<ReviewerBoard />} />
            <Route path="/work/:workId" element={<ReviewerBoard />} />

            <Route path="*" element={<Login />} />
          </Routes>
        </div>
      </BrowserRouter>
    </div>
  );
}
