import { useCallback } from "react";
import {
  InvalidSheet,
  WhichPartsAreValidDTO,
} from "../types/signature-sheet.types";
import { useAuthenticatedRequestService } from "./service.base";

export function useInvalidSheetService() {
  const PATH = "invalidsheets";
  const { authGet, authPost } = useAuthenticatedRequestService();

  const getInvalidSheetCountByMatter = useCallback(
    async function (matterId: string): Promise<number> {
      return await authGet(`${PATH}/${matterId}/count`);
    },
    [authGet]
  );

  const getNextInvalidSheet = useCallback(
    async function (matterId: string): Promise<InvalidSheet> {
      return await authGet(`${PATH}/${matterId}/next`);
    },
    [authGet]
  );

  const getInvalidSheetImageById = useCallback(
    async function (
      matterId: string,
      invalidSheetId: string,
      pageNumber: number
    ) {
      return await authGet(
        `${PATH}/${matterId}/${invalidSheetId}/${pageNumber}`,
        {
          responseType: "blob",
        }
      );
    },
    [authGet]
  );

  const markInvalidSheetAsValid = useCallback(
    async function (
      matterId: string,
      invalidSheetId: number,
      whichPartsAreValid: WhichPartsAreValidDTO
    ) {
      return await authPost(
        `${PATH}/${matterId}/markvalid/${invalidSheetId}`,
        whichPartsAreValid
      );
    },
    [authPost]
  );

  const funcs = {
    getInvalidSheetCountByMatter,
    getNextInvalidSheet,
    getInvalidSheetImageById,
    markInvalidSheetAsValid,
  };
  return funcs;
}
