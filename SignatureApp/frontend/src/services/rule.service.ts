import { useCallback } from "react";
import { RuleSummary, RuleContextType } from "../types/rule.types";
import { ViolationCriterion } from "../types/work.types";
import {ExecutionStatus, GetLastRunDTO} from "../types/background-operation.types";
import { useAuthenticatedRequestService } from "./service.base";

export function useRuleService() {
  const RULES_PATH = "rules";
  const BACKOP_PATH = "backgroundoperation";
  const { authGet, authPost, authDelete } = useAuthenticatedRequestService();

  const getRules = useCallback(
    async function (matterId: string): Promise<RuleSummary[]> {
      return await authGet(`${RULES_PATH}/${matterId}`);
    },
    [authGet]
  );

  const getMatterViolations = useCallback(
    async function (matterId: string): Promise<ViolationCriterion[]> {
      return await authGet(
        `${RULES_PATH}/${matterId}/type/${RuleContextType.Matter}`
      );
    },
    [authGet]
  );

  const getSheetViolations = useCallback(
    async function (matterId: string): Promise<ViolationCriterion[]> {
      return await authGet(
        `${RULES_PATH}/${matterId}/type/${RuleContextType.SignatureSheet}`
      );
    },
    [authGet]
  );

  const getNonTaskByType = useCallback(
    async function (
      matterId: string,
      type: RuleContextType
    ): Promise<ViolationCriterion[]> {
      return await authGet(`${RULES_PATH}/${matterId}/nontask/${type}`);
    },
    [authGet]
  );

  const getRuleCount = useCallback(
    async function (matterId: string): Promise<number> {
      return await authGet(`${RULES_PATH}/count/?matterId=${matterId}`);
    },
    [authGet]
  );

  const getLastRunRulesOperation = useCallback(
    async function (matterId: string): Promise<GetLastRunDTO> {
      const result = await authGet(`${RULES_PATH}/${matterId}/lastrun`);
      return result;
    },
    [authGet]
  );

  const runRules = useCallback(
    async function (matterId: string, force: boolean = false)  {
      return await authPost(`${RULES_PATH}/run/${matterId}?force=${force}`, {});
    },
    [authPost]
  );

  const deleteBackgroundOperation = useCallback(
    async function (backgroundId: number): Promise<boolean> {
      return await authDelete(`${BACKOP_PATH}/${backgroundId}`);
    },
    [authDelete]
  );

  const getBackgroundOperationStatus = useCallback(
    async function (backgroundId: number): Promise<ExecutionStatus> {
      const result = await authGet(`${BACKOP_PATH}/${backgroundId}`);
      return result.executionStatus;
    },
    [authGet]
  );

  const funcs = {
    getRules,
    getMatterViolations,
    getSheetViolations,
    getNonTaskByType,
    getRuleCount,
    runRules,
    deleteBackgroundOperation,
    getBackgroundOperationStatus,
    getLastRunRulesOperation,
  };
  return funcs;
}
