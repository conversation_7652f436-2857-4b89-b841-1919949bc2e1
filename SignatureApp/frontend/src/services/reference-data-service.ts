import { useCallback } from "react";
import { useAuthenticatedRequestService } from "./service.base";
import { UsState } from "../types/reference-data.types";
import { Boundary } from "../types/boundary.types";

export function useReferenceDataService() {
  const STATES_PATH = "usstates";
  const { authGet } = useAuthenticatedRequestService();

  const getStates = useCallback(
    async function (): Promise<UsState[]> {
      return await authGet(STATES_PATH);
    },
    [authGet]
  );

  const getStateById = useCallback(
    async function (stateId): Promise<UsState> {
      return await authGet(`${STATES_PATH}/${stateId}`);
    },
    [authGet]
  );

  const getCountiesByStateId = useCallback(
    async function (stateId): Promise<Boundary[]> {
      return await authGet(`${STATES_PATH}/${stateId}/counties`);
    },
    [authGet]
  );

  const getCitiesByStateId = useCallback(
    async function (stateId): Promise<Boundary[]> {
      return await authGet(`${STATES_PATH}/${stateId}/cities`);
    },
    [authGet]
  );

  const getZipCodesByStateId = useCallback(
    async function (stateId): Promise<Boundary[]> {
      return await authGet(`${STATES_PATH}/${stateId}/zipcodes`);
    },
    [authGet]
  );

  const funcs = {
    getStates,
    getStateById,
    getCountiesByStateId,
    getCitiesByStateId,
    getZipCodesByStateId
  };
  return funcs;
}
