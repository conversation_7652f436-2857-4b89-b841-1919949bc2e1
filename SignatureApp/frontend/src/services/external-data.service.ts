import { useCallback } from "react";
import {
  ExternalData,
  ExternalDataField,
  ExternalDataSource,
  GetVoterSearchDTO,
  SearchResults,
  UploadType,
  VoterSearchDTO,
} from "../types/external-data.types";
import { useAuthenticatedRequestService } from "./service.base";

export function useExternalDataService() {
  const EXTERNALDATA_PATH = "externaldata";
  const { authGet, authPost, authDelete } = useAuthenticatedRequestService();

  const getAllExternalData = useCallback(
    async function (uploadType: UploadType): Promise<ExternalDataSource[]> {
      return await authGet(`${EXTERNALDATA_PATH}?uploadType=${uploadType}`);
    },
    [authGet]
  );

  const getExternalDataSourceById = useCallback(
    async function (externalDataSourceId: string): Promise<ExternalDataSource> {
      return await authGet(`${EXTERNALDATA_PATH}/id/${externalDataSourceId}`);
    },
    [authGet]
  );

  const getExternalDataSourceMatters = useCallback(
    async function (matterId: string): Promise<ExternalDataSource[]> {
      return await authGet(`${EXTERNALDATA_PATH}/matter/${matterId}`);
    },
    [authGet]
  );

  const freeFormVoterSearch = useCallback(
    async function (
      matterId: string,
      criteria: VoterSearchDTO
    ): Promise<GetVoterSearchDTO> {
      return await authPost(
        `${EXTERNALDATA_PATH}/matter/${matterId}/votersearch`,
        criteria
      );
    },
    [authPost]
  );

  const postExternalDataSourceMatters = useCallback(
    async function (
      matterId: string,
      formValues: Record<string, boolean>
    ): Promise<void> {
      return await authPost(
        `${EXTERNALDATA_PATH}/matter/${matterId}`,
        formValues
      );
    },
    [authPost]
  );

  const getWorkVoter = useCallback(
    async function (workId: number, voterId: string): Promise<ExternalData> {
      return await authGet(`${EXTERNALDATA_PATH}/${workId}/${voterId}`);
    },
    [authGet]
  );

  const voterRegistrationSearch = useCallback(
    async function (
      workId: string,
      externalFields: ExternalDataField[]
    ): Promise<SearchResults> {
      return await authPost(
        `${EXTERNALDATA_PATH}/search/${workId}`,
        externalFields
      );
    },
    [authPost]
  );

  const getMatchingCounty = useCallback(
    async function (countyName: string): Promise<boolean> {
      return await authGet(`${EXTERNALDATA_PATH}/${countyName}`);
    },
    [authGet]
  );

  const funcs = {
    getAllExternalData,
    getExternalDataSourceById,
    getExternalDataSourceMatters,
    freeFormVoterSearch,
    postExternalDataSourceMatters,
    getWorkVoter,
    voterRegistrationSearch,
    getMatchingCounty,
  };
  return funcs;
}
