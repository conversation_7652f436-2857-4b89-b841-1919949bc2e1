import { useCallback } from "react";
import { useAuthenticatedRequestService } from "./service.base";
import { UploadHistoryDTO } from "../types/signature-sheet.types";

export function useUploadService() {
  const UPLOAD_PATH = "upload/signaturesheets";
  const { authGet, authPost, authDelete } = useAuthenticatedRequestService();

  const startUpload = useCallback(
    async function (
      matterId: string,
      templateId: string,
      downloadUrl: string
    ): Promise<string> {
      const upload = {
        matterId: Number(matterId),
        templateId: Number(templateId),
        downloadUrl,
      };
      return await authPost(`${UPLOAD_PATH}/start`, upload);
    },
    [authPost]
  );

  const deleteUpload = useCallback(
    async function (uploadId: string): Promise<null> {
      return await authDelete(`${UPLOAD_PATH}/${uploadId}`);
    },
    [authDelete]
  );

  const getUploadHistory = useCallback(
    async function (matterId: string): Promise<UploadHistoryDTO[]> {
      return await authGet(`${UPLOAD_PATH}/${matterId}/history`);
    },
    [authGet]
  );

  const postRequeueUncompleted = useCallback(
    async function (matterId: string): Promise<string> {
      return await authPost(
        `${UPLOAD_PATH}/${matterId}/requeue/uncompleted`,
        {}
      );
    },
    [authPost]
  );

  const getIsUploadValidAgainstExistingTemplate = useCallback(
    async function (matterId: string, newTemplateId: string): Promise<boolean> {
      return await authGet(
        `${UPLOAD_PATH}/${matterId}/validate/${newTemplateId}`
      );
    },
    [authGet]
  );

  const funcs = {
    startUpload,
    deleteUpload,
    getUploadHistory,
    postRequeueUncompleted,
    getIsUploadValidAgainstExistingTemplate,
  };
  return funcs;
}
