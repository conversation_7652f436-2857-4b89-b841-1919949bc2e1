import axios from "axios";
import { UploadProgress } from "../types/file.types";
import { useCallback } from "react";
import { BackgroundOperation } from "../types/background-operation.types";
import { useAuthenticatedRequestService } from "./service.base";
const API_URL = process.env.REACT_APP_BACKEND_URL;

export function useUploadFileService() {
  const BACKOP_PATH = "backgroundoperation";
  const { authGet, authDelete } = useAuthenticatedRequestService();

  const uploadFile = useCallback(async function uploadFile(
    file: File,
    urlPath: string,
    accessToken: string,
    { setUploadProgress }: UploadProgress,
    additionalData?: { [key: string]: string }
  ) {
    let formData = new FormData();
    if (additionalData) {
      for (let key in additionalData) {
        const value = additionalData[key];
        formData.append(key, value);
      }
    }
    formData.append("file", file);

    return await axios.post(API_URL + urlPath, formData, {
      headers: {
        Authorization: "Bearer " + accessToken,
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        const { loaded, total } = progressEvent;
        setUploadProgress(Math.floor((loaded * 100) / (total ?? 1)));
      },
    });
  },
  []);

  const deleteBackgroundOperation = useCallback(
    async function (backgroundId: number): Promise<boolean> {
      return await authDelete(`${BACKOP_PATH}/${backgroundId}`);
    },
    [authDelete]
  );

  const getBackgroundOperation = useCallback(
    async function (backgroundId: number): Promise<BackgroundOperation> {
      return await authGet(`${BACKOP_PATH}/${backgroundId}`);
    },
    [authGet]
  );

  return {
    uploadFile,
    deleteBackgroundOperation,
    getBackgroundOperation,
  };
}
