import { useCallback } from "react";
import { DeficiencyReview } from "../types/deficiency.types";
import { useAuthenticatedRequestService } from "./service.base";

export function useDeficiencyReviewService() {
  const { authGet, authPost } = useAuthenticatedRequestService();

  const getReviewableDeficiencyCountByMatter = useCallback(
    async function (matterId: string): Promise<number> {
      const deficiencyIds = await authGet(`matters/${matterId}/deficiency/reviews`);
      return deficiencyIds.length;
    },
    [authGet]
  );
  const getNextReviewableDeficiencyByMatter = useCallback(
    async function (matterId: string): Promise<DeficiencyReview> {
      return await authGet(`matters/${matterId}/deficiency/reviews/next`);
    },
    [authGet]
  );

  const postDeficiencyReview = useCallback(
    async function (matterId: string, deficiencyReview: DeficiencyReview): Promise<DeficiencyReview> {
      return await authPost(`matters/${matterId}/deficiency/reviews`, deficiencyReview);
    },
    [authPost]
  );

  const funcs = {
    getReviewableDeficiencyCountByMatter,
    getNextReviewableDeficiencyByMatter,
    postDeficiencyReview,
  };
  return funcs;
}
