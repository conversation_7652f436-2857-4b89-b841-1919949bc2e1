import { useCallback } from "react";
import { useAuthenticatedRequestService } from "./service.base";

export function useUploadExternalDataService() {
  const UPLOAD_PATH = "upload/externaldata";
  const { authGet, authDelete } = useAuthenticatedRequestService();

  const checkExternalData = useCallback(
    async function (id: string): Promise<number> {
      return await authGet(`${UPLOAD_PATH}/voters/${id}`);
    },
    [authGet]
  );

  const deleteExternalData = useCallback(
    async function (id: string): Promise<number> {
      return await authDelete(`${UPLOAD_PATH}/voters/${id}`);
    },
    [authDelete]
  );

  const deleteCirculators = useCallback(
    async function (id: string): Promise<number> {
      return await authDelete(`${UPLOAD_PATH}/circulators/${id}`);
    },
    [authDelete]
  );

  const funcs = {
    checkExternalData,
    deleteExternalData,
    deleteCirculators,
  };
  return funcs;
}
