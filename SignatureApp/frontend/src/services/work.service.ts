import { useCallback } from "react";
import {
  WorkDto,
  FinishWorkDTO,
  MatterWorkStatusDTO,
  TaskWorkStatus,
  Work,
} from "../types/work.types";
import { useAuthenticatedRequestService } from "./service.base";

export function useWorkService() {
  const WORK_PATH = "work";
  const { authGet, authPost, authPut } = useAuthenticatedRequestService();

  const getWorkStatus = useCallback(
    async function (): Promise<MatterWorkStatusDTO[]> {
      return await authGet(`${WORK_PATH}/status`);
    },
    [authGet]
  );

  const getTaskWorkStatus = useCallback(
    async function (matterId: string): Promise<TaskWorkStatus[]> {
      return await authGet(`${WORK_PATH}/${matterId}/taskstatus`);
    },
    [authGet]
  );

  const getWork = useCallback(
    async function (id: string): Promise<WorkDto> {
      return await authGet(`${WORK_PATH}/${id}`);
    },
    [authGet]
  );

  const getNextWork = useCallback(
    async function (): Promise<WorkDto | null> {
      return await authGet(`${WORK_PATH}/next`);
    },
    [authGet]
  );

  const getNextFlaggedWork = useCallback(
    async function (matterId: string): Promise<WorkDto | null> {
      return await authGet(`${WORK_PATH}/flagged/${matterId}`);
    },
    [authGet]
  );

  const getFlaggedWorkCount = useCallback(
    async function (matterId: string): Promise<number> {
      return await authGet(`${WORK_PATH}/flagged/${matterId}/count`);
    },
    [authGet]
  );

  const getUnavailableWorkCount = useCallback(
    async function (matterId: string, sheetNumber: string): Promise<number> {
      return await authGet(
        `${WORK_PATH}/unavailable/${matterId}/${sheetNumber}/count`
      );
    },
    [authGet]
  );

  const getUnavailableWork = useCallback(
    async function (matterId: string, sheetNumber: string): Promise<Work[]> {
      return await authGet(
        `${WORK_PATH}/unavailable/${matterId}/${sheetNumber}`
      );
    },
    [authGet]
  );

  const releaseUnavailableWork = useCallback(
    async function (
      matterId: string,
      sheetNumber: string,
      releaseWorkIds: number[]
    ): Promise<Work[]> {
      return await authPost(
        `${WORK_PATH}/unavailable/${matterId}/${sheetNumber}/release`,
        releaseWorkIds
      );
    },
    [authPost]
  );

  const getPreviewRowWorkItems = useCallback(
    async function (
      matterId: string,
      sheetNumber: string,
      rowNumber: string
    ): Promise<WorkDto[]> {
      return await authGet(
        `${WORK_PATH}/preview/${matterId}/${sheetNumber}/row/${rowNumber}`
      );
    },
    [authGet]
  );

  const getPreviewFieldWorkItems = useCallback(
    async function (matterId: string, sheetNumber: string): Promise<WorkDto[]> {
      return await authGet(
        `${WORK_PATH}/preview/${matterId}/${sheetNumber}/fields`
      );
    },
    [authGet]
  );

  const getPreviousWork = useCallback(
    async function (workId: string): Promise<WorkDto | null> {
      return await authGet(`${WORK_PATH}/${workId}/previous`);
    },
    [authGet]
  );

  const updateWork = useCallback(
    async function (workId: string, work: FinishWorkDTO): Promise<null> {
      return await authPut(`${WORK_PATH}/${workId}`, work);
    },
    [authPut]
  );

  const funcs = {
    getWorkStatus,
    getTaskWorkStatus,
    getWork,
    getNextWork,
    getNextFlaggedWork,
    getFlaggedWorkCount,
    getUnavailableWorkCount,
    getUnavailableWork,
    releaseUnavailableWork,
    getPreviousWork,
    getPreviewFieldWorkItems,
    getPreviewRowWorkItems,
    updateWork,
  };
  return funcs;
}
