import {useCallback} from 'react';
import {DataTransformationStatusDTO} from '../types/data-transformation-step';
import {useAuthenticatedRequestService} from './service.base';

export function useDataTransformationStepService() {
    const PATH_PREFIX = 'datatransformationstep/';
    const { authGet } = useAuthenticatedRequestService();

    const getStatusByUploadId = useCallback(async function (uploadId: number): Promise<DataTransformationStatusDTO> {
        return await authGet(PATH_PREFIX + "status/" + uploadId);
    }, [authGet]);
  
    return {
        getStatusByUploadId,
    };
}