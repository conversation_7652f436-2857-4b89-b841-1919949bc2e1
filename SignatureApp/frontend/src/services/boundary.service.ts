import { useCallback } from "react";
import { useAuthenticatedRequestService } from "./service.base";
import { BoundaryPoint } from "../types/boundary.types";

export function useBoundaryService() {
  const BOUNDARIES_PATH = "boundaries";
  const { authGet, authHead } = useAuthenticatedRequestService();

  const doesBoundaryExist = useCallback(
    async function (stateId: number, boundaryName: string): Promise<boolean> {
      return await authHead(`${BOUNDARIES_PATH}/${stateId}/${boundaryName}`);
    },
    [authHead]
  );

  const getBoundaryPointsByName = useCallback(
    async function (
      stateId: number,
      boundaryName: string
    ): Promise<BoundaryPoint[]> {
      return await authGet(`${BOUNDARIES_PATH}/${stateId}/${boundaryName}`);
    },
    [authGet]
  );

  const funcs = {
    doesBoundaryExist,
    getBoundaryPointsByName,
  };
  return funcs;
}
