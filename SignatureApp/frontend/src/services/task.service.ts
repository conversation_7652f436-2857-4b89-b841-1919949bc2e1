import { useCallback } from "react";
import { Task } from "../types/task.types";
import { useAuthenticatedRequestService } from "./service.base";

export function useTaskService() {
  const TASKS_PATH = "tasks";
  const { authGet, authPost } = useAuthenticatedRequestService();

  const getTasks = useCallback(
    async function (matterId: string): Promise<Task[]> {
      return await authGet(`${TASKS_PATH}/${matterId}/all`);
    },
    [authGet]
  );

  const getTaskCount = useCallback(
    async function (matterId: string): Promise<number> {
      return await authGet(`${TASKS_PATH}/${matterId}/count`);
    },
    [authGet]
  );

  const createTasks = useCallback(
    async function (matterId: string, templateId?: string) {
      let url = `${TASKS_PATH}/${matterId}/all`;
      if (templateId) {
        url += `?templateId=${templateId}`;
      }
      await authPost(url, {});
    },
    [authPost]
  );

  const funcs = {
    getTasks,
    getTaskCount,
    createTasks,
  };
  return funcs;
}
