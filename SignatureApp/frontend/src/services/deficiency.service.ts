import { useCallback } from "react";
import { Deficiency, DeficiencyWithRule, FullSheetDeficiency } from "../types/deficiency.types";
import { useAuthenticatedRequestService } from "./service.base";

export function useDeficiencyService() {
  const DEFICIENCIES_PATH = "deficiencies";
  const { authGet, authGetFile, authPost, authDelete } = useAuthenticatedRequestService();

  const getDeficiencyWithRuleById = useCallback(
    async function (matterId: string, deficiencyId: string): Promise<DeficiencyWithRule> {
      return await authGet(`matters/${matterId}/${DEFICIENCIES_PATH}/${deficiencyId}`);
    },
    [authGet]
  );

  const getSheetDeficienciesById = useCallback(
    async function (sheetId: number): Promise<Deficiency[]> {
      return await authGet(`sheets/${sheetId}/deficiencies`);
    },
    [authGet]
  );

  const getOnlyMatterDeficienciesById = useCallback(
    async function (matterId: string): Promise<Deficiency[]> {
      return await authGet(`matters/${matterId}/${DEFICIENCIES_PATH}/matter`);
    },
    [authGet]
  );

  const getAllMatterDeficienciesById = useCallback(
    async function (matterId: string): Promise<Deficiency[]> {
      return await authGet(`matters/${matterId}/deficiencies`);
    },
    [authGet]
  );

  const getDeficienciesByRuleId = useCallback(
    async function (matterId: string, ruleId: string): Promise<Deficiency[]> {
      return await authGet(`matters/${matterId}/${DEFICIENCIES_PATH}/rules/${ruleId}`);
    },
    [authGet]
  );

  const getFullSheetDeficienciesById = useCallback(
    async function (matterId: string): Promise<FullSheetDeficiency[]> {
      return await authGet(`matters/${matterId}/${DEFICIENCIES_PATH}/full`, {
        timeout: 5 * 60 * 1000,
      }); // 5 mins
    },
    [authGet]
  );

  const getDeficiencyCsv = useCallback(
    async function (matterId: string): Promise<string> {
      return await authGet(`matters/${matterId}/${DEFICIENCIES_PATH}/csv`);
    },
    [authGet]
  );

  const getDeficiencyWord = useCallback(
    async function (matterId: string): Promise<Blob> {
      var response = await authGetFile(`matters/${matterId}/${DEFICIENCIES_PATH}/word`);
      return response.blob();
    },
    [authGetFile]
  );

  const getDeficiencyPdf = useCallback(
    async function (matterId: string): Promise<Blob> {
      var response = await authGetFile(`matters/${matterId}/${DEFICIENCIES_PATH}/pdf`);
      return response.blob();
    },
    [authGetFile]
  );

  const postMatterDeficiency = useCallback(
    async function (matterId: string, ruleId: number, note: string | null): Promise<Deficiency> {
      return await authPost(`matters/${matterId}/${DEFICIENCIES_PATH}/${ruleId}`, { note });
    },
    [authPost]
  );

  const postSheetDeficiency = useCallback(
    async function (sheetId: number, ruleId: number, note: string | null): Promise<Deficiency> {
      console.log(`sheets/${sheetId}/${DEFICIENCIES_PATH}/${ruleId}`, "path");
      return await authPost(`sheets/${sheetId}/${DEFICIENCIES_PATH}/${ruleId}`, { note });
    },
    [authPost]
  );

  const clearMatterDeficiency = useCallback(
    async function (matterId: string): Promise<Deficiency> {
      return await authPost(`matters/${matterId}/${DEFICIENCIES_PATH}/clear`, {});
    },
    [authPost]
  );

  const deleteMatterDeficiency = useCallback(
    async function (matterId: string, ruleId: number): Promise<void> {
      return await authDelete(`matters/${matterId}/${DEFICIENCIES_PATH}/${ruleId}`);
    },
    [authDelete]
  );

  const deleteSheetDeficiency = useCallback(
    async function (sheetId: number, ruleId: number): Promise<void> {
      return await authDelete(`sheets/${sheetId}/${DEFICIENCIES_PATH}/${ruleId}`);
    },
    [authDelete]
  );

  const funcs = {
    getAllMatterDeficienciesById,
    getOnlyMatterDeficienciesById,
    getDeficiencyWithRuleById,
    getDeficienciesByRuleId,
    getSheetDeficienciesById,
    getFullSheetDeficienciesById,
    getDeficiencyCsv,
    getDeficiencyWord,
    getDeficiencyPdf,
    postMatterDeficiency,
    postSheetDeficiency,
    clearMatterDeficiency,
    deleteMatterDeficiency,
    deleteSheetDeficiency,
  };
  return funcs;
}
