import { useCallback } from "react";
import { cloneDeep } from "lodash";
import {
  SignatureTable,
  SignatureSheetTable,
} from "../types/signature-sheet.types";
import { useAuthenticatedRequestService } from "./service.base";

export function useSignatureSheetService() {
  const PATH = "signaturesheets/";
  const { authGet, authPost } = useAuthenticatedRequestService();

  const getTableByMatterAndSheetNumber = useCallback(
    async function (
      matterId: string,
      sheetNumber: string
    ): Promise<SignatureSheetTable> {
      return await authGet(`${PATH}${matterId}/${sheetNumber}`);
    },
    [authGet]
  );

  const getSheetNumbersByMatter = useCallback(
    async function (matterId: string): Promise<number[]> {
      return await authGet(`${PATH}${matterId}/sheetNumbers`);
    },
    [authGet]
  );

  const postAdjustedSheet = useCallback(
    async function (
      matterId: string,
      sheetNumber: string,
      tableData: SignatureTable
    ) {
      console.debug(
        `posting table ${tableData.columns[0].left} ${
          tableData.columns[3].left
        } ${tableData.columns[3].right} ${
          tableData.columns[tableData.columns.length - 1].right
        } `
      );

      const body = cloneDeep(tableData);
      body.sheetImage = null;

      return await authPost(`${PATH}${matterId}/${sheetNumber}`, body);
    },
    [authPost]
  );

  const funcs = {
    getTableByMatterAndSheetNumber,
    getSheetNumbersByMatter,
    postAdjustedSheet,
  };
  return funcs;
}
