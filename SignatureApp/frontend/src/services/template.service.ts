import { useCallback } from "react";
import {
  GetNextSignatureTableColumnDTO,
  GetTranscribableFieldDTO,
  Template,
  TemplateIgnoredWordsDTO,
  UpdateSignatureTableColumnDTO,
  UpdateTranscribableFieldDTO,
} from "../types/template.types";
import { useAuthenticatedRequestService } from "./service.base";
import {
  SignatureTable,
  TemplateSignatureTable,
} from "../types/signature-sheet.types";

export function useTemplateService() {
  const TEMPLATES_PATH = "templates";
  const { authGet, authPut, authPost, authDelete } =
    useAuthenticatedRequestService();

  const getAllTemplates = useCallback(
    async function (): Promise<Template[]> {
      return await authGet(TEMPLATES_PATH);
    },
    [authGet]
  );

  const getTemplateById = useCallback(
    async function (id: string): Promise<Template> {
      return await authGet(`${TEMPLATES_PATH}/${id}`);
    },
    [authGet]
  );

  const getTableByTemplateId = useCallback(
    async function (templateId: string): Promise<TemplateSignatureTable> {
      return await authGet(`${TEMPLATES_PATH}/${templateId}/table`);
    },
    [authGet]
  );

  const getFieldsByTemplateId = useCallback(
    async function (templateId: string): Promise<GetTranscribableFieldDTO[]> {
      return await authGet(`${TEMPLATES_PATH}/${templateId}/fields`);
    },
    [authGet]
  );

  const postAdjustedTable = useCallback(
    async function (templateId: string, tableData: SignatureTable) {
      tableData.sheetImage = null;
      return await authPost(`${TEMPLATES_PATH}/${templateId}/table`, tableData);
    },
    [authPost]
  );

  const createTemplate = useCallback(
    async function (name: string): Promise<Template | null> {
      return await authPost(TEMPLATES_PATH, { name });
    },
    [authPost]
  );

  const updateTemplate = useCallback(
    async function (id: string, name: string): Promise<Template | null> {
      return await authPut(`${TEMPLATES_PATH}/${id}`, { name });
    },
    [authPut]
  );

  const deleteTemplate = useCallback(
    async function (id: string): Promise<null> {
      return await authDelete(`${TEMPLATES_PATH}/${id}`);
    },
    [authDelete]
  );

  const getNextTranscribableField = useCallback(
    async function (
      templateId: string,
      fieldId?: string
    ): Promise<GetTranscribableFieldDTO> {
      let urlPath = `transcribablefields/${templateId}`;
      if (fieldId) {
        urlPath += "/" + fieldId;
      }
      return await authGet(urlPath);
    },
    [authGet]
  );

  const updateTranscribableField = useCallback(
    async function (
      templateId: string,
      fieldId: string,
      dto: UpdateTranscribableFieldDTO
    ): Promise<GetTranscribableFieldDTO> {
      let urlPath = `transcribablefields/${templateId}/${fieldId}`;
      return await authPut(urlPath, dto);
    },
    [authPut]
  );

  const getNextSignatureTableColumn = useCallback(
    async function (
      templateId: string,
      fieldId?: string
    ): Promise<GetNextSignatureTableColumnDTO> {
      let urlPath = `${TEMPLATES_PATH}/${templateId}/signaturetablecolumns`;
      if (fieldId) {
        urlPath += "/" + fieldId;
      }
      return await authGet(urlPath);
    },
    [authGet]
  );

  const updateSignatureTableColumn = useCallback(
    async function (
      templateId: string,
      fieldId: string,
      dto: UpdateSignatureTableColumnDTO
    ): Promise<GetNextSignatureTableColumnDTO> {
      return await authPut(
        `${TEMPLATES_PATH}/${templateId}/signaturetablecolumns/${fieldId}`,
        dto
      );
    },
    [authPut]
  );

  const getIgnoredWordsByTemplateId = useCallback(
    async function (templateId: string): Promise<TemplateIgnoredWordsDTO[]> {
      return await authGet(`${TEMPLATES_PATH}/${templateId}/ignoredwords`);
    },
    [authGet]
  );

  const updateTemplateIgnoredWords = useCallback(
    async function (
      templateId: string,
      newWords?: string[],
      deletedWordIds?: number[]
    ): Promise<TemplateIgnoredWordsDTO[]> {
      return await authPost(`${TEMPLATES_PATH}/${templateId}/ignoredwords`, {
        newWords,
        deletedWordIds,
      });
    },
    [authPost]
  );

  const funcs = {
    getAllTemplates,
    getTemplateById,
    getFieldsByTemplateId,
    getTableByTemplateId,
    postAdjustedTable,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    getNextTranscribableField,
    updateTranscribableField,
    getNextSignatureTableColumn,
    updateSignatureTableColumn,
    getIgnoredWordsByTemplateId,
    updateTemplateIgnoredWords,
  };
  return funcs;
}
