import { useNavigate } from "react-router-dom";
import { useAuthContext } from "../AuthProvider";
import { useEffect } from "react";
import { checkForRole } from "../common/utils";
import { Roles } from "../types/user.types";

export const useAuthGuard = (role: Roles) => {
  const navigate = useNavigate();
  const { authenticatedUser } = useAuthContext();

  useEffect(() => {
    if (!checkForRole(role, authenticatedUser?.roleId)) {
      navigate("/login");
    }
  });
};
