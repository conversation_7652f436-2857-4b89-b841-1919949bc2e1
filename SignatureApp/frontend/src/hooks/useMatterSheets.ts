import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useSignatureSheetService } from "../services/signature-sheet.service";
import { SignatureSheetTable } from "../types/signature-sheet.types";
import { fixColumnLeftAndRight } from "../common/utils";
import { useWorkService } from "../services/work.service";

export function useMatterSheets() {
  const navigate = useNavigate();
  const { matterId, sheetNumber } = useParams();

  const [showNoSheets, setShowNoSheets] = useState(false);
  const [sheetHasUnavailableWork, setSheetHasUnavailableWork] = useState(false);

  const [sheetNumbers, setSheetNumbers] = useState<number[] | null>(null);
  const [tableData, setTableData] = useState<SignatureSheetTable>();
  const {
    getTableByMatterAndSheetNumber,
    getSheetNumbersByMatter,
    postAdjustedSheet,
  } = useSignatureSheetService();
  const { getUnavailableWorkCount } = useWorkService();

  // Fetch valid sheet data
  useEffect(() => {
    if (!matterId || !sheetNumber) return;

    getTableByMatterAndSheetNumber(matterId, sheetNumber).then((table) => {
      fixColumnLeftAndRight(table.columns);
      setTableData(table);
    });
  }, [matterId, sheetNumber, getTableByMatterAndSheetNumber]);

  useEffect(() => {
    if (!matterId) return;
    getSheetNumbersByMatter(matterId)
      .then((data) => {
        if (data && data.length > 0) {
          setSheetNumbers(data);
          if (!sheetNumber) {
            navigate(`/admin/matters/${matterId}/sheets/${data[0]}`);
          }
        }
      })
      .catch(() => setShowNoSheets(true));
  }, [matterId, sheetNumber, getSheetNumbersByMatter, navigate]);

  useEffect(() => {
    if (!matterId || !sheetNumber) return;

    getUnavailableWorkCount(matterId, sheetNumber).then((count) => {
      setSheetHasUnavailableWork(count > 0);
    });
  }, [matterId, sheetNumber, getUnavailableWorkCount]);

  return {
    matterId,
    sheetNumber,
    showNoSheets,
    sheetNumbers,
    tableData,
    setTableData,
    postAdjustedSheet,
    sheetHasUnavailableWork,
  };
}
