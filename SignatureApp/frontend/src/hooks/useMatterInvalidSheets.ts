import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { InvalidSheet } from "../types/signature-sheet.types";
import { useInvalidSheetService } from "../services/invalid-sheet.service";

export function useMatterInvalidSheets() {
  const { matterId } = useParams();

  const [templateId, setTemplateId] = useState<number | undefined>();
  const [currentInvalidSheet, setCurrentInvalidSheet] =
    useState<InvalidSheet>();
  const [isQueueEmpty, setIsQueueEmpty] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { getNextInvalidSheet, getInvalidSheetImageById } =
    useInvalidSheetService();

  const [invalidFrontImage, setInvalidFrontImage] = useState<string | null>(
    null
  );
  const [invalidBackImage, setInvalidBackImage] = useState<string | null>(null);

  // Fetch invalid sheet image
  useEffect(() => {
    if (!matterId || !currentInvalidSheet) return;

    getInvalidSheetImageById(
      matterId,
      currentInvalidSheet.id.toString(),
      1
    ).then((blob) => {
      setInvalidFrontImage(URL.createObjectURL(blob));
    });
    getInvalidSheetImageById(
      matterId,
      currentInvalidSheet.id.toString(),
      2
    ).then((blob) => {
      setInvalidBackImage(URL.createObjectURL(blob));
    });
  }, [matterId, currentInvalidSheet, getInvalidSheetImageById]);

  // Fetch invalid sheets
  useEffect(() => {
    if (!matterId) return;
    if (!currentInvalidSheet && !isQueueEmpty) {
      setIsLoading(true);
      getNextInvalidSheet(matterId)
        .then((data) => {
          setTemplateId(data.templateId);
          setCurrentInvalidSheet(data);
          setIsQueueEmpty(false);
        })
        .catch((error) => {
          // Handle 404 - no more invalid sheets available for this user
          if (error.response?.status === 404) {
            setIsQueueEmpty(true);
            setCurrentInvalidSheet(undefined);
          } else {
            console.error("Error fetching next invalid sheet:", error);
          }
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [currentInvalidSheet, getNextInvalidSheet, matterId, isQueueEmpty]);

  return {
    templateId,
    currentInvalidSheet,
    setCurrentInvalidSheet,
    invalidFrontImage,
    invalidBackImage,
    isQueueEmpty,
    isLoading,
  };
}
