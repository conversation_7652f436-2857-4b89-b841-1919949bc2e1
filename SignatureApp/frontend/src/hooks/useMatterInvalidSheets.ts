import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { InvalidSheet } from "../types/signature-sheet.types";
import { useInvalidSheetService } from "../services/invalid-sheet.service";

export function useMatterInvalidSheets() {
  const { matterId } = useParams();

  const [templateId, setTemplateId] = useState<number | undefined>();
  const [currentInvalidSheet, setCurrentInvalidSheet] =
    useState<InvalidSheet>();
  const { getNextInvalidSheet, getInvalidSheetImageById } =
    useInvalidSheetService();

  const [invalidFrontImage, setInvalidFrontImage] = useState<string | null>(
    null
  );
  const [invalidBackImage, setInvalidBackImage] = useState<string | null>(null);

  // Fetch invalid sheet image
  useEffect(() => {
    if (!matterId || !currentInvalidSheet) return;

    getInvalidSheetImageById(
      matterId,
      currentInvalidSheet.id.toString(),
      1
    ).then((blob) => {
      setInvalidFrontImage(URL.createObjectURL(blob));
    });
    getInvalidSheetImageById(
      matterId,
      currentInvalidSheet.id.toString(),
      2
    ).then((blob) => {
      setInvalidBackImage(URL.createObjectURL(blob));
    });
  }, [matterId, currentInvalidSheet, getInvalidSheetImageById]);

  // Fetch invalid sheets
  useEffect(() => {
    if (!matterId) return;
    if (!currentInvalidSheet) {
      getNextInvalidSheet(matterId).then((data) => {
        setTemplateId(data.templateId);
        setCurrentInvalidSheet(data);
      });
    }
  }, [currentInvalidSheet, getNextInvalidSheet, matterId]);

  return {
    templateId,
    currentInvalidSheet,
    setCurrentInvalidSheet,
    invalidFrontImage,
    invalidBackImage,
  };
}
