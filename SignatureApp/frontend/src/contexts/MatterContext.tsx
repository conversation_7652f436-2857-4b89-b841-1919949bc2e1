import { createContext, useContext, useState, ReactNode, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useMatterService } from "../services/matter.service";
import { Matter } from "../types/matter.types";

interface MatterContextType {
  matterId: string | undefined;
  matterName: string | null;
  matter: Matter | null;
  loading: boolean;
  error: string | null;
  setMatterName: (name: string | null) => void;
}

const MatterContext = createContext<MatterContextType | undefined>(undefined);

export const MatterProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [matterName, setMatterName] = useState<string | null>(null);
  const [matter, setMatter] = useState<Matter | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getMatterById } = useMatterService();
  const params = useParams();
  const matterId = params?.matterId;

  useEffect(() => {
    if (matterId && matterId !== "new") {
      setLoading(true);
      setError(null);
      getMatterById(matterId).then(
        (fetchedMatter) => {
          setMatter(fetchedMatter);
          setMatterName(fetchedMatter.name);
          setLoading(false);
        },
        (error) => {
          setError(error.message || "Failed to fetch matter");
          setLoading(false);
        }
      );
    } else if (matterId === "new") {
      setMatter(null);
      setMatterName(null);
      setLoading(false);
      setError(null);
    }
  }, [matterId, getMatterById]);

  return (
    <MatterContext.Provider value={{ matterId, matterName, matter, loading, error, setMatterName }}>
      {children}
    </MatterContext.Provider>
  );
};

export function useMatterContext( optional: boolean = false ) {
  const context = useContext(MatterContext);
  if (context === undefined && !optional) {
    throw new Error("useMatterContext must be used within a MatterProvider");
  }

  return context || {
    matterId: undefined,
    matterName: null,
    matter: null,
    loading: false,
    error: null,
    setMatterName: () => {}
  };
}
