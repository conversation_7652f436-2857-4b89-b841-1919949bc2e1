import { Signatory } from "../types/external-data.types";

export interface SignatoryViewProps {
  signatory: Signatory;
}

export default function SignatoryView({ signatory }: SignatoryViewProps) {
  return (
    <>
      <div className="row">
        <label className="col">First Name:</label>
        <input type="text" className="col" value={signatory.firstName} readOnly />
      </div>
      <div className="row">
        <label className="col">Last Name:</label>
        <input type="text" className="col" value={signatory.lastName} readOnly />
      </div>
      <div className="row">
        <label className="col">Address Line:</label>
        <input type="text" className="col" value={signatory.address ?? ""} />
      </div>
      <div className="row">
        <label className="col">City, State, Zip:</label>
        <input
          type="text"
          className="col"
          value={`${signatory.city}, ${signatory.state} ${signatory.postalCode}`}
          readOnly
        />
      </div>
    </>
  );
}
