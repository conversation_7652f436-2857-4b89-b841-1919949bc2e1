import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { Work, WorkDto } from "../types/work.types";
import { useWorkService } from "../services/work.service";
import { PreviewWorkItems } from "./preview-work-items";
import HeaderNavBar from "./header-nav-bar";

interface PreviewFieldWorkItemsProps {
  isRelease: boolean;
  workItems?: Work[];
  workReleasable?: boolean[];
  setWorkReleasable?: (updater: (prev: boolean[]) => boolean[]) => void;
}

export function PreviewFieldWorkItems({
  isRelease,
  workItems,
  workReleasable,
  setWorkReleasable,
}: PreviewFieldWorkItemsProps) {
  const { matterId, sheetNumber } = useParams();
  const { getPreviewFieldWorkItems } = useWorkService();
  const [internalWorkItems, setInternalWorkItems] = useState<WorkDto[]>([]);
  const [internalWorkReleasable, setInternalWorkReleasable] = useState<boolean[]>(workReleasable || []);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (workItems && workReleasable) {
      // Use external props - no need to fetch or set internal state
      return;
    }
    if (!matterId || !sheetNumber) return;
    setIsLoading(true);
    getPreviewFieldWorkItems(matterId, sheetNumber).then((workItems) => {
      setIsLoading(false);
      setInternalWorkItems(workItems);
      setInternalWorkReleasable(workItems.map((_) => true));
    });
  }, [matterId, sheetNumber, getPreviewFieldWorkItems, workItems, workReleasable]);

  return (
    <div className="container">
      <HeaderNavBar backTo="sheet" />

      <PreviewWorkItems
        isRelease={isRelease}
        isLoading={isLoading}
        workItems={internalWorkItems}
        workReleasable={workReleasable || internalWorkReleasable}
        setWorkReleasable={setWorkReleasable || setInternalWorkReleasable}
      />
    </div>
  );
}
