import { useState, useEffect, useMemo } from "react";
import { useParams } from "react-router-dom";

export type PaginationProps = {
  sheetNumbers: number[];
  onNavigate: (sheetNumber: number) => void;
};

export default function Pagination({
  sheetNumbers,
  onNavigate,
}: PaginationProps) {
  const params = useParams();

  const minSheetNumber = useMemo(() => sheetNumbers[0] ?? 1, [sheetNumbers]);
  const maxSheetNumber = useMemo(
    () => sheetNumbers[sheetNumbers.length - 1] ?? 1,
    [sheetNumbers]
  );

  const sheetNumber = Number(Object.values(params)?.[1]) || minSheetNumber;

  const currentIndex = useMemo(() => {
    return sheetNumbers.findIndex((num) => num === sheetNumber);
  }, [sheetNumbers, sheetNumber]);

  const [inputValue, setInputValue] = useState(sheetNumber.toString());
  const [error, setError] = useState("");

  useEffect(() => {
    setInputValue(sheetNumber.toString());
  }, [sheetNumber]);

  const handleNavigateByIndex = (direction: "prev" | "next") => {
    if (currentIndex === -1) {
      onNavigate(sheetNumbers[0]);
      return;
    }

    if (direction === "prev" && currentIndex > 0) {
      onNavigate(sheetNumbers[currentIndex - 1]);
    } else if (direction === "next" && currentIndex < sheetNumbers.length - 1) {
      onNavigate(sheetNumbers[currentIndex + 1]);
    }
  };

  const handleNavigate = (value: number) => {
    if (!sheetNumbers.includes(value)) {
      setError(
        `Sheet ${value} doesn't exist` +
          (sheetNumbers.length < 20
            ? `, available sheets: ${sheetNumbers.join(", ")}`
            : "")
      );
      return;
    }
    onNavigate(value);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
  };

  const handleInputBlur = () => {
    const numValue = parseInt(inputValue, 10);
    if (!isNaN(numValue)) {
      setError("");
      handleNavigate(numValue);
    } else {
      setError("Please enter a valid number.");
      setInputValue(sheetNumber.toString());
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleInputBlur();
    }
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "8px",
        alignItems: "center",
      }}
    >
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <button
          onClick={() => handleNavigateByIndex("prev")}
          disabled={currentIndex <= 0}
        >
          &lt;
        </button>
        <input
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyPress}
          style={{
            textAlign: "center",
            padding: "4px",
            width: "100px",
            border: error ? "1px solid red" : "1px solid #ccc",
          }}
        />
        <button
          onClick={() => handleNavigateByIndex("next")}
          disabled={currentIndex >= sheetNumbers.length - 1}
        >
          &gt;
        </button>
      </div>

      <div style={{ fontSize: "14px", color: "#555" }}>
        {`${minSheetNumber} - ${maxSheetNumber} (${currentIndex + 1} of ${
          sheetNumbers.length
        })`}
      </div>

      {error && <span style={{ color: "red", fontSize: "12px" }}>{error}</span>}
    </div>
  );
}
