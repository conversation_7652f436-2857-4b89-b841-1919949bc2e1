import { useMemo } from "react";

interface ChooseRowsColumnsProps {
  columnHeaders: string[];
  numberOfRows: number;
  validRows: boolean[];
  validCols: boolean[];
  setValidRows: (validRows: boolean[]) => void;
  setValidCols: (validCols: boolean[]) => void;
}

export default function ChooseRowsColumns({
  columnHeaders,
  numberOfRows,
  validRows,
  validCols,
  setValidRows,
  setValidCols,
}: ChooseRowsColumnsProps) {
  const style: React.CSSProperties = useMemo(() => {
    return {
      border: "1px solid black",
      borderCollapse: "collapse",
      minHeight: "30px",
      minWidth: "50px",
    };
  }, []);

  function getCellStyle(
    rowIndex: number,
    colIndex: number
  ): React.CSSProperties {
    return {
      ...style,
      backgroundColor:
        validRows[rowIndex] && validCols[colIndex]
          ? "rgba(0, 255, 0, 0.3)"
          : "white",
    };
  }

  function handleColumnClick(colIndex: number) {
    const newCols = [...validCols];
    newCols[colIndex] = !newCols[colIndex];
    setValidCols(newCols);
  }
  function handleRowClick(rowIndex: number) {
    const newRows = [...validRows];
    newRows[rowIndex] = !newRows[rowIndex];
    setValidRows(newRows);
  }

  if (columnHeaders.length === 0 || numberOfRows === 0) {
    return null;
  }

  return (
    <table style={style}>
      <tbody>
        <tr style={style}>
          <td style={style}></td>
          {columnHeaders.map((header, colIndex) => (
            <td key={header} style={style}>
              <input
                className="ms-1"
                type="checkbox"
                checked={validCols[colIndex]}
                onChange={() => handleColumnClick(colIndex)}
              />
              <label className="ms-1 me-1">{header}</label>
            </td>
          ))}
        </tr>
        {validRows.map((rowChecked, rowIndex) => (
          <tr key={`row-${rowIndex}`} style={style}>
            <td style={style}>
              <input
                className="ms-1"
                type="checkbox"
                checked={rowChecked}
                onChange={() => handleRowClick(rowIndex)}
              />
              <label className="ms-1 me-1">{rowIndex + 1}</label>
            </td>
            {columnHeaders.map((_, colIndex) => (
              <td
                key={`${rowIndex}-${colIndex}`}
                style={getCellStyle(rowIndex, colIndex)}
              />
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
}
