import { useCallback, useMemo, useState } from "react";

interface ChooseRowsColumnsProps {
  columnHeaders: string[];
  numberOfRows: number;
  validRows: boolean[];
  validCols: boolean[];
  setValidRows: (validRows: boolean[]) => void;
  setValidCols: (validCols: boolean[]) => void;
}

export default function ChooseRowsColumns({
  columnHeaders,
  numberOfRows,
  validRows,
  validCols,
  setValidRows,
  setValidCols,
}: ChooseRowsColumnsProps) {
  const rowHeaders = useMemo(() => Array(numberOfRows).fill(true), [numberOfRows]);

  const getIsColumnDisabled = useCallback((cols: boolean[], colIndex: number): boolean => {
    const leftSelected = colIndex > 0 ? cols[colIndex - 1] : false;
    const rightSelected = colIndex < cols.length - 1 ? cols[colIndex + 1] : false;

    if (cols[colIndex]) {
      // If checked, disable if both neighbors are selected (would break contiguity)
      return leftSelected && rightSelected;
    } else {
      // If unchecked, disable if neither neighbor is selected (would create non-contiguous block)
      return !leftSelected && !rightSelected;
    }
  }, []);

  const getIsRowDisabled = useCallback((rows: boolean[], rowIndex: number): boolean => {
    const topSelected = rowIndex > 0 ? rows[rowIndex - 1] : false;
    const bottomSelected = rowIndex < rows.length - 1 ? rows[rowIndex + 1] : false;
    if (rows[rowIndex]) {
      // If checked, disable if both neighbors are selected (would break contiguity)
      return topSelected && bottomSelected;
    } else {
      // If unchecked, disable if neither neighbor is selected (would create non-contiguous block)
      return !topSelected && !bottomSelected;
    }
  }, []);

  const [rowsDisabled, setRowsDisabled] = useState<boolean[]>(
    validRows.map((_, index) => getIsRowDisabled(validRows, index))
  );
  const [colsDisabled, setColsDisabled] = useState<boolean[]>(
    validCols.map((_, index) => getIsColumnDisabled(validCols, index))
  );

  const style: React.CSSProperties = useMemo(() => {
    return {
      border: "1px solid black",
      borderCollapse: "collapse",
      minHeight: "30px",
      minWidth: "50px",
    };
  }, []);

  function getCellStyle(rowIndex: number, colIndex: number): React.CSSProperties {
    return {
      ...style,
      backgroundColor: validRows[rowIndex] && validCols[colIndex] ? "rgba(0, 255, 0, 0.3)" : "white",
    };
  }

  function handleColumnClick(colIndex: number) {
    const newCols = [...validCols];
    newCols[colIndex] = !newCols[colIndex];
    setValidCols(newCols);
    var newColsDisabled = columnHeaders.map((_, index) => getIsColumnDisabled(newCols, index));
    setColsDisabled(newColsDisabled);
  }
  function handleRowClick(rowIndex: number) {
    const newRows = [...validRows];
    newRows[rowIndex] = !newRows[rowIndex];
    setValidRows(newRows);
    var newRowsDisabled = rowHeaders.map((_, index) => getIsRowDisabled(newRows, index));
    setRowsDisabled(newRowsDisabled);
  }

  if (columnHeaders.length === 0 || numberOfRows === 0) {
    return null;
  }

  return (
    <table style={style}>
      <tbody>
        <tr style={style}>
          <td style={style}></td>
          {columnHeaders.map((header, colIndex) => (
            <td key={header} style={style}>
              <input
                className="ms-1"
                type="checkbox"
                checked={validCols[colIndex]}
                disabled={colsDisabled[colIndex]}
                onChange={() => handleColumnClick(colIndex)}
              />
              <label className="ms-1 me-1">{header}</label>
            </td>
          ))}
        </tr>
        {validRows.map((_, rowIndex) => (
          <tr key={`row-${rowIndex}`} style={style}>
            <td style={style}>
              <input
                className="ms-1"
                type="checkbox"
                checked={validRows[rowIndex]}
                disabled={rowsDisabled[rowIndex]}
                onChange={() => handleRowClick(rowIndex)}
              />
              <label className="ms-1 me-1">{rowIndex + 1}</label>
            </td>
            {columnHeaders.map((_, colIndex) => (
              <td key={`${rowIndex}-${colIndex}`} style={getCellStyle(rowIndex, colIndex)} />
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
}
