import { faTrashCan } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

type TemplateIgnoredWordRowProps = {
  id: number;
  word: string;
  onDelete?: (wordId: number) => void;
};

export function TemplateIgnoredWordRow({
  id,
  word,
  onDelete,
}: TemplateIgnoredWordRowProps) {
  const canDelete = !!onDelete;
  return (
    <li
      className={`list-group-item d-flex align-items-center text-truncate ${
        canDelete ? "px-0" : "px-2"
      }`}
    >
      {canDelete && (
        <button
          className="btn sw-maroon pt-0 pb-0"
          onClick={() => onDelete(id)}
        >
          <FontAwesomeIcon className="sw-maroon" icon={faTrashCan} />
        </button>
      )}
      <span>{word}</span>
    </li>
  );
}
