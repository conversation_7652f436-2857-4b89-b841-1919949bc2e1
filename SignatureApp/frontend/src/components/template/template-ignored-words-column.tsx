import { Fragment } from "react/jsx-runtime";
import { TemplateIgnoredWordsDTO } from "../../types/template.types";
import { TemplateIgnoredWordRow } from "./template-ignored-word-row";

type TemplateIgnoredWordsProps = {
  words: TemplateIgnoredWordsDTO[];
  onDelete: (wordId: number) => void;
};

export const TemplateIgnoredWordsColumn = ({
  words,
  onDelete,
}: TemplateIgnoredWordsProps) => (
  <div className="col-sm-12 col-lg-6">
    <ul className="list-group">
      {words.map(({ id, word }) => (
        <Fragment key={id}>
          <TemplateIgnoredWordRow id={id} word={word} onDelete={onDelete} />
        </Fragment>
      ))}
    </ul>
  </div>
);
