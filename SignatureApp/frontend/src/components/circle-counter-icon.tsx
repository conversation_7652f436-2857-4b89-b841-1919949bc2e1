import { faCircle } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Tooltip } from "bootstrap";
import { useEffect, useRef } from "react";

const MAX_COUNT = 99;

type CircleCounterProps = {
  count: number;
};

export default function CircleCounterIcon({ count }: CircleCounterProps) {
  const isOverMax = count > MAX_COUNT;
  const limitedCount = isOverMax ? MAX_COUNT : count < 0 ? 0 : count;
  const tooltipRef = useRef<HTMLSpanElement | null>(null);

  useEffect(() => {
    const tooltipEle = tooltipRef.current;
    let tooltip: Tooltip | null = null;
    if (tooltipEle && isOverMax) {
      tooltip = new Tooltip(tooltipEle);
    }

    return () => {
      if (tooltip) {
        tooltip.dispose();

        /*
         * Remove tooltip attributes from the element so that the
         * native browser tooltip isn't shown
         */
        if (tooltipEle) {
          tooltipEle.removeAttribute("data-bs-original-title");
          tooltipEle.removeAttribute("title");
        }
      }
    };
  }, [isOverMax]);

  return (
    <span className="fa-layers fa-fw">
      <FontAwesomeIcon
        icon={faCircle}
        transform={{ size: 24 }}
        className="me-2"
      />
      <span
        className="fa-layers-text fa-inverse fw-bold"
        title={isOverMax ? count.toString() : undefined}
        ref={tooltipRef}
        style={{
          fontSize: 10,
          left: "13%",
          padding: "4px 5px",
        }}
      >
        {limitedCount}
      </span>
      {isOverMax && (
        <span
          className="fa-layers-counter fw-bold"
          style={{
            fontSize: 30,
            top: "-2.6px",
            right: "1.2px",
            backgroundColor: "transparent",
          }}
        >
          +
        </span>
      )}
    </span>
  );
}
