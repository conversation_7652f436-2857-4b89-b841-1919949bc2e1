import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Template, SupportedPageSize } from "../types/template.types";
import { useReferenceDataService } from "../services/reference-data-service";
import { UsState } from "../types/reference-data.types";
import UploadFiles from "./upload-files.component";
import SelectTemplatePageSize from "./select-template-pagesize.component";
import { InlineSpinner } from "./inline-spinner";
interface TemplateUploadsProps {
  template: Template;
  refreshTemplateInfo: () => void;
}
export default function TemplateUploads({
  template,
  refreshTemplateInfo,
}: TemplateUploadsProps) {
  const [processing, setProcessing] = useState(false);
  const [pageSize, setPageSize] = useState<SupportedPageSize>(
    SupportedPageSize.Unknown
  );
  const { getStates } = useReferenceDataService();
  const [states, setStates] = useState<UsState[]>([]);
  const [selectedStateId, setSelectedStateId] = useState<string>("");
  const [showValidationError, setShowValidationError] = useState(false);

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;

    if (value === "") {
      setPageSize(SupportedPageSize.Unknown);
    } else {
      setPageSize(value as SupportedPageSize);
    }
  };

  const handleFileSelected = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isPageSizeValid = pageSize !== SupportedPageSize.Unknown;
    const isStateSelected = selectedStateId !== "";

    if (!isPageSizeValid || !isStateSelected) {
      setShowValidationError(true);
      e.target.value = "";
      return;
    }
    setShowValidationError(false);
  };

  useEffect(() => {
    getStates().then((data) => setStates(data));
  }, [getStates]);

  function mapPageSizeValue(value: number | string): SupportedPageSize {
    switch (value) {
      case 1:
      case "Letter":
        return SupportedPageSize.Letter;
      case 2:
      case "Legal":
        return SupportedPageSize.Legal;
      default:
        return SupportedPageSize.Unknown;
    }
  }

  useEffect(() => {
    const mapped = mapPageSizeValue(template.pageSize);
    setPageSize(mapped);
  }, [template.pageSize]);

  useEffect(() => {
    if (template.usStateId) {
      setSelectedStateId(template.usStateId.toString());
    }
  }, [template.usStateId]);

  return (
    <div className="mt-4">
      <h3>Upload Template Form</h3>

      <h6>Template Page Size</h6>
      <SelectTemplatePageSize
        onPageSizeChange={handlePageSizeChange}
        selectedPageSize={pageSize}
      />
      <h6>Template State</h6>
      <select
        data-testid="select-us-state"
        className="form-select w-50 mb-3"
        value={selectedStateId}
        onChange={(e) => setSelectedStateId(e.target.value)}
      >
        <option value="">Select State</option>
        {states?.map((state) => (
          <option key={state.id} value={state.id}>
            {state.name} ({state.abbreviation})
          </option>
        ))}
      </select>

      {showValidationError && (
        <div className="text-danger mt-2">
          Please select both a page size and a state before uploading the
          template.
        </div>
      )}

      <h6>Template File</h6>
      <UploadFiles
        uploadUrlPath={`upload/templates?templateId=${template.id}&pageSize=${pageSize}&usStateId=${selectedStateId}`}
        fileTypes=".pdf, .tif, .tiff, .zip"
        isSynchronousUpload={true}
        onUploadProcessing={() => {
          setProcessing(true);
          setShowValidationError(false);
        }}
        onUploadComplete={() => {
          refreshTemplateInfo();
          setProcessing(false);
        }}
        onUploadError={() => setProcessing(false)}
        onFileSelected={handleFileSelected}
      />

      {processing && (
        <div>
          <InlineSpinner />
          <span className="small">Processing file...</span>
        </div>
      )}
      {template?.fileName ? (
        <>
          {!processing && (
            <>
              <span className="small">
                {`${template?.fileName} uploaded by ${template?.uploadedBy} on ${template?.uploadedOn}.`}
              </span>
              <div className="d-flex mt-3">
                <Link
                  to={`/admin/templates/${template.id}/table-review`}
                  className="btn btn-sw me-3"
                >
                  Table Review
                </Link>

                <Link
                  to={`/admin/templates/${template.id}/field-identification`}
                  className="btn btn-sw me-3"
                >
                  Identify Fields
                </Link>

                <Link
                  to={`/admin/templates/${template.id}/column-identification`}
                  className="btn btn-sw me-3"
                >
                  Identify Columns
                </Link>

                <Link
                  to={`/admin/templates/${template.id}/ignored-words`}
                  className="btn btn-sw"
                >
                  Ignored Words
                </Link>
              </div>
              {template?.fieldsModifiedBy && template?.fieldsModifiedOn && (
                <div>
                  <span className="small">
                    {`Fields were identified by ${template?.fieldsModifiedBy} on ${template?.fieldsModifiedOn}.`}
                  </span>
                </div>
              )}

              {template?.columnsModifiedBy && template?.columnsModifiedOn && (
                <div>
                  <span className="small">
                    {`Columns were identified by ${template?.columnsModifiedBy} on ${template?.columnsModifiedOn}.`}
                  </span>
                </div>
              )}
            </>
          )}
        </>
      ) : (
        <span className="small">No template uploaded</span>
      )}
    </div>
  );
}
