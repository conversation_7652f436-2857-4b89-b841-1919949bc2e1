import { useEffect, useState } from "react";
import { useSignatureSheetService } from "../services/signature-sheet.service";
import { SignatureSheetTable } from "../types/signature-sheet.types";
import { ResizableTable } from "./resizable-table";
import { WorkField } from "../types/work.types";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { InlineSpinner } from "./inline-spinner";
import { fixColumnLeftAndRight } from "../common/utils";

type SignatureTableValidationProps = {
  matterId: string;
  sheetNumber: string;
  width: number;
  onNext?: (fields: WorkField[]) => void;
};

export default function SignatureTableValidation({
  matterId,
  sheetNumber,
  width,
  onNext,
}: SignatureTableValidationProps) {
  const [tableData, setTableData] = useState<SignatureSheetTable>();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const { getTableByMatterAndSheetNumber, postAdjustedSheet } =
    useSignatureSheetService();

  useEffect(() => {
    if (!matterId || !sheetNumber) {
      return;
    }

    getTableByMatterAndSheetNumber(matterId, sheetNumber).then((table) => {
      fixColumnLeftAndRight(table.columns);
      setTableData(table);
    });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const image = tableData?.sheetImage;

  if (!image) {
    return null;
  }

  const height = width / (tableData?.imageAspectRatio || 1);

  async function handleSave() {
    if (!tableData) {
      return;
    }
    setIsSubmitting(true);
    await postAdjustedSheet(matterId, sheetNumber, tableData);
    if (onNext) {
      await onNext([]);
    }
    setIsSubmitting(false);
  }

  return (
    <>
      <ResizableTable
        key={`table-${tableData.signatureSheetId}-${tableData.pageNumber}`}
        tableData={tableData}
        page={tableData.signatureSheet.pages[0]}
        widthInPx={width}
        heightInPx={height}
      />
      <div className="row text-center">
        <div className="col-sm-12 mt-1">
          <button
            type="submit"
            className="btn btn-sw"
            onClick={handleSave}
            disabled={isSubmitting}
          >
            {!isSubmitting && (
              <FontAwesomeIcon icon={faArrowRight} className="me-2" />
            )}
            {isSubmitting && <InlineSpinner />}
            Next
          </button>
        </div>
      </div>
    </>
  );
}
