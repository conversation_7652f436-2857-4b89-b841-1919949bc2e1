import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { RefObject, useCallback, useEffect, useState } from "react";
import { InputType } from "../types/template.types";
import {
  ValidationForm,
  Validity,
  WorkDto,
  WorkField,
} from "../types/work.types";
import { InlineSpinner } from "./inline-spinner";
import { useAuthContext } from "../AuthProvider";
import { Roles } from "../types/user.types";
import { checkForRole } from "../common/utils";

interface FieldValidationProps {
  work: WorkDto;
  formikRef: RefObject<FormikProps<ValidationForm>> | null;
  onNext: (fields: WorkField[]) => void;
  nextLabel: string | null;
  isSignatureTable: boolean;
  isRegistrationVerification: boolean;
}

interface KeyboardEvent {
  key: string;
  repeat: boolean;
  target: EventTarget | null;
}

export default function FieldValidation({
  work,
  onNext,
  formikRef,
  nextLabel,
  isSignatureTable,
  isRegistrationVerification,
}: FieldValidationProps) {
  const { authenticatedUser } = useAuthContext();
  const [isReviewer, setIsReviewer] = useState<boolean>(false);

  useEffect(() => {
    const isMgrOrAdmin = checkForRole(Roles.Manager, authenticatedUser?.roleId);
    setIsReviewer(!isMgrOrAdmin);
  }, [authenticatedUser]);

  const handleKeyPress = useCallback(
    (e: KeyboardEvent) => {
      if (e.repeat) return;
      if (!formikRef?.current) return;
      if (e.key && e.key.toLowerCase() === "enter") {
        formikRef?.current?.submitForm();
        return;
      }

      const nodeName = (e.target as Element)?.nodeName;
      if (nodeName !== "INPUT" && nodeName !== "TEXTAREA") {
        if (e.key && e.key.toLowerCase() === "v") {
          formikRef?.current?.setFieldValue(
            "fields[0].validity",
            Validity.Valid
          );
          handleResetStrikethroughs(
            formikRef.current!,
            0,
            formikRef.current!.values.fields[0]
          );
          return;
        }
        if (e.key && e.key.toLowerCase() === "n") {
          formikRef?.current?.setFieldValue(
            "fields[0].validity",
            Validity.Invalid
          );
          handleResetStrikethroughs(
            formikRef.current!,
            0,
            formikRef.current!.values.fields[0]
          );
          return;
        }
        if (e.key && e.key.toLowerCase() === "s") {
          formikRef?.current?.setFieldValue(
            "fields[0].validity",
            Validity.Strikethrough
          );
          handleStrikethrough(formikRef.current!);
          return;
        }
      }
    },
    [formikRef]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyPress, true);
    return () => {
      document.removeEventListener("keydown", handleKeyPress, true);
    };
  }, [handleKeyPress]);

  function resizeTextArea(this: HTMLTextAreaElement, ev: Event) {
    this.style.height = "auto";
    this.style.height = this.scrollHeight + "px";
  }

  useEffect(() => {
    document.querySelectorAll("textarea").forEach(function (textarea) {
      textarea.style.height = textarea.scrollHeight + "px";
      textarea.style.overflowY = "hidden";

      textarea.addEventListener("input", resizeTextArea);
    });

    return () => {
      document.querySelectorAll("textarea").forEach(function (textarea) {
        textarea.removeEventListener("input", resizeTextArea);
      });
    };
  }, []);

  const handleNext = (formValues: ValidationForm) => {
    onNext(formValues.fields);
  };

  const checkValidity = (value: string) => {
    if (value === Validity.Unknown.toString()) {
      return "Validity is required";
    }
    return null;
  };

  const handleStrikethrough = (formik: FormikProps<ValidationForm>) => {
    formik.values.fields.forEach((_, index) => {
      formik.setFieldValue(`fields[${index}].validity`, Validity.Strikethrough);
    });
  };

  const handleResetStrikethroughs = (
    formik: FormikProps<ValidationForm>,
    currentIndex: number,
    currentField: WorkField
  ) => {
    formik.values.fields.forEach((otherField, index) => {
      if (
        index !== currentIndex &&
        otherField.name !== currentField.name &&
        otherField.validity === Validity.Strikethrough
      ) {
        formik.setFieldValue(`fields[${index}].validity`, Validity.Unknown);
      }
    });
  };

  return (
    <>
      <div className="row text-center">
        <div className="col-sm-12">
          <img
            alt="transcribable field"
            style={{ width: "100%", border: "1px black solid" }}
            src={`data:image/png;base64,${work.image.fileContents}`}
          />
        </div>
      </div>
      <div className="row">
        <Formik
          innerRef={formikRef}
          enableReinitialize
          validateOnBlur={true}
          initialValues={
            {
              fields: work.fields,
            } as ValidationForm
          }
          onSubmit={handleNext}
        >
          {(formik) => (
            <Form>
              <div className="row text-center my-3">
                <>
                  {formik.values.fields.map((field, i) => (
                    <div
                      key={field.name}
                      data-testid="CypressOptions"
                      className={`col-sm-${Math.max(
                        12 / formik.values.fields.length,
                        4
                      )}`}
                    >
                      {field.name && (
                        <div className="form-group mt-4">
                          {field.inputType === InputType.Text && (
                            <div>
                              {/*<label>{field.name}</label>    */}
                              <Field
                                name={`fields[${i}].value`}
                                className="form-control"
                                placeholder={`Enter ${field.name} value`}
                                type="text"
                                validate={(value: string) => {
                                  if (
                                    !value &&
                                    field.validity === Validity.Valid
                                  ) {
                                    return "Value is required";
                                  }
                                  return null;
                                }}
                              />
                            </div>
                          )}
                          {field.inputType === InputType.CheckBox && (
                            <label>
                              <Field
                                name={field.name}
                                type="checkbox"
                                className="form-check-input me-2"
                                checked={formik.values.fields[i].value === "X"}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) => {
                                  const isChecked = e.target.checked;
                                  formik.setFieldValue(
                                    `fields[${i}].value`,
                                    isChecked ? "X" : " "
                                  );
                                }}
                              />
                              {field.name}
                            </label>
                          )}

                          {field.inputType === InputType.TextArea && (
                            <Field
                              as="textarea"
                              name={`fields[${i}].value`}
                              className="form-control"
                            />
                          )}
                          <ErrorMessage
                            name={`fields[${i}].value`}
                            component="div"
                            className="alert alert-danger p-2"
                          />
                        </div>
                      )}

                      {field.canBeInvalid && (
                        <>
                          <div className="form-group">
                            <div className="form-check form-check-inline">
                              <Field
                                data-testid={`${
                                  field.name
                                    ? field.name.replace(/ /g, "")
                                    : "Signature"
                                }Valid`}
                                id={`fields[${i}].valid`}
                                className="form-check-input"
                                type="radio"
                                validate={checkValidity}
                                name={`fields[${i}].validity`}
                                required
                                checked={field.validity === Validity.Valid}
                                onChange={() => {
                                  formik.setFieldValue(
                                    `fields[${i}].validity`,
                                    Validity.Valid
                                  );
                                  handleResetStrikethroughs(formik, i, field);
                                }}
                              />
                              <label
                                className="m-0"
                                htmlFor={`fields[${i}].valid`}
                              >
                                <span style={{ textDecoration: "underline" }}>
                                  V
                                </span>
                                <span>alid</span>
                              </label>
                            </div>
                            <div className="form-check form-check-inline">
                              <Field
                                data-testid={`${
                                  field.name
                                    ? field.name.replace(/ /g, "")
                                    : "Signature"
                                }Invalid`}
                                id={`fields[${i}].invalid`}
                                className="form-check-input"
                                type="radio"
                                validate={checkValidity}
                                name={`fields[${i}].validity`}
                                required
                                checked={field.validity === Validity.Invalid}
                                onChange={() => {
                                  formik.setFieldValue(
                                    `fields[${i}].validity`,
                                    Validity.Invalid
                                  );
                                  handleResetStrikethroughs(formik, i, field);
                                }}
                              />
                              <label
                                className="m-0"
                                htmlFor={`fields[${i}].invalid`}
                              >
                                <span>I</span>
                                <span style={{ textDecoration: "underline" }}>
                                  n
                                </span>
                                <span>valid</span>
                              </label>
                            </div>

                            {isSignatureTable &&
                              !isRegistrationVerification && (
                                <div className="form-check form-check-inline">
                                  <Field
                                    data-testid={`${
                                      field.name
                                        ? field.name.replace(/ /g, "")
                                        : "Signature"
                                    }Strikethrough`}
                                    id={`fields[${i}].strikethrough`}
                                    className="form-check-input"
                                    type="radio"
                                    validate={checkValidity}
                                    name={`fields[${i}].validity`}
                                    required
                                    checked={
                                      field.validity === Validity.Strikethrough
                                    }
                                    onChange={() => {
                                      handleStrikethrough(formik);
                                    }}
                                  />
                                  <label
                                    className="m-0"
                                    htmlFor={`fields[${i}].strikethrough`}
                                  >
                                    <span
                                      style={{ textDecoration: "underline" }}
                                    >
                                      S
                                    </span>
                                    <span>trike</span>
                                  </label>
                                </div>
                              )}
                          </div>

                          {field.validity === Validity.Invalid && (
                            <div className="form-group mt-2">
                              <Field
                                as="select"
                                style={{ minWidth: "25%" }}
                                className="form-control m-auto"
                                validate={(value: string) => {
                                  if (
                                    !value &&
                                    field.validity === Validity.Invalid
                                  ) {
                                    return "Violation is required";
                                  }
                                  return null;
                                }}
                                name={`fields[${i}].violationId`}
                                value={field.violationId?.toString()}
                                onChange={formik.handleChange}
                              >
                                <option value="">Select Violation</option>
                                {field.violationCriteria &&
                                  field.violationCriteria.map((criterion) => {
                                    return (
                                      <option
                                        key={criterion.ruleId}
                                        value={criterion.ruleId}
                                      >
                                        {criterion.name}
                                      </option>
                                    );
                                  })}
                              </Field>

                              <ErrorMessage
                                name={`fields[${i}].violationId`}
                                component="div"
                                className="alert alert-danger m-auto p-2"
                              />

                              <Field
                                className="form-control m-auto mt-2"
                                placeholder="Notes"
                                as="textarea"
                                name={`fields[${i}].violationNote`}
                                value={field.violationNote}
                                onChange={formik.handleChange}
                              />
                            </div>
                          )}

                          <ErrorMessage
                            name={`fields[${i}].validity`}
                            component="div"
                            className="alert alert-danger ms-auto me-auto p-2"
                          />
                        </>
                      )}
                    </div>
                  ))}
                </>
              </div>

              <div className="row text-center">
                <div className="col-sm-12 mt-1">
                  <div className="form-group">
                    <button
                      type="submit"
                      className="btn btn-sw"
                      disabled={formik.isSubmitting || !!formik.errors.fields || (!isReviewer && !formik.dirty)}
                    >
                      {!formik.isSubmitting && (
                        <FontAwesomeIcon icon={faArrowRight} className="me-2" />
                      )}
                      {formik.isSubmitting && <InlineSpinner />}
                      {nextLabel || "Next"}
                    </button>
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </>
  );
}
