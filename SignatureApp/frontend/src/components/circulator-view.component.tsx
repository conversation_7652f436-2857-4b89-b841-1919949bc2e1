import { Circulator } from "../types/external-data.types";

export interface CirculatorViewProps {
  circulator: Circulator;
}

export default function CirculatorView({ circulator }: CirculatorViewProps) {
  return (
    <>
      <div className="row">
        <label className="col">Registration ID:</label>
        <input type="text" className="col" value={circulator.registrationId ?? ""} />
      </div>
      <div className="row">
        <label className="col">Name:</label>
        <input type="text" className="col" value={circulator.name} readOnly />
      </div>
      <div className="row">
        <label className="col">Address Line:</label>
        <input type="text" className="col" value={circulator.address ?? ""} readOnly />
      </div>
      <div className="row">
        <label className="col">City, State, Zip:</label>
        <input
          type="text"
          className="col"
          value={`${circulator.city}, ${circulator.state} ${circulator.postalCode}`}
          readOnly
        />
      </div>
    </>
  );
}
