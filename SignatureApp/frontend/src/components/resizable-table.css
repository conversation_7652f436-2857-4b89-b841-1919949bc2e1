.table-background {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.table-image {
  display: block;
  width: 100%; /* Ensure it matches the container */
  height: 100%; /* Ensure it matches the container */
}

.table-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-collapse: collapse;
}

.table-overlay td {
  padding: 0;
  margin: 0;
}

.resizeLine{
  position: absolute; 
  height: 100%;
  width: 4px;
  top: 0;
  right: -2px;
  cursor: col-resize;
  &:hover{
    background-color: #0AA1DD;
  }
  &:active {
    background-color: #0AA1DD;

  }
}