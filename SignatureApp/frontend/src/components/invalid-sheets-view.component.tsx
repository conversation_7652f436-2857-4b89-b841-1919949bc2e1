import { useCallback, useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useInvalidSheetService } from "../services/invalid-sheet.service";
import { useTemplateService } from "../services/template.service";
import { useUploadFileService } from "../services/upload-file.service";
import { ExecutionStatus } from "../types/background-operation.types";
import { InvalidSheet, TemplateSignatureTable } from "../types/signature-sheet.types";
import { GetTranscribableFieldDTO } from "../types/template.types";
import ChooseRowsColumns from "./choose-rows-columns.component";
import { InlineSpinner } from "./inline-spinner";
import { Loader } from "./loader";
import UploadFiles from "./upload-files.component";

interface InvalidSheetsViewProps {
  currentInvalidSheet?: InvalidSheet;
  setCurrentInvalidSheet: (sheet: InvalidSheet) => void;
  invalidFrontImage: string | null;
  invalidBackImage: string | null;
  templateId: number | undefined;
  matterId: string | undefined;
}

enum InvalidActionMode {
  Replace = "Replace",
  MarkAsValid = "Mark as Valid",
}

const InvalidSheetsView: React.FC<InvalidSheetsViewProps> = ({
  currentInvalidSheet,
  setCurrentInvalidSheet,
  invalidFrontImage,
  invalidBackImage,
  templateId,
  matterId,
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { getTableByTemplateId, getFieldsByTemplateId } = useTemplateService();
  const { getBackgroundOperation } = useUploadFileService();
  const { markInvalidSheetAsValid } = useInvalidSheetService();

  const [backgroundId, setBackgroundId] = useState<number | null>(null);
  const [isTimerActive, setIsTimerActive] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isMarkingValid, setIsMarkingValid] = useState(false);
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);
  const [templateSignatureTable, setTemplateSignatureTable] = useState<TemplateSignatureTable | null>(null);
  const [transcribableFields, setTranscribableFields] = useState<GetTranscribableFieldDTO[]>([]);
  const [validRows, setValidRows] = useState<boolean[]>([]);
  const [validCols, setValidCols] = useState<boolean[]>([]);
  const [validFields, setValidFields] = useState<boolean[]>([]);
  const [invalidActionMode, setInvalidActionMode] = useState<InvalidActionMode>(InvalidActionMode.Replace);
  const [markValidErrors, setMarkValidErrors] = useState<string[]>([]);

  const interval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!templateId) {
      return;
    }
    getTableByTemplateId(templateId.toString())
      .then((tableData) => {
        setTemplateSignatureTable(tableData);
        setValidRows(new Array(tableData.rows.length).fill(true));
        setValidCols(new Array(tableData.columns.length).fill(true));
      })
      .catch((error) => {
        console.error("Error fetching table data:", error);
      });
    getFieldsByTemplateId(templateId.toString())
      .then((fields) => {
        setTranscribableFields(fields);
        setValidFields(new Array(fields.length).fill(true));
      })
      .catch((error) => {
        console.error("Error fetching table data:", error);
      });
  }, [templateId, getTableByTemplateId, getFieldsByTemplateId]);

  const createAndSetRedirectUrl = useCallback(
    (message: string) => {
      const pathname = location.pathname;
      const sheetNumbers = JSON.parse(message);
      const pathParts = pathname.split("/");
      pathParts[pathParts.length - 1] = sheetNumbers[0];
      const newPathname = pathParts.join("/");
      setRedirectUrl(newPathname);
    },
    [location.pathname]
  );

  const checkIsProcessingSheet = useCallback(async () => {
    if (!backgroundId) {
      return;
    }
    try {
      const backgroundOperation = await getBackgroundOperation(backgroundId);
      if (backgroundOperation.executionStatus !== ExecutionStatus.Running) {
        setIsTimerActive(false);
      }
      if (backgroundOperation.executionStatus === ExecutionStatus.Succeded) {
        setIsUploading(false);
        createAndSetRedirectUrl(backgroundOperation.message);
      }
    } catch (error) {
      return error;
    }
  }, [backgroundId, createAndSetRedirectUrl, getBackgroundOperation]);

  useEffect(() => {
    if (isTimerActive) {
      interval.current = setInterval(checkIsProcessingSheet, 2000);
    } else {
      if (interval.current) {
        clearInterval(interval.current);
      }
      interval.current = null;
    }

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, [checkIsProcessingSheet, isTimerActive]);

  function handleMarkSheetAsValid(): void {
    if (!matterId || !currentInvalidSheet) {
      return;
    }
    setIsMarkingValid(true);
    setMarkValidErrors([]);
    markInvalidSheetAsValid(matterId, currentInvalidSheet.id, {
      columns: validCols,
      rows: validRows,
      fields: validFields,
    })
      .then((sheetNumber) => {
        navigate(`/admin/matters/${matterId}/sheets/${sheetNumber}`);
      })
      .catch((error) => {
        console.error("Error marking sheet as valid:", error);
        if (error.response?.status === 400 && Array.isArray(error.response.data)) {
          setMarkValidErrors(error.response.data);
        } else {
          setMarkValidErrors(["An unexpected error occurred while marking the sheet as valid."]);
        }
      })
      .finally(() => {
        setIsMarkingValid(false);
      });
  }

  function handleChangeFieldIsPresent(e: { target: { checked: boolean } }, index: number): void {
    const newValidFields = [...validFields];
    newValidFields[index] = e.target.checked;
    setValidFields(newValidFields);
    setMarkValidErrors([]);
  }

  return (
    <>
      <div className="row mb-4">
        <div className="col-6">
          {Object.values(InvalidActionMode).map((value) => (
            <div key={value} className="form-check form-check-inline">
              <input
                id={`${value}`}
                className="form-check-input"
                type="radio"
                name={value.toString()}
                value={value}
                checked={invalidActionMode === value}
                onChange={() => {
                  console.log("Selected action mode:", value);
                  setInvalidActionMode(value);
                  setMarkValidErrors([]);
                }}
              />
              <label className="m-0" htmlFor={`${value}`}>
                {value}
              </label>
            </div>
          ))}
        </div>
      </div>
      {invalidActionMode === InvalidActionMode.MarkAsValid &&
        templateSignatureTable?.rows?.length &&
        templateSignatureTable?.columns?.length && (
          <>
            <div className="row mb-4">
              <div className="col-sm-9">
                <ChooseRowsColumns
                  numberOfRows={templateSignatureTable?.rows.length}
                  columnHeaders={templateSignatureTable?.columns?.map((c) => c.name || "") ?? []}
                  validRows={validRows}
                  setValidRows={(newRows) => {
                    setValidRows(newRows);
                    setMarkValidErrors([]);
                  }}
                  validCols={validCols}
                  setValidCols={(newCols) => {
                    setValidCols(newCols);
                    setMarkValidErrors([]);
                  }}
                />
              </div>
              <div className="col-sm-3">
                {transcribableFields
                  .filter((tf) => tf.pageNumber === 1)
                  .map((field, index) => (
                    <div className="form-check" key={field.name}>
                      <input
                        type="checkbox"
                        id={field.name}
                        name={field.name}
                        checked={validFields[index]}
                        onChange={(e) => handleChangeFieldIsPresent(e, index)}
                      ></input>
                      <label className="form-check-label ms-1" htmlFor={field.name}>
                        {field.name}
                      </label>
                    </div>
                  ))}
              </div>
            </div>
          </>
        )}
      {invalidActionMode === InvalidActionMode.Replace && (
        <div className="row mt-3">
          <UploadFiles
            uploadUrlPath={`invalidsheets/${matterId}/markreplaced/${currentInvalidSheet?.id}/${templateId}`}
            fileTypes=".pdf"
            isSynchronousUpload={true}
            onUploadProcessing={() => setIsUploading(true)}
            onUploadComplete={(opId: string) => {
              setBackgroundId(parseInt(opId));
              setIsTimerActive(true);
              return;
            }}
            onUploadError={() => {
              setIsUploading(false);
            }}
          />
          {isUploading && (
            <>
              <p>Processing uploaded sheet</p>
              <Loader />
            </>
          )}
          {redirectUrl && <a href={redirectUrl}>Go to newly uploaded sheet</a>}
        </div>
      )}

      <div className="row mt-3">
        {invalidFrontImage ? (
          <img
            src={invalidFrontImage}
            alt="Invalid front side of the sheet"
            style={{ maxWidth: "100%", height: "auto" }}
          />
        ) : (
          <p>Loading...</p>
        )}
      </div>
      <hr />
      {invalidActionMode === InvalidActionMode.MarkAsValid && (
        <div className="row mt-3">
          {transcribableFields.map(
            (field, index) =>
              field.pageNumber > 1 && (
                <div className="col" key={field.name}>
                  <div className="form-check">
                    <input
                      type="checkbox"
                      id={field.name}
                      name={field.name}
                      checked={validFields[index]}
                      onChange={(e) => handleChangeFieldIsPresent(e, index)}
                    ></input>
                    <label className="form-check-label ms-1" htmlFor={field.name}>
                      {field.name}
                    </label>
                  </div>
                </div>
              )
          )}
        </div>
      )}
      <div className="row mt-3">
        {invalidBackImage ? (
          <img
            src={invalidBackImage}
            alt="Invalid back side of the sheet"
            style={{ maxWidth: "100%", height: "auto" }}
          />
        ) : (
          <p>Loading...</p>
        )}
      </div>
      {invalidActionMode === InvalidActionMode.MarkAsValid && (
        <div className="flex-row m-4 ms-0">
          <div className="col d-flex align-items-start">
            <button className="btn btn-sw btn-outline me-3" onClick={handleMarkSheetAsValid} disabled={isMarkingValid}>
              {isMarkingValid && <InlineSpinner />}
              {InvalidActionMode.MarkAsValid}
            </button>
            {markValidErrors.length > 0 && (
              <div className="alert alert-danger mb-0 flex-grow-1" role="alert">
                <ul className="mb-0">
                  {markValidErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};
export default InvalidSheetsView;
