import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  SignatureSheetTable,
  SignatureTable,
} from "../types/signature-sheet.types";
import Pagination from "./pagination-control";
import { ResizableTable } from "./resizable-table";
import { InlineSpinner } from "./inline-spinner";

interface ValidSheetsViewProps {
  matterId?: string;
  sheetNumber?: string;
  showNoSheets: boolean;
  tableData?: SignatureSheetTable;
  setTableData: (table: SignatureSheetTable) => void;
  sheetNumbers: number[];
  onSaveAdjustedSheet: (
    matterId: string,
    sheetNumber: string,
    body: SignatureTable
  ) => Promise<void>;
}

const ValidSheetsView: React.FC<ValidSheetsViewProps> = ({
  matterId,
  sheetNumber,
  showNoSheets,
  onSaveAdjustedSheet,
  tableData,
  sheetNumbers,
}) => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  function handleNavigate(sheetNumber: number) {
    navigate(`/admin/matters/${matterId}/sheets/${sheetNumber}`, {
      replace: true,
    });
  }

  const [width, height] = useMemo(() => {
    const width = window.innerWidth - 60;
    const height = width / (tableData?.imageAspectRatio || 1);
    return [width, height];
  }, [tableData]);

  async function handleSave() {
    if (!matterId || !sheetNumber || !tableData) {
      return;
    }
    setIsSubmitting(true);
    await onSaveAdjustedSheet(matterId, sheetNumber, tableData);
    setIsSubmitting(false);
  }

  return (
    <>
      {showNoSheets ? (
        <h3>No sheets have been uploaded</h3>
      ) : (
        <>
          <div className="row d-flex justify-content-between align-items-center">
            <div className="col-auto">
              <Pagination
                sheetNumbers={sheetNumbers}
                onNavigate={handleNavigate}
              />
            </div>
            <div className="col-auto pe-0">
              <button
                type="submit"
                className="btn btn-sw"
                onClick={handleSave}
                disabled={isSubmitting}
              >
                {isSubmitting && <InlineSpinner />}
                Save
              </button>
            </div>
          </div>

          {tableData && (
            <ResizableTable
              key={`table-${tableData.signatureSheetId}-${tableData.pageNumber}`}
              tableData={tableData}
              page={tableData.signatureSheet.pages[0]}
              widthInPx={width}
              heightInPx={height}
            />
          )}
        </>
      )}
    </>
  );
};
export default ValidSheetsView;
