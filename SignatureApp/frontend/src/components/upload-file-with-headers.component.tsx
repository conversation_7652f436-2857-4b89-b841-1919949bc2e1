import { useState } from "react";
import * as <PERSON> from "papapar<PERSON>";

import UploadFiles from "./upload-files.component";
import { InlineSpinner } from "./inline-spinner";

interface UploadFileWithHeadersProps {
  filePath?: string;
  urlPath: string;
  urlIndex?: number;
  possibleHeaderChoices: string[];
  isValid: boolean;
  additionalData: { [key: string]: string };
  isCsvHeaderSet: boolean;
  onUploadComplete: (id: number) => void;
  onUploadError: (msg: string) => void;
  onSetRows: (rows: string[][]) => void;
  onSetHeaderFields: (headerFields: string[]) => void;
  isProcessing: boolean;
  processingLog: string | undefined;
  processingIndex: number | null;
}

export default function UploadFileWithHeaders(
  props: UploadFileWithHeadersProps
) {
  const [filePath, setFilePath] = useState<string>(props.filePath || "");

  const filterHeaders = (r: Papa.ParseStepResult<unknown>): string[] => {
    const regExDate = /\d{1,2}\/\d{1,2}\/\d{2,4}/;
    return r.meta.fields?.filter((f) => !regExDate.test(f)) || [];
  };

  const handleReplaceFile = () => {
    setFilePath("");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length !== 1) {
      return;
    }
    let linesParsed = 0;
    const rows: string[][] = [];
    let headerFields: string[] = [];
    Papa.parse(e?.target.files[0], {
      step: (r, p) => {
        if (linesParsed === 0) {
          headerFields = filterHeaders(r);
          if (!props.isCsvHeaderSet) {
            props.onSetHeaderFields(headerFields);
          }
        }
        linesParsed++;
        const row = [] as string[];
        const obj = r.data as any;
        for (const field of headerFields) {
          row.push(obj[field]?.toString() || "");
        }
        rows.push(row);
        // stop after 20
        if (linesParsed === 20) {
          p.abort();
        }
      },
      complete: (r) => {
        props.onSetRows(rows);
      },
      header: true,
    });
  };

  return (
    <div className="row gy-2" key={props.urlIndex}>
      <div className="col-sm-12">
        <UploadFiles
          filePath={filePath}
          uploadUrlPath={props.urlPath}
          fileTypes=".csv"
          isSynchronousUpload={false}
          isValid={() => props.isValid}
          onUploadComplete={(result) => {
            const id = parseInt(result, 10);
            props.onUploadComplete(id);
          }}
          onUploadError={(msg) => {
            props.onUploadError(msg);
          }}
          onFileSelected={handleFileChange}
          onReplaceFile={() => handleReplaceFile()}
          additionalData={props.additionalData}
        />
      </div>
      {props.isProcessing && props.urlIndex === props.processingIndex && (
        <div>
          <InlineSpinner />
          <span className="small">{props.processingLog}</span>
        </div>
      )}
    </div>
  );
}
