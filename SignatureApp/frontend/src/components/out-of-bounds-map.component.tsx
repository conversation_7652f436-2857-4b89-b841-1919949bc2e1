import { useBoundaryService } from "../services/boundary.service";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import L, { LatLngExpression } from "leaflet";
import "leaflet/dist/leaflet.css";

import icon from "leaflet/dist/images/marker-icon.png";
import iconShadow from "leaflet/dist/images/marker-shadow.png";
import { useEffect, useRef, useState } from "react";
import { BoundaryPoint } from "../types/boundary.types";

let DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow,
  iconSize: [28, 46],
  iconAnchor: [17, 46],
});

L.Marker.prototype.options.icon = DefaultIcon;

interface OutOfBoundsMapProps {
  outerBoundaryName: string;
  innerBoundaryName: string;
  point: BoundaryPoint;
}

function calculateCentroid(points: LatLngExpression[]): LatLngExpression {
  // console.time('calculateCentroid');
  if (points.length === 0) {
    // console.timeEnd('calculateCentroid');
    return { lat: 0, lng: 0 };
  }

  const totalLat = points.reduce((sum, point) => sum + (point as any).lat, 0);
  const totalLng = points.reduce((sum, point) => sum + (point as any).lng, 0);

  const result = {
    lat: totalLat / points.length,
    lng: totalLng / points.length
  };
  // console.timeEnd('calculateCentroid');
  return result;
}

function calculateZoomForBounds(points: LatLngExpression[]): number {
  // console.time('calculateZoomForBounds');
  if (points.length === 0) {
    // console.timeEnd('calculateZoomForBounds');
    return 7;
  }

  const lats = points.map(p => (p as any).lat);
  const lngs = points.map(p => (p as any).lng);

  const latDiff = Math.max(...lats) - Math.min(...lats);
  const lngDiff = Math.max(...lngs) - Math.min(...lngs);
  const maxDiff = Math.max(latDiff, lngDiff);

  //https://leafletjs.com/examples/zoom-levels/
  const zoomLevel = maxDiff > 0 ? Math.floor(Math.log2(360 / maxDiff)) : 7;

  // console.timeEnd('calculateZoomForBounds');
  return Math.max(4, Math.min(18, zoomLevel));
}

export function OutOfBoundsMap({
  outerBoundaryName,
  innerBoundaryName,
  point,
}: OutOfBoundsMapProps) {
  const { getBoundaryPointsByName } = useBoundaryService();
  const [outerBoundaryPositions, setOuterBoundaryPositions] =
    useState<LatLngExpression[]>();
  const [innerBoundaryPositions, setInnerBoundaryPositions] =
    useState<LatLngExpression[]>();
  const [pointPosition, setPointPosition] = useState<LatLngExpression>({
    lat: point.latitude,
    lng: point.longitude,
  });
  const [mapCenter, setMapCenter] = useState<LatLngExpression>({
    lat: point.latitude,
    lng: point.longitude,
  });
  const [zoomLevel, setZoomLevel] = useState<number>(7);
  const mapRef = useRef<L.Map | null>(null);

  useEffect(() => {
    getBoundaryPointsByName(3, outerBoundaryName).then((boundaryPoints) => {
      setOuterBoundaryPositions(
        boundaryPoints.map((bp) => ({ lat: bp.latitude, lng: bp.longitude }))
      );
    });
    if (!!innerBoundaryName) {
      getBoundaryPointsByName(3, innerBoundaryName).then((boundaryPoints) => {
        const innerPositions = boundaryPoints.map((bp) => ({ lat: bp.latitude, lng: bp.longitude }));
        setInnerBoundaryPositions(innerPositions);
      });
    } else {
      setInnerBoundaryPositions(undefined);
    }

  }, [outerBoundaryName, innerBoundaryName, getBoundaryPointsByName]);

  useEffect(() => {
    if (point.latitude === 0 && point.longitude === 0) {
      if ((!!innerBoundaryPositions || !!outerBoundaryPositions)) {
        const positions = innerBoundaryPositions ?? outerBoundaryPositions;
        if (!positions) {
          return;
        }
        const centroid = calculateCentroid(positions);
        if (!!innerBoundaryPositions) {
          setPointPosition(centroid);
        }
        setMapCenter(centroid);
        const zoomLevel = calculateZoomForBounds(positions);
        setZoomLevel(zoomLevel);
      } else {
        setPointPosition({ lat: 0, lng: 0 });
      }
    } else {
      const newPointPosition = { lat: point.latitude, lng: point.longitude };
      setPointPosition(newPointPosition);
      setMapCenter(newPointPosition);
      setZoomLevel(7);
    }
  }, [point, innerBoundaryPositions, outerBoundaryPositions]);

  useEffect(() => {
    if (mapRef.current) {
      mapRef.current.setView(mapCenter, zoomLevel);
    }
  }, [mapCenter, zoomLevel]);

  if (!outerBoundaryPositions) {
    return null;
  }

  return (
    <MapContainer
      center={mapCenter}
      zoom={zoomLevel}
      style={{ height: "400px" }}
      whenCreated={mapInstance => { mapRef.current = mapInstance; }}
    >
      <TileLayer
        attribution="Google Maps"
        url="http://www.google.com/maps/vt?lyrs=m@189&gl=us&x={x}&y={y}&z={z}"
      />
      <Polyline
        pathOptions={{ color: "#0000FF" }}
        positions={outerBoundaryPositions}
      />
      {innerBoundaryPositions && (
        <Polyline
          pathOptions={{ color: "#FF00FF" }}
          positions={innerBoundaryPositions}
        />
      )}
      {'lat' in pointPosition && pointPosition.lat !== 0 && pointPosition.lng !== 0 && <Marker position={pointPosition} />}
    </MapContainer>
  );
}
