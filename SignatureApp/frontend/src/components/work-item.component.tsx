import {
  faCircleLeft,
  faFlag,
  faShareSquare,
} from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { FormikProps } from "formik";
import {
  SyntheticEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  RegisteredVoterFlags,
  ValidationForm,
  WorkDto,
  WorkField,
} from "../types/work.types";
import { WorkForTaskType } from "./work-for-task-type";

interface WorkItemProps {
  work: WorkDto;
  onSave?: (
    fields: WorkField[],
    externalDataRecordId?: string | null,
    registeredVoterFlags?: RegisteredVoterFlags
  ) => void;
  onNext: (
    fields: WorkField[],
    externalDataRecordId?: string | null,
    registeredVoterFlags?: RegisteredVoterFlags
  ) => void;
  onPrevious?: (e: SyntheticEvent) => void;
  onFlagForReview?: (e: SyntheticEvent) => void;
  onTakeBreak?: (e: SyntheticEvent) => void;
  formikRef: React.RefObject<FormikProps<ValidationForm>>;
  errorMessage: string;
  nextLabel?: string | null;
  isReviewer?: boolean;
}

const WorkItem = ({
  work,
  onSave,
  onNext,
  onPrevious,
  onFlagForReview,
  onTakeBreak,
  formikRef,
  errorMessage,
  nextLabel = null,
  isReviewer = true,
}: WorkItemProps) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const onResize = useCallback((entries) => {
    setContainerWidth(entries[0].contentRect.width);
  }, []);

  useEffect(() => {
    const observer = new ResizeObserver(onResize);
    const containerEle = containerRef.current;
    if (containerEle) {
      observer.observe(containerEle);
    }

    return () => {
      if (containerEle) {
        observer.unobserve(containerEle);
      }
    };
  }, [onResize]);

  return (
    <div className="row tablet-view justify-content-start">
      <div className="col-3 tablet-left d-flex flex-column">
        <div className="tablet-buffer d-flex flex-fill flex-column mb-2">
          <div className="mb-auto">
            <h3>{work.taskName}</h3>
            {work.taskDescription && (
              <ul className="mt-3">
                {work.taskDescription &&
                  work.taskDescription
                    .split("\n")
                    .map((instruction, i) => <li key={i}>{instruction}</li>)}
              </ul>
            )}
            <div>
              <ul className="mt-3">
                {work.violationCriteria &&
                  work.violationCriteria.map((rule) => (
                    <li key={rule.ruleId}>{rule.name}</li>
                  ))}
              </ul>
            </div>
          </div>
          <div>
            {onTakeBreak && (
              <button
                className="btn btn-light btn-outline"
                onClick={onTakeBreak}
                disabled={!isReviewer}
              >
                <FontAwesomeIcon icon={faShareSquare} className="me-2" />
                Take a Break
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="col-9 tablet-right d-flex flex-column">
        <div
          className="tablet-buffer d-flex flex-fill flex-column mb-2"
          ref={containerRef}
        >
          <div className="mb-auto">
            <WorkForTaskType
              work={work}
              containerWidth={containerWidth}
              onNext={onNext}
              onSave={onSave}
              formikRef={formikRef}
              nextLabel={nextLabel}
            />

            {errorMessage && (
              <div className="row">
                <div className="col">
                  <div className="form-group">
                    <div className="alert alert-danger" role="alert">
                      {errorMessage}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div>
            <div className="d-flex left-right">
              <div className="me-auto">
                {onPrevious && (
                  <button
                    className="btn btn-sw"
                    onClick={onPrevious}
                    disabled={!isReviewer}
                  >
                    <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
                    Previous Item
                  </button>
                )}
              </div>
              <div>
                {onFlagForReview && (
                  <button
                    className="btn btn-sw-m"
                    onClick={onFlagForReview}
                    disabled={!isReviewer}
                  >
                    <FontAwesomeIcon icon={faFlag} className="me-2" />
                    Flag for Review
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkItem;
