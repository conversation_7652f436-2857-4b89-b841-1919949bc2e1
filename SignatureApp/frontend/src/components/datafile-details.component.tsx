import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
import { Link, useParams } from "react-router-dom";
import { ExternalDataSource } from "../types/external-data.types";

export default function DataFileDetails() {
  const params = useParams();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<ExternalDataSource | null>(null);

  const dataSourceId = params?.dataSourceId;
  const isNew = dataSourceId === "new";

  return isNew || dataSource ? (
    <div className="container">
      <div className="d-flex flex-row mb-4">
        <div className="me-auto" style={{ marginLeft: "-12px" }}>
          <Link to="/admin" className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={faCircleLeft} className="me-2" />
            Back to Dashboard
          </Link>
        </div>
      </div>
      <div className="row tablet-view">
        <div className="col-4 tablet-left d-flex flex-column">
          <div className="tablet-buffer d-flex flex-fill flex-column mb-3">
            <div>
              <span>Data File</span>
              <h2>{dataSource?.fileName || "New"}</h2>
            </div>
          </div>
        </div>
        <div className="col-8 tablet-right">
          <div className="tablet-buffer"></div>
        </div>
      </div>
    </div>
  ) : (
    <>{!loading && "This data file is invalid"}</>
  );
}
