import { faCircleLeft } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Link, useParams } from "react-router-dom";
import { useMatterContext } from "../contexts/MatterContext";
import { faArrowUpRightFromSquare } from "@fortawesome/free-solid-svg-icons";
import { checkForRole } from "../common/utils";
import { Roles } from "../types/user.types";
import { useAuthContext } from "../AuthProvider";

interface MatterHeaderBarProps {
  contextType?: "matter" | "admin-dashboard" | "manager-dashboard";
  backTo?: "dashboard" | "details" | "sheet" | "admin" | "manager";
  leftContent?: React.ReactNode;
  rightContent?: React.ReactNode;
  shouldHideMatter?: boolean;
  variant?: "container" | "container-fluid";
}

export default function HeaderNavBar({
  contextType = "matter",
  backTo = "details",
  leftContent,
  rightContent,
  shouldHideMatter,
  variant = "container",
}: MatterHeaderBarProps) {
  const { authenticatedUser } = useAuthContext();
  const matterContext = useMatterContext(true);
  const { matterId, matterName } = matterContext;
  const { sheetNumber } = useParams();

  const getBackLink = () => {
    if (contextType === "matter") {
      if (backTo === "dashboard") {
        return "/admin";
      }
      if (backTo === "sheet") {
        return `/admin/matters/${matterId}/sheets/${sheetNumber}`;
      }
      return `/admin/matters/${matterId}/details`;
    }

    if (backTo === "manager") {
      return "/manager";
    }
    return "/admin";
  };

  const getLeftNavIcon = () => {
    if (contextType === "matter") {
      return faCircleLeft;
    }
    return faArrowUpRightFromSquare; // Default icon for other contexts
  };

  const getBackText = () => {
    if (contextType === "matter") {
      if (backTo === "dashboard") {
        return "Back to Dashboard";
      }
      return "Back to Matter";
    }
    if (contextType === "admin-dashboard") {
      return "Manager Dashboard";
    }
    if (contextType === "manager-dashboard") {
      return "Admin Dashboard";
    }
  };

  const headerContent = (
    <div
      className={
        variant === "container" ? "d-flex flex-row align-items-center mb-4" : "d-flex align-items-center m-4 mt-0"
      }
    >
      <div className="flex-shrink-0" style={variant === "container" ? { marginLeft: "-12px" } : {}}>
        {leftContent ? (
          leftContent
        ) : (
          (contextType !== "manager-dashboard"|| checkForRole(Roles.Admin, authenticatedUser?.roleId))&&
          <Link to={getBackLink()} className="btn btn-sw btn-sm btn-outline">
            <FontAwesomeIcon icon={getLeftNavIcon()} className="me-2" />
            {getBackText()}
          </Link>
        )}
      </div>

      <div className="flex-grow-1 d-flex justify-content-center">
        {!shouldHideMatter && (
          <>
            {matterName && <span>{matterName}</span>}
            {sheetNumber && <span>- Sheet {sheetNumber}</span>}
          </>
        )}
      </div>

      <div className="flex-shrink-0" style={variant === "container" ? {} : { marginRight: "36px" }}>
        {rightContent}
      </div>
    </div>
  );

  if (variant === "container-fluid") {
    return <div className="row bg-sw-primary">{headerContent}</div>;
  }

  return headerContent;
}
