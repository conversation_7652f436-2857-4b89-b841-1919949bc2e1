import { FullSheetDeficiency } from "../types/deficiency.types";
import { DeficiencySheetBadges } from "./deficiency-sheet-badges.component";

interface SheetDeficiencyItemProps {
  matterId: string;
  sheetDeficiency: FullSheetDeficiency;
  onOpenModal: (sheetId: number, sheetNumber: number) => void;
}

export default function SheetDeficiencyItem({
  matterId,
  sheetDeficiency,
  onOpenModal,
}: SheetDeficiencyItemProps) {


  return (
    <div className="row mb-4">
      <div className="col-md-4">
        <DeficiencySheetBadges
          matterId={matterId}
          sheetDeficiency={sheetDeficiency}
        />
        <button 
          className="btn btn-sm btn-sw mt-3" 
          onClick={() => {
            onOpenModal(sheetDeficiency.signatureSheetId, sheetDeficiency.sheetNumber)}}
        >
          Sheet Deficiencies
        </button>
      </div>
      {sheetDeficiency.imageUrl && (
        <div className="col-md-8">
          <img
            alt="deficiency"
            style={{ maxWidth: 730 }}
            src={sheetDeficiency.imageUrl}
          />
        </div>
      )} 
      <hr />
    </div>
  );
} 