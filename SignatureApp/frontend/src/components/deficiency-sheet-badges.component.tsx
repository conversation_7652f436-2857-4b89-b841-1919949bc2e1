import { MouseEvent, useMemo } from "react";
import { FullSheetDeficiency } from "../types/deficiency.types";
import { useNavigate, useLocation } from "react-router-dom";

export type DeficiencySheetBadgesProps = {
  matterId: string;
  sheetDeficiency: FullSheetDeficiency;
};

export function DeficiencySheetBadges({
  matterId,
  sheetDeficiency,
}: DeficiencySheetBadgesProps) {
  const navigate = useNavigate();
  const location = useLocation();

  const badges = useMemo(() => {
    const seenRuleIds: number[] = [];
    return sheetDeficiency.deficiencyBadges.map((badge) => {
      const alreadySeenRule = seenRuleIds.includes(badge.ruleId);
      const isRowLevelRule = badge.badgeDescription.includes(":r");
      if (!alreadySeenRule && !isRowLevelRule) {
        seenRuleIds.push(badge.ruleId);
      }
      return { ...badge, alreadySeenRule };
    });
  }, [sheetDeficiency.deficiencyBadges]);

  function handleNavigateToDeficiencyReview(
    e: MouseEvent<HTMLAnchorElement, globalThis.MouseEvent>,
    deficiencyId: number | null
  ): void {
    e.preventDefault();
    navigate(`/admin/matters/${matterId}/deficiencies/review/${deficiencyId}`, {
      state: { from: `${window.location.pathname}#sheet` },
    });
  }
  function handleNavigateToWork(
    e: MouseEvent<HTMLAnchorElement, globalThis.MouseEvent>,
    workId: number | null, 
  ): void {
    e.preventDefault();
    if (workId == null) return;
    navigate(`/work/${workId}`, {
      state: { from: `${location.pathname}#sheet` },
    });
  }
  return (
    <>
      {badges?.length > 0 &&
        badges.map((badge) => (
          <div key={badge.deficiencyId}>
            <span style={{ color: "#FF0000" }}>{badge.badgeNumber}</span>
            {!badge.userName && !badge.alreadySeenRule && (
              <a
                href={`/admin/matters/${matterId}/deficiencies/review/${badge.deficiencyId}`}
                className="ms-2"
                onClick={(e) =>
                  handleNavigateToDeficiencyReview(e, badge.deficiencyId)
                }
              >
                {badge.badgeDescription}
              </a>
            )}
            {!badge.userName && badge.alreadySeenRule && (
              <span className="ms-2">{badge.badgeDescription}</span>
            )}

            {badge.userName && badge.workId && !badge.alreadySeenRule && (
              <a
                href={`/work/${badge.workId}`}
                className="ms-2"
                onClick={(e) => handleNavigateToWork(
                  e,
                  badge.workId
                )}
              >
                {badge.badgeDescription}
              </a>
            )}
            {badge.userName && (!badge.workId || badge.alreadySeenRule) && (
              <span className="ms-2">{badge.badgeDescription}</span>
            )}
          </div>
        ))}
    </>
  );
}
