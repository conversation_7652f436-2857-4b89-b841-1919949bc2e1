import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { useRef } from "react";
import Modal from "react-modal";
import { ViolationCriterion } from "../types/work.types";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrashCan } from "@fortawesome/free-solid-svg-icons";
import { RecordIdType } from "../types/deficiency.types";
interface AddMatterDeficiencyForm {
  violationId: number | string | null;
  violationDescription: string,
  violationNote: string | null;
}

export type DeficiencyModalRow = { description: string, ruleId: number }

interface EditDeficienciesModalProps {
  isOpen: boolean;
  close: () => void;
  type: RecordIdType.Matter | RecordIdType.SignatureSheet
  addDeficiency: (form: AddMatterDeficiencyForm) => void;
  deleteDeficiency: (violationId: number) => void;
  deficiencies: DeficiencyModalRow[]
  errorMessage?: string;
  violationCriteria: ViolationCriterion[];
}

export const EditDeficienciesModal = ({
  isOpen,
  deficiencies,
  type,
  close,
  addDeficiency,
  deleteDeficiency,
  errorMessage,
  violationCriteria,
}: EditDeficienciesModalProps) => {
  const formikRef = useRef<FormikProps<AddMatterDeficiencyForm>>(null);

  const handleCloseModal = () => {
    close();
  };
  
  const handleAdd = (formValues: AddMatterDeficiencyForm) => {
    addDeficiency(formValues);
    formikRef.current?.resetForm()
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={handleCloseModal}
      onAfterOpen={() => {
        // TODO: load things
      }}
      style={{
        content: {
          top: "50%",
          left: "50%",
          right: "auto",
          bottom: "auto",
          width: 800,
          maxHeight: "80vh",
          transform: "translate(-50%, -50%)",
          overflow: "hidden"
        },
      }}
      contentLabel="Add Users Modal"
    >
      <div className="row">
        <div className="col">
          <div className="d-flex left-right">
            <h3>{type === RecordIdType.Matter ? "Matter" : "Sheet"} Deficiencies</h3>
            <span
              style={{ fontSize: "1.25rem", cursor: "pointer" }}
              aria-label="Close"
              onClick={close}
              className="font-weight-bold"
            >
              X
            </span>
          </div>
        </div>
      </div>
      {deficiencies?.length > 0 && (
        <div style={{ maxHeight: "40vh", overflow: "auto", marginBottom: "15px" }}>
          <table className="sw">
            <thead style={{ position: "sticky", top: 0, background: "white" }}>
              <tr className="d-flex">
                <th scope="col" className="col-sm-11">
                  Description
                </th>
                <th scope="col" className="col-sm-1">
                  Action
                </th>
              </tr>
            </thead>
            <tbody>
              {deficiencies.map((d, i) => (
                <tr
                  key={d.description + d.ruleId + i}
                  className="d-flex border-bottom"
                >
                  <td className="col-sm-11">{d.description}</td>
                  <td className="col-sm-1">
                    <button
                      className="btn btn-link sw-maroon"
                      onClick={() => {
                        deleteDeficiency(d.ruleId);
                      }}
                    >
                      <FontAwesomeIcon
                        style={{ color: "#741D38" }}
                        icon={faTrashCan}
                      />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <Formik
        innerRef={formikRef}
        enableReinitialize
        validateOnBlur={true}
        initialValues={{} as AddMatterDeficiencyForm}
        onSubmit={(values, {resetForm, setFormikState}) => {
          handleAdd(values);
          resetForm();
          setFormikState(prev => ({
            ...prev,
            values: { violationId: '', violationDescription: '', violationNote: '' },
          }));
        }}
      >
        {(formik) => (
          <Form>
            <div className="row text-center my-3">
              <div className="form-group mt-2">
                <Field
                  as="select"
                  style={{ minWidth: "25%" }}
                  className="form-control w-75"
                  name={`violationId`}
                  value={formik.values.violationId?.toString()}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    formik.handleChange(e);
                    const selectedId = e.target.value;
                    if (selectedId) {
                      const selectedCriterion = violationCriteria.find(c => c.ruleId.toString() === selectedId);
                      if (selectedCriterion) {
                        formik.setFieldValue('violationDescription', selectedCriterion.name);
                      }
                    } else {
                      formik.setFieldValue('violationDescription', '');
                    }
                  }}
                >
                  <option value="">Select Violation</option>
                  {violationCriteria
                    .filter(criterion => !deficiencies.some(d => d.ruleId === criterion.ruleId))
                    .map((criterion) => (
                    <option key={criterion.ruleId} value={criterion.ruleId}>
                      {criterion.name}
                    </option>
                  ))}
                </Field>

                <ErrorMessage
                  name={`violationId`}
                  component="div"
                  className="alert alert-danger w-50 p-2"
                />
                {formik.values.violationId && (
                  <Field
                    className="form-control w-75 mt-2"
                    placeholder="Notes" 
                    as="textarea"
                    name={`violationNote`}
                    value={formik.values.violationNote}
                    onChange={formik.handleChange}
                  />
                )}
              </div>
            </div>
            <div className="row align-items-end mt-3">
              <div className="col">
                <button 
                  type="submit" 
                  className="btn btn-sw"
                  disabled={!formik.values.violationId}
                >
                  Add Deficiency
                </button>
                {errorMessage && (
                  <div className="form-group d-inline ms-2">
                    <div className="alert alert-danger d-inline" role="alert">
                      {errorMessage}
                    </div>
                  </div>
                )}
              </div>
              <div className="col">
                {errorMessage && (
                  <div className="form-group d-inline ms-2">
                    <div className="alert alert-danger d-inline" role="alert">
                      {errorMessage}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};
