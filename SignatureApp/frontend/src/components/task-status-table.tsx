import { useMemo } from "react";
import { formatDateString } from "../common/utils";
import { Matter } from "../types/matter.types";
import { TaskWorkStatus, WorkStatus } from "../types/work.types";

type TaskStatusTableProps = {
  matter: Matter | null;
  tasks: TaskWorkStatus[];
};

type TaskTotal = {
  taskId: number;
  taskName: string;
  completed: number;
  total: number;
};

type TaskTotalMap = {
  [key: number]: TaskTotal;
};

type CountTotals = {
  completed: number;
  total: number;
};

export default function TaskStatusTable({
  matter,
  tasks,
}: TaskStatusTableProps) {
  const taskTotals = useMemo(() => {
    const taskTotalMap = tasks.reduce((taskMap, task) => {
      const isCompleted = task.workStatus === WorkStatus.Completed;
      if (!taskMap[task.taskId]) {
        taskMap[task.taskId] = {
          taskId: task.taskId,
          taskName: task.taskName,
          completed: 0,
          total: 0,
        };
      }

      if (isCompleted) {
        taskMap[task.taskId].completed += task.count;
      }

      taskMap[task.taskId].total += task.count;

      return taskMap;
    }, {} as TaskTotalMap);

    const totals = Object.entries(taskTotalMap).map(([_, task]) => task);

    totals.sort((a, b) => {
      const aCompletedPercent = a.completed / a.total;
      const bCompletedPercent = b.completed / b.total;

      return bCompletedPercent - aCompletedPercent;
    });

    return totals;
  }, [tasks]);

  const counts = useMemo(() => {
    return taskTotals.reduce(
      (sum, task) => {
        sum.completed += task.completed;
        sum.total += task.total;

        return sum;
      },
      {
        completed: 0,
        total: 0,
      } as CountTotals
    );
  }, [taskTotals]);

  if (!matter) {
    return null;
  }

  return (
    <div>
      <h3>Matter</h3>
      <div
        style={{
          maxHeight: 400,
          overflowY: "auto",
          overflowX: "hidden",
        }}
      >
        <table className="sw">
          <thead>
            <tr className="d-flex">
              <th scope="col" className="col-sm-4">
                Name
              </th>
              <th scope="col" className="col-sm-3">
                Due Date
              </th>
              <th scope="col" className="col-sm-8">
                Progress
              </th>
            </tr>
          </thead>
          <tbody>
            <tr key={matter.name} className="d-flex border-bottom">
              <td className="col-sm-4">{matter.name}</td>
              <td className="col-sm-3">{formatDateString(matter.dueDate)}</td>
              <td className="col-sm-5">
                <progress
                  style={{ width: "100%" }}
                  max={counts.total}
                  value={counts.completed}
                  title={`${counts.completed} out of ${counts.total}`}
                ></progress>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        style={{
          marginTop: 20,
        }}
      >
        <h3>Tasks</h3>
        <table className="sw">
          <thead>
            <tr className="d-flex">
              <th scope="col" className="col-sm-6">
                Name
              </th>
              <th scope="col" className="col-sm-6">
                Progress
              </th>
            </tr>
          </thead>
          <tbody>
            {taskTotals.map((task) => (
              <tr key={task.taskId} className="d-flex border-bottom">
                <td className="col-sm-6">{task.taskName}</td>
                <td className="col-sm-6">
                  <progress
                    style={{ width: "100%" }}
                    max={task.total}
                    value={task.completed}
                    title={`${task.completed} out of ${task.total}`}
                  ></progress>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
