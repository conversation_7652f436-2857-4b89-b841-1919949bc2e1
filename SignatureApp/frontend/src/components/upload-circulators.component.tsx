import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSquarePlus, faTrashCan } from "@fortawesome/free-regular-svg-icons";
import { useCallback, useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { DocumentUploadInfo } from "../types/matter.types";
import { useMatterStatusService } from "../services/matter-status.service";
import { useUploadExternalDataService } from "../services/upload-externaldata.service";
import { handleError } from "../common/utils";
import ConfirmModal from "./confirm-modal";
import { InlineSpinner } from "./inline-spinner";

interface UploadCirculatorsProps {
  refreshChecklist: () => void;
}

export default function UploadCirculators({ refreshChecklist }: UploadCirculatorsProps) {
  const { getCirculatorRegistrationInfoById } = useMatterStatusService();
  const params = useParams();
  const matterId = params?.matterId;
  const [errorMessage, setErrorMessage] = useState("");
  const [showCirculatorConfirmModal, setShowCirculatorConfirmModal] = useState(false);
  const { deleteCirculators } = useUploadExternalDataService();
  const [externalDataSourceIdToDelete, setExternalDataSourceIdToDelete] = useState<string>();
  const [isDeleting, setIsDeleting] = useState(false);
  const [loadingDataSources, setLoadingDataSources] = useState(false);

  const [circulatorRegistrationUploadInfo, setCirculatorRegistrationUploadInfo] = useState<DocumentUploadInfo>({
    totalValidCount: 0,
    totalInvalidCount: 0,
    lastUploadId: "",
    lastUploadedBy: "",
    lastUploadedOn: "",
    uploadCount: 0,
  });
  const [registeredCirculatorsProcessing, setRegisteredCirculatorsProcessing] = useState(false);

  const populateCirculatorRegistrationInfo = useCallback(() => {
    getCirculatorRegistrationInfoById(matterId?.toString() || "").then((info) => {
      setCirculatorRegistrationUploadInfo(info);
    });
  }, [getCirculatorRegistrationInfoById, matterId]);

  useEffect(() => {
    populateCirculatorRegistrationInfo();
  }, [populateCirculatorRegistrationInfo]);

  const handleDeleteCirculator = async () => {
    if (externalDataSourceIdToDelete) {
      try {
        setErrorMessage("");
        setIsDeleting(true);
        await deleteCirculators(externalDataSourceIdToDelete.toString());
        setTimeout(() => {
          populateCirculatorRegistrationInfo();
        }, 1000);
      } catch (error) {
        handleError(error, setErrorMessage, setLoadingDataSources);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <div className="mt-4">
      <div className="d-flex left-right mt-4">
        <h3>Circulator Info</h3>
        <Link role="button" to={`/admin/matters/${matterId}/circulator-info-upload`} className="btn btn-sw btn-outline">
          <FontAwesomeIcon icon={faSquarePlus} className="me-2" />
          Upload Circulators
        </Link>
      </div>
      {registeredCirculatorsProcessing && (
        <div>
          <InlineSpinner />
          <span className="small">Processing file...</span>
        </div>
      )}
      {!registeredCirculatorsProcessing && (
        <span className="small">
          {`${circulatorRegistrationUploadInfo?.totalValidCount} circulator registrations have been uploaded. `}
          {circulatorRegistrationUploadInfo?.totalValidCount > 0
            ? `Last upload by ${circulatorRegistrationUploadInfo?.lastUploadedBy} on ${circulatorRegistrationUploadInfo?.lastUploadedOn}`
            : ""}
          {circulatorRegistrationUploadInfo?.totalValidCount > 0 && (
            <button
              className="btn btn-link sw-maroon"
              onClick={() => {
                setShowCirculatorConfirmModal(true);
                setExternalDataSourceIdToDelete(circulatorRegistrationUploadInfo?.lastUploadId.toString());
              }}
            >
              <FontAwesomeIcon style={{ color: "#741D38" }} icon={faTrashCan} />
            </button>
          )}
        </span>
      )}
      <ConfirmModal
        message="Are you sure you want to delete this circulator file?"
        action={handleDeleteCirculator}
        isOpen={showCirculatorConfirmModal}
        close={() => setShowCirculatorConfirmModal(false)}
      />
    </div>
  );
}
