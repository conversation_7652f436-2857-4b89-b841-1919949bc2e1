import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import "./resizable-table.css";
import { Rnd } from "react-rnd";
import { Container, Section, Bar } from "@column-resizer/react";
import {
  Column,
  Page,
  Row,
  SignatureTable,
} from "../types/signature-sheet.types";

export interface ResizableTableProps {
  widthInPx: number;
  heightInPx: number;
  tableData: SignatureTable;
  page: Page;
}

export const ResizableTable: React.FC<ResizableTableProps> = ({
  tableData,
  page,
  widthInPx,
  heightInPx,
}) => {
  const BAR_WIDTH = 3;
  const [data, setData] = useState<SignatureTable>(tableData);

  const [hideColumns, setHideColumns] = useState(false);
  const [hasValidSheetImage, setHasValidSheetImage] = useState(
    !!tableData.sheetImage?.fileContents
  );

  const imageWidth = widthInPx;
  const imageHeight = heightInPx;
  const jsonWidth = page.width;
  const jsonHeight = page.height;
  const scaleX = imageWidth / jsonWidth;
  const scaleY = imageHeight / jsonHeight;

  const sectionRefs = useRef<Array<HTMLDivElement | undefined>>([]);
  const barRefs = useRef<Array<HTMLDivElement | undefined>>([]);

  const columns = useMemo(
    () => data.columns.filter((col) => !col.isMissing),
    [data.columns]
  );

  const rows = useMemo(
    () => data.rows.filter((row) => !row.isMissing),
    [data.rows]
  );

  // Bounds are controlled by first and last rows
  const [bounds, setBounds] = useState({
    x: columns[0].left * scaleX,
    y: rows[0].top * scaleY,
    width: (columns[columns.length - 1].right - columns[0].left) * scaleX,
    height: (rows[rows.length - 1].bottom - rows[0].top) * scaleY,
  });

  const rowsContainerStyles: React.CSSProperties = {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    width: "100%",
    position: "absolute",
    top: 0,
    left: 0,
  };

  const logColumnSizes = useCallback(() => {
    console.log(
      `Table bounds x (px): ${bounds.x} ${bounds.x + bounds.width} ${
        bounds.width
      }`
    );
    for (const column of columns) {
      let left = column.left * scaleX;
      if (column.columnIndex !== 0) {
        left += BAR_WIDTH / 2.0; // Adjust left for bar width
      }
      let right = column.right * scaleX;
      if (column.columnIndex !== columns.length - 1) {
        right -= BAR_WIDTH / 2.0; // Adjust right for bar width
      }
      console.log(
        `${column.columnIndex} ${left.toFixed(1)} ${right.toFixed(1)} ${(
          right - left
        ).toFixed(1)}`
      );
    }

    console.log(
      `Table bounds x (in): ${(bounds.x / scaleX).toFixed(6)} ${(
        (bounds.x + bounds.width) /
        scaleX
      ).toFixed(6)} ${(bounds.width / scaleX).toFixed(6)}`
    );
    for (const column of columns) {
      console.log(
        `${column.columnIndex} ${column.left.toFixed(6)} ${column.right.toFixed(
          6
        )} ${(column.right - column.left).toFixed(6)}`
      );
    }
  }, [bounds, columns, scaleX]);

  useEffect(() => {
    console.log(`14 x 8.5 = ${(14.0 / 8.5).toFixed(6)}`);
    //console.log(`11 x 8.5 = ${11.0 / 8.5}`);
    console.log(
      `Image dimensions: ${imageWidth.toFixed(6)} by ${imageHeight.toFixed(
        6
      )} = ${(imageWidth / imageHeight).toFixed(6)}`
    );
    console.log(
      `JSON dimensions: ${jsonWidth.toFixed(6)} by ${jsonHeight.toFixed(
        6
      )} = ${(jsonWidth / jsonHeight).toFixed(6)}`
    );
    console.log(`Scale: X=${scaleX.toFixed(6)}, Y=${scaleY.toFixed(6)}`);

    // Initialize sectionRefs and barRefs with nulls
    logColumnSizes();
  }, [
    imageHeight,
    imageWidth,
    jsonHeight,
    jsonWidth,
    logColumnSizes,
    scaleX,
    scaleY,
  ]);

  const getDefaultSectionSize = useCallback(
    (index) => {
      const column = columns[index];
      let left = column.left * scaleX;
      // To get the sizes for the sections we need to remove the bar width from each side
      if (column.columnIndex !== 0) {
        left += BAR_WIDTH / 2.0; // Adjust left for bar width
      }
      let right = column.right * scaleX;
      if (column.columnIndex !== columns.length - 1) {
        right -= BAR_WIDTH / 2.0; // Adjust right for bar width
      }
      const width = right - left;
      return width;
    },
    [columns, scaleX]
  );

  const adjustDataRows = useCallback(() => {
    const newTop = bounds.y / scaleY;
    const newHeight = bounds.height / scaleY;
    const heightPerRow = newHeight / rows.length;

    rows.forEach((row, index) => {
      const newRowTop = newTop + index * heightPerRow;
      const newRowBottom = newRowTop + heightPerRow;
      row.top = newRowTop;
      row.bottom = newRowBottom;
    });
  }, [bounds.height, bounds.y, rows, scaleY]);

  const adjustDataColumns = useCallback(() => {
    let left = bounds.x;
    sectionRefs.current.forEach((section, index) => {
      if (!section) return;
      console.log(`${index} ${left}`);
      const column: Column = columns[index];

      // To get the sizes for the columns from the sections we need to add back the bar width
      let newLeft = left;
      if (index !== 0) {
        newLeft -= BAR_WIDTH / 2.0;
      }
      column.left = newLeft / scaleX;

      let newRight = left + section.offsetWidth;
      if (index !== columns.length - 1) {
        newRight += BAR_WIDTH / 2.0;
      }
      column.right = newRight / scaleX;

      left += section.offsetWidth + BAR_WIDTH;
    });
    return left;
  }, [bounds.x, columns, scaleX]);

  const handleOuterResizeStart = useCallback((_e, _dir) => {}, []);

  const handleOuterResizeStop = useCallback(
    (_e, dir, ref, _delta, position) => {
      console.log(`handleOuterResizeStop dir=${dir}`);
      const newSize = {
        width: Number(ref.style.width.replace("px", "")),
        height: Number(ref.style.height.replace("px", "")),
      };

      setBounds((prev) => ({
        ...prev,
        width: newSize.width,
        height: newSize.height,
        x: position.x,
        y: position.y,
      }));

      // Update data immediately when outer resize stops
      adjustDataRows();
      adjustDataColumns();
      logColumnSizes();
      setData((prevData) => ({ ...prevData }));
    },
    [adjustDataRows, adjustDataColumns, logColumnSizes]
  );

  const outputSectionSizes = useCallback(() => {
    console.log(
      sectionRefs.current.map((section) => section?.offsetWidth).join(" ")
    );
  }, []);

  const handleBarStatusChanged = useCallback(
    (isActive: boolean) => {
      console.log(`handleBarStatusChanged isActive=${isActive}`);
      outputSectionSizes();

      // Update data immediately when column resizing stops
      if (!isActive) {
        adjustDataColumns();
        logColumnSizes();
        setData((prevData) => ({ ...prevData }));
      }
    },
    [outputSectionSizes, adjustDataColumns, logColumnSizes]
  );

  const renderColumn = useCallback(
    (column, index, lastIndex) => {
      return (
        <React.Fragment key={`col-${column.id}`}>
          <Section
            ref={(element) =>
              (sectionRefs.current[index] = element ?? undefined)
            }
            defaultSize={getDefaultSectionSize(index)}
          />

          {index !== lastIndex && (
            <Bar
              ref={(element) => (barRefs.current[index] = element ?? undefined)}
              key={`col-${column.id}`}
              size={BAR_WIDTH}
              style={{ background: "red", cursor: "col-resize" }}
              onStatusChanged={handleBarStatusChanged}
            />
          )}
        </React.Fragment>
      );
    },
    [getDefaultSectionSize, handleBarStatusChanged]
  );

  const renderRow = useCallback(
    (row: Row, index: number) => {
      const style = {
        borderBottom: `${BAR_WIDTH}px solid green`,
        borderLeft: `${BAR_WIDTH}px solid red`,
        borderRight: `${BAR_WIDTH}px solid red`,
        boxSizing: `border-box`,
        height: `${100 / rows.length}%`,
      } as any;
      if (index === 0) {
        style.borderTop = `${BAR_WIDTH}px solid green`;
      }

      return <div key={`row-${row.id}`} style={style}></div>;
    },
    [rows.length]
  );

  const Columns = useMemo(() => {
    const lastIndex = columns.length - 1;
    return columns.map((column, index) =>
      renderColumn(column, index, lastIndex)
    );
  }, [columns, renderColumn]);

  const Rows = useMemo(() => {
    return rows.map((row, index) => renderRow(row, index));
  }, [rows, renderRow]);

  return (
    <div className="resizable-table-container">
      {hasValidSheetImage && (
        <div
          className="table-background"
          style={{ width: imageWidth + "px", height: imageHeight + "px" }}
        >
          {hasValidSheetImage && (
            <img
              src={`data:image/png;base64,${data.sheetImage?.fileContents}`}
              alt="background"
              className="table-image"
              style={{ aspectRatio: data.imageAspectRatio }}
              onClick={logColumnSizes}
            />
          )}
          <Rnd
            size={{ width: bounds.width, height: bounds.height }}
            position={{ x: bounds.x, y: bounds.y }}
            disableDragging
            onResizeStart={handleOuterResizeStart}
            onResizeStop={handleOuterResizeStop}
          >
            <div style={rowsContainerStyles}>{Rows}</div>
          </Rnd>
          {!hideColumns && (
            <Container
              className="resize-container"
              style={{
                width: bounds.width,
                height: bounds.height,
                position: "absolute",
                left: bounds.x,
                top: bounds.y,
              }}
            >
              <>{Columns}</>
            </Container>
          )}
        </div>
      )}
      {!hasValidSheetImage && <p>Failed to load signature sheet image.</p>}
    </div>
  );
};
