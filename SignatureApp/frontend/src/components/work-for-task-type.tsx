import { FormikProps } from "formik";
import {
  RegisteredVoterFlags,
  TaskType,
  ValidationForm,
  WorkDto,
  WorkField,
} from "../types/work.types";
import FieldValidation from "./field-validation.component";
import VoterValidation from "./voter-validation.component";
import ColumnValidation from "./column-validation.component";
import SignatureTableValidation from "./signature-table-validation";

export type WorkForTaskTypeProps = {
  work: WorkDto;
  containerWidth?: number;
  onSave?: (
    fields: WorkField[],
    externalDataRecordId?: string | null,
    registeredVoterFlags?: RegisteredVoterFlags
  ) => void;
  onNext: (
    fields: WorkField[],
    externalDataRecordId?: string | null,
    registeredVoterFlags?: RegisteredVoterFlags
  ) => void;
  formikRef: React.RefObject<FormikProps<ValidationForm>>;
  nextLabel: string | null;
};

export function WorkForTaskType({
  work,
  containerWidth,
  onNext,
  onSave,
  formikRef,
  nextLabel,
}: WorkForTaskTypeProps) {
  const isSheetReview = !!(work!.taskTypeId & TaskType.SheetReview);
  const isRegistrationVerification = !!(
    work!.taskTypeId & TaskType.ExternalDataSourceVerification
  );
  const otherThanRegistrationVerification =
    work!.taskTypeId & ~TaskType.ExternalDataSourceVerification;
  const isSignatureTableColumn = !!(
    work!.taskTypeId & TaskType.SignatureTableColumn
  );

  function handleSubmitCorrection(fields: WorkField[]) {
    if (onSave) {
      onSave(fields);
    }
  }

  if (isSheetReview) {
    return (
      <SignatureTableValidation
        matterId={work.matterId.toString()}
        onNext={onNext}
        sheetNumber={work.sheetNumber.toString()}
        width={containerWidth || 0}
      />
    );
  } else if (isRegistrationVerification) {
    return (
      <>
        {!!otherThanRegistrationVerification && (
          <FieldValidation
            work={work}
            onNext={handleSubmitCorrection}
            formikRef={formikRef}
            nextLabel="Submit Correction"
            isSignatureTable={isSignatureTableColumn}
            isRegistrationVerification={isRegistrationVerification}
          />
        )}

        <VoterValidation
          work={work}
          onNext={onNext}
          formikRef={formikRef}
          nextLabel={nextLabel}
        />
      </>
    );
  } else if (work?.showWholeColumn) {
    return (
      <ColumnValidation work={work} onNext={onNext} formikRef={formikRef} />
    );
  } else {
    return (
      <FieldValidation
        work={work}
        onNext={onNext}
        nextLabel={nextLabel}
        formikRef={formikRef}
        isSignatureTable={isSignatureTableColumn}
        isRegistrationVerification={isRegistrationVerification}
      />
    );
  }
}
