import { faSquarePlus, faTrashCan } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuthContext } from "../AuthProvider";
import { useExternalDataService } from "../services/external-data.service";
import { formatDateString, handleError, checkForRole } from "../common/utils";
import { ExternalDataSource, UploadType } from "../types/external-data.types";
import { Roles } from "../types/user.types";
import { useRuleService } from "../services/rule.service";
import { ExecutionStatus } from "../types/background-operation.types";
import ConfirmModal from "./confirm-modal";
import { InlineSpinner } from "./inline-spinner";
import { useUploadExternalDataService } from "../services/upload-externaldata.service";

interface AdminDataFilesProps {
  uploadType: UploadType;
}

export default function AdminDataFiles({ uploadType }: AdminDataFilesProps) {
  const { authenticatedUser } = useAuthContext();
  const navigate = useNavigate();
  const { getAllExternalData } = useExternalDataService();
  const { checkExternalData, deleteExternalData } =
    useUploadExternalDataService();
  const { getBackgroundOperationStatus, deleteBackgroundOperation } =
    useRuleService();

  const [dataSources, setDataSources] = useState<ExternalDataSource[]>([]);
  const [loadingDataSources, setLoadingDataSources] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [dataSourceIdToDelete, setDataSourceIdToDelete] = useState<number>(0);
  const [countOfRowsToDelete, setCountOfRowsToDelete] = useState<number>(0);
  const [showDataSourceConfirmModal, setShowDataSourceConfirmModal] =
    useState(false);

  const [isDeleting, setIsDeleting] = useState(false);
  const [backgroundId, setBackgroundId] = useState<number>();
  const interval = useRef<NodeJS.Timeout | null>(null);

  const urlPath =
    uploadType.toString()[0].toLowerCase() +
    uploadType.toString().substring(1) +
    "s";

  const loadDataSources = useCallback(() => {
    setErrorMessage("");
    setLoadingDataSources(true);

    getAllExternalData(uploadType).then(
      (dataSources) => {
        setDataSources(dataSources);
        setLoadingDataSources(false);
      },
      (error) => {
        handleError(error, setErrorMessage, setLoadingDataSources);
      }
    );
  }, [uploadType, getAllExternalData]);

  useEffect(() => {
    if (!checkForRole(Roles.Admin, authenticatedUser?.roleId)) {
      navigate("/login");
    } else {
      loadDataSources();
    }
  }, [authenticatedUser?.roleId, loadDataSources, navigate]);

  const checkDeleteProgress = useCallback(async () => {
    if (!backgroundId) {
      return;
    }
    try {
      const executionStatus = await getBackgroundOperationStatus(backgroundId);
      if (executionStatus !== ExecutionStatus.Running) {
        setIsDeleting(false);
      }
      if (executionStatus === ExecutionStatus.Succeded) {
        const success = await deleteBackgroundOperation(backgroundId);
        loadDataSources();
      }
    } catch (error) {
      return error;
    }
  }, [
    backgroundId,
    deleteBackgroundOperation,
    getBackgroundOperationStatus,
    loadDataSources,
  ]);

  useEffect(() => {
    if (isDeleting) {
      interval.current = setInterval(checkDeleteProgress, 2000);
    } else {
      if (interval.current) {
        clearInterval(interval.current);
      }
      interval.current = null;
    }

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, [checkDeleteProgress, isDeleting]);

  const handleDeleteDataSource = () => {
    if (dataSourceIdToDelete) {
      setErrorMessage("");
      setIsDeleting(true);
      deleteExternalData(dataSourceIdToDelete.toString()).then(
        (id) => {
          setBackgroundId(id);
        },
        (error) => {
          handleError(error, setErrorMessage, setLoadingDataSources);
        }
      );
    }
  };

  async function checkForDelete(dataSourceId: number): Promise<void> {
    const countOfRows = await checkExternalData(dataSourceId.toString());
    setCountOfRowsToDelete(countOfRows);
    setShowDataSourceConfirmModal(true);
    setDataSourceIdToDelete(dataSourceId);
  }

  return (
    <>
      <div className="d-flex left-right mt-4">
        <h3>{`${uploadType} Files`}</h3>
        <Link to={`/admin/${urlPath}/new`} className="btn btn-sw btn-outline">
          <FontAwesomeIcon icon={faSquarePlus} className="me-2" />
          {`New ${uploadType} File`}
        </Link>
      </div>
      <div
        className="mt-3"
        style={{ maxHeight: 400, overflowY: "auto", overflowX: "hidden" }}
      >
        {dataSources?.length > 0 && (
          <table className="sw">
            <thead>
              <tr className="d-flex">
                <th scope="col" className="col-sm-7">
                  Name
                </th>
                <th scope="col" className="col-sm-4">
                  Uploaded
                </th>
                <th scope="col" className="col-sm-1"></th>
              </tr>
            </thead>
            <tbody>
              {dataSources?.map((dataSource) => (
                <tr key={dataSource.id} className="d-flex border-bottom">
                  <td
                    className="col-sm-7"
                    onClick={() =>
                      navigate(`/admin/${urlPath}/${dataSource.id}`)
                    }
                  >
                    {dataSource.fileName}
                  </td>
                  <td className="col-sm-4">
                    {formatDateString(dataSource?.uploadedOn.toString())}
                  </td>
                  <td className="col-sm-1">
                    {dataSourceIdToDelete === dataSource.id && isDeleting ? (
                      <InlineSpinner />
                    ) : (
                      <button
                        className="btn btn-link"
                        onClick={() => checkForDelete(dataSource.id)}
                      >
                        <FontAwesomeIcon
                          style={{ color: "#741D38" }}
                          icon={faTrashCan}
                        />
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
        {!dataSources?.length && !loadingDataSources && (
          <div className="list-group-item list-group-item-light">
            {`There are no ${uploadType.toLowerCase()} files`}
          </div>
        )}
      </div>

      <ConfirmModal
        message={`There are ${countOfRowsToDelete} rows currently referencing this data source.  Are you sure you want to delete it?`}
        action={handleDeleteDataSource}
        isOpen={showDataSourceConfirmModal}
        close={() => {
          setShowDataSourceConfirmModal(false);
        }}
      />
    </>
  );
}
