interface TabOption {
  label: string;
  value: string;
  disabled?: boolean;
}

interface TabsSwitcherProps {
  options: TabOption[];
  activeTab: string;
  switchTabs: (value: string) => void;
  name: string;
}

const TabsSwitcher: React.FC<TabsSwitcherProps> = ({
  options,
  activeTab,
  switchTabs,
  name,
}) => {
  return (
    <div className="row mb-4">
      <div className="col-sm-6">
        <div className="form-group">
          {options.map(({ label, value, disabled }) => (
            <div key={value} className="form-check form-check-inline">
              <input
                id={`${name}-${value}`}
                className="form-check-input"
                type="radio"
                name={name}
                value={value}
                disabled={disabled}
                checked={activeTab === label}
                onChange={() => switchTabs(value)}
              />
              <label className="m-0" htmlFor={`${name}-${value}`}>
                {label}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TabsSwitcher;
