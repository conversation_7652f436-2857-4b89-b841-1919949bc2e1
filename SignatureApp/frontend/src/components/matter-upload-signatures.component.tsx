import { faTrashCan } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ChangeEvent, useCallback, useEffect, useRef, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { useDataProcessingLogsService } from "../services/data-processing-logs.service";
import { useDataTransformationStepService } from "../services/data-transformation-step.service";
import { useMatterStatusService } from "../services/matter-status.service";
import { useMatterService } from "../services/matter.service";
import { useRuleService } from "../services/rule.service";
import { DataTransformationStatusDTO } from "../types/data-transformation-step";
import { DocumentUploadInfo, Matter } from "../types/matter.types";
import ConfirmModal from "./confirm-modal";
import SelectTemplate from "./select-template.component";
import { useUploadService } from "../services/upload-signaturesheets.service";
import { InlineSpinner } from "./inline-spinner";

interface MatterUploadSignaturesProps {
  matter: Matter | null;
  refreshChecklist: () => void;
}

export default function MatterUploadSignatures({ matter, refreshChecklist }: MatterUploadSignaturesProps) {
  const { updateMatter } = useMatterService();
  const { startUpload, deleteUpload, getIsUploadValidAgainstExistingTemplate } = useUploadService();
  const { getUploadInfoById } = useMatterStatusService();
  const { getSignatureSheetLogsByUploadId } = useDataProcessingLogsService();
  const { getStatusByUploadId } = useDataTransformationStepService();
  const { getRuleCount } = useRuleService();

  const params = useParams();
  const matterId = params?.matterId;

  const [uploadInfo, setUploadInfo] = useState<DocumentUploadInfo>({} as DocumentUploadInfo);
  const [pipelineStatusDescription, setPipelineStatusDescription] = useState<string>();

  const [lastPipelineStatusDescription, setLastPipelineStatusDescription] = useState<string>();

  const [templateId, setTemplateId] = useState("");
  const [uploadSheetsProcessing, setUploadSheetsProcessing] = useState(false);
  const [processingLog, setProcessingLog] = useState<string>();
  const [showDeleteUploadConfirmModal, setShowDeleteUploadConfirmModal] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [signatureSheetUploadId, setSignatureSheetUploadId] = useState(0);
  const [isPipelineRunning, setIsPipelineRunning] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [pipelineStatusSame, setPipelineStatusSame] = useState(0);
  const [isSheetReviewAdded, setIsSheetReviewAdded] = useState<boolean>(matter?.isSheetReviewAdded || false);

  const interval = useRef<NodeJS.Timeout | null>(null);

  const populateUploadInfo = useCallback(async () => {
    const info = await getUploadInfoById(matterId?.toString() || "");
    setUploadInfo(info);
    if (info.lastUploadId) {
      const lastUploadId = parseInt(info.lastUploadId, 10);
      const status = await getStatusByUploadId(lastUploadId);
      const lastStatus = getPipelineStatusDescription(status);
      setLastPipelineStatusDescription(lastStatus);
    }
  }, [getUploadInfoById, getStatusByUploadId, matterId]);

  const handleTemplateChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newTemplateId = e.target.value;
    setTemplateId(newTemplateId);

    setErrorMessage("");
    if (!matterId || !newTemplateId) {
      return;
    }

    const isValid = await validateTemplateMatch(matterId, newTemplateId);
    if (!isValid) {
      setErrorMessage("The selected template's Page Size and State do not match the current template.");
    }
  };

  const [ruleCount, setRuleCount] = useState(0);

  const getCountOfRules = useCallback(async () => {
    const count = await getRuleCount(matterId?.toString() || "");
    setRuleCount(count);
  }, [getRuleCount, matterId]);

  useEffect(() => {
    getCountOfRules();
  }, [getCountOfRules]);

  const handleDeleteUpload = useCallback(
    async (uploadId: string) => {
      await deleteUpload(uploadId);
      await populateUploadInfo();
    },
    [deleteUpload, populateUploadInfo]
  );

  const changeDownloadUrl = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDownloadUrl(e.target.value);
  };

  const validateTemplateMatch = useCallback(
    async (matterId: string, templateId: string): Promise<boolean> => {
      if (!matterId || !templateId) {
        return false;
      }

      const isMatch = await getIsUploadValidAgainstExistingTemplate(matterId, templateId);
      return isMatch;
    },
    [getIsUploadValidAgainstExistingTemplate]
  );

  const handleUpload = useCallback(async () => {
    if (!matterId) {
      return;
    }
    setIsUploading(true);
    setErrorMessage("");
    try {
      const isValid = await validateTemplateMatch(matterId, templateId);
      if (!isValid) return;

      if (matter?.areTasksCreated === false) {
        await updateMatter({ ...matter, isSheetReviewAdded });
      }
      const result = await startUpload(matterId, templateId, downloadUrl);
      var uploadId = parseInt(result, 10);
      setSignatureSheetUploadId(uploadId);
      setIsPipelineRunning(true);
    } catch (errMsg: any) {
      if (errMsg.response.status === 409 || errMsg.response.status === 400) {
        setErrorMessage(errMsg.response.data);
      }
    } finally {
      setIsUploading(false);
    }
  }, [matterId, validateTemplateMatch, templateId, matter, startUpload, downloadUrl, updateMatter, isSheetReviewAdded]);

  const startGettingLogs = useCallback(
    (uploadId: string) => {
      let interval = setInterval(async () => {
        const logs = await getSignatureSheetLogsByUploadId(uploadId);
        if (logs && logs.length) {
          const lastLog = logs[logs.length - 1];
          if (lastLog.operation.includes("finished")) {
            clearInterval(interval);
            setUploadSheetsProcessing(false);
            await getCountOfRules();
            await populateUploadInfo();
            refreshChecklist();
          }
          setProcessingLog(lastLog.operation);
        }
      }, 3000);
    },
    [getCountOfRules, getSignatureSheetLogsByUploadId, populateUploadInfo, refreshChecklist]
  );

  function getIsPipelineFinished(status: DataTransformationStatusDTO) {
    return (
      status.isDownloadingStarted &&
      status.numFilesDownloaded >= 1 &&
      status.numFilesDownloaded === status.numFilesIntoSplitAndMerge &&
      status.numFilesSplitOrMerged >= status.numFilesIntoSplitAndMerge / 2 &&
      status.numFilesIntoValidation >= status.numFilesSplitOrMerged &&
      status.numFilesValid + status.numFilesInvalid + status.numFilesDuplicated === status.numFilesIntoValidation
    );
  }

  const checkDataPipelineProgress = useCallback(async () => {
    try {
      const status = await getStatusByUploadId(signatureSheetUploadId);
      const newStatusDescription = getPipelineStatusDescription(status);

      setPipelineStatusDescription(newStatusDescription);
      if (pipelineStatusDescription === newStatusDescription) {
        setPipelineStatusSame(pipelineStatusSame + 1);
      } else {
        setPipelineStatusSame(0);
      }
      const isPipelineFinished = getIsPipelineFinished(status);
      if (isPipelineFinished || pipelineStatusSame > 105) {
        // 3 seconds * 105 is a little over a 5 minutes and
        // right now the queue interval goes up to 30 seconds
        setIsPipelineRunning(false);
        setPipelineStatusSame(0);
        await getCountOfRules();
        await populateUploadInfo();
        refreshChecklist();
      }
    } catch (error) {
      return error;
    }
  }, [
    getCountOfRules,
    getStatusByUploadId,
    pipelineStatusDescription,
    pipelineStatusSame,
    populateUploadInfo,
    refreshChecklist,
    signatureSheetUploadId,
  ]);

  const getPipelineStatusDescription = (status: DataTransformationStatusDTO): string => {
    let description = "";
    description += `DownloadFiles: Downloaded ${status.numFilesDownloaded} files.\n`;
    description += `SplitAndMerge: ${status.numFilesIntoSplitAndMerge} file(s) in, ${status.numFilesSplitOrMerged} file(s) written.\n`;
    description += `DocumentIntelligence: ${status.numFilesIntoValidation} file(s) submitted, ${status.numFilesValid} valid, ${status.numFilesInvalid} invalid, ${status.numFilesDuplicated} duplicated.\n`;
    return description;
  };

  const handleRePoll = useCallback(async () => {
    const lastUploadId = parseInt(uploadInfo?.lastUploadId, 10);
    if (lastUploadId) {
      setSignatureSheetUploadId(lastUploadId);
      setIsPipelineRunning(true);
    }
  }, [uploadInfo?.lastUploadId]);

  useEffect(() => {
    if (isPipelineRunning) {
      interval.current = setInterval(checkDataPipelineProgress, 3000);
    } else {
      if (interval.current) {
        clearInterval(interval.current);
      }
      interval.current = null;
    }

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, [checkDataPipelineProgress, isPipelineRunning]);

  // Startup
  useEffect(() => {
    populateUploadInfo();
  }, [populateUploadInfo]); // Empty dependency array ensures this runs only once on mount

  function handleIsSheetReviewChanged(event: ChangeEvent<HTMLInputElement>): void {
    const isChecked = event.target.checked;
    setIsSheetReviewAdded(isChecked);
  }

  return (
    <>
      <div className="mt-4">
        <h3>Signed Petitions</h3>
        <div className="row">
          <div className="col-sm-6">
            <SelectTemplate templateChange={handleTemplateChange} />
          </div>
          <div className="col-sm-6">
            <input
              id="add-sheet-review-checkbox"
              className="form-check-input"
              type="checkbox"
              checked={isSheetReviewAdded}
              onChange={handleIsSheetReviewChanged}
              disabled={matter?.areTasksCreated || false}
            />
            <label htmlFor="add-sheet-review-checkbox" className="ms-2">
              Add Sheet Review Task
            </label>
          </div>
        </div>
        <div className="form-group">
          <div className="input-group">
            <input
              name="downloadUrl"
              type="text"
              placeholder="Download URL"
              className="form-control"
              value={downloadUrl}
              onChange={changeDownloadUrl}
            />
            <button type="button" onClick={handleUpload} className="btn btn-sw" disabled={isUploading}>
              {isUploading && <InlineSpinner />}
              Start
            </button>
          </div>
        </div>
        {errorMessage && (
          <div>
            <span className="small error">{errorMessage}</span>
          </div>
        )}
        {isPipelineRunning && (
          <div>
            <InlineSpinner />
            <span className="small" style={{ whiteSpace: "pre-wrap" }}>
              {pipelineStatusDescription}
            </span>
          </div>
        )}
        {!isPipelineRunning && !!uploadInfo?.uploadCount && (
          <>
            <span className="small">
              {`Total Uploads: ${uploadInfo?.uploadCount || 0}. Total Valid Sheets: ${
                uploadInfo?.totalValidCount || 0
              }.  Total Invalid Sheets ${uploadInfo?.totalInvalidCount || 0}.`}
            </span>
            <button className="btn btn-sm btn-sw ms-2 me-2" onClick={handleRePoll}>
              Re-Poll
            </button>
            <Link to={`/admin/matters/${matterId}/signature-sheet-upload-history`}>
              <span className="small">Upload history</span>
            </Link>

            <div>
              <span className="small">
                {`Last upload by ${uploadInfo?.lastUploadedBy} on ${uploadInfo?.lastUploadedOn}`}
              </span>
              <a role="button" className="ms-2" onClick={() => handleDeleteUpload(uploadInfo?.lastUploadId)}>
                <FontAwesomeIcon icon={faTrashCan} className="pt-1" />
              </a>
            </div>
            <div>
              <span className="small" style={{ whiteSpace: "pre-wrap" }}>
                {lastPipelineStatusDescription}
              </span>
            </div>
          </>
        )}
      </div>
      <div>
        <span className="small">{ruleCount} rules have been created.</span>
      </div>
      <ConfirmModal
        message="Are you sure you want to delete the most recent upload (all of its sheets and work)?"
        action={() => handleDeleteUpload(uploadInfo?.lastUploadId)}
        isOpen={showDeleteUploadConfirmModal}
        close={() => {
          setShowDeleteUploadConfirmModal(false);
        }}
      />
    </>
  );
}
