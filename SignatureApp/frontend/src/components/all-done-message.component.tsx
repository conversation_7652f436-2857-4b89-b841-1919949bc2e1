import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheckCircle } from "@fortawesome/free-solid-svg-icons";

interface AllDoneMessageProps {
  title?: string;
  message?: string;
}

const AllDoneMessage: React.FC<AllDoneMessageProps> = ({
  title = "All Done!",
  message = "There are no more invalid sheets to review at this time."
}) => {
  return (
    <div className="container-fluid d-flex justify-content-center align-items-center" style={{ minHeight: "60vh" }}>
      <div className="text-center">
        <FontAwesomeIcon 
          icon={faCheckCircle} 
          className="text-success mb-3" 
          style={{ fontSize: "4rem" }}
        />
        <h2 className="mb-3">{title}</h2>
        <p className="text-muted lead">{message}</p>
      </div>
    </div>
  );
};

export default AllDoneMessage;
