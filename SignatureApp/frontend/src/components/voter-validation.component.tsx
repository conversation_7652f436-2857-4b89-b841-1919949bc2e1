import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Field, FieldArray, Form, Formik, FormikProps } from "formik";
import { Ref, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { handleError } from "../common/utils";
import { useExternalDataService } from "../services/external-data.service";
import { ExternalData, ExternalDataField } from "../types/external-data.types";
import {
  RegisteredVoterFlags,
  ValidationForm,
  WorkDto,
  WorkField,
} from "../types/work.types";
import { InlineSpinner } from "./inline-spinner";

interface FieldValidationProps {
  work: WorkDto;
  formikRef: Ref<FormikProps<ValidationForm>> | null;
  onNext?: (
    fields: WorkField[],
    externalDataRecordId: string | null,
    registeredVoterFlags: RegisteredVoterFlags
  ) => void;
  nextLabel: string | null;
}

export default function VoterValidation({
  work,
  formikRef,
  onNext,
  nextLabel,
}: FieldValidationProps) {
  const { getWorkVoter, voterRegistrationSearch } = useExternalDataService();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [voterRegistrations, setVoterRegistrations] = useState<
    ExternalData[] | null
  >(null);
  const [totalHits, setTotalHits] = useState(0);

  const mountedRef = useRef(true);
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, [mountedRef]);

  const workFields = useMemo(
    () => work.fields.map((field, index) => ({ field, index })),
    [work.fields]
  );

  const addressFields = useMemo(() => {
    const addressField = workFields.find(({ field }) =>
      field.name?.toLowerCase().includes("address")
    );
    return addressField ? [addressField] : [];
  }, [workFields]);

  const nameFields = useMemo(() => {
    const fields = workFields.filter((fi) => {
      const loweredName = fi.field.name?.toLowerCase();
      return (
        fi.field.canBeInvalid &&
        (loweredName.includes("name") ||
          loweredName.includes("first") ||
          loweredName.includes("last"))
      );
    });
    return fields;
  }, [workFields]);

  const searchFields = useMemo(() => {
    return [...nameFields, ...addressFields];
  }, [nameFields, addressFields]);

  const handleSearch = useCallback(
    (fields: WorkField[], setFieldValue) => {
      var searchFieldNames = searchFields.map((fi) => fi.field.name);
      const externalDataFields = fields.filter((f) =>
        searchFieldNames.includes(f.name)
      );

      if (work?.workId) {
        setErrorMessage("");
        setLoading(true);
        voterRegistrationSearch(
          work.workId.toString(),
          externalDataFields
        ).then(
          (externalData) => {
            if (!mountedRef.current) {
              return;
            }
            setLoading(false);
            setVoterRegistrations(externalData.results);
            console.log(`Search results`, externalData);
            setFieldValue("voterRegistrations", externalData.results);
            setTotalHits(externalData.totalHits);
          },
          (error) => {
            if (!mountedRef.current) {
              return;
            }
            handleError(error, setErrorMessage, setLoading);
          }
        );
      }
    },
    [searchFields, work.workId, voterRegistrationSearch]
  );

  useEffect(() => {
    if (!work?.workId) {
      return;
    }
    if (work.externalDataRecordId) {
      getWorkVoter(work.workId, work.externalDataRecordId.toString()).then(
        (externalData) => {
          if (!mountedRef.current) {
            return;
          }
          console.log(`Previous search results`, externalData);
          setVoterRegistrations([externalData]);
        },
        (error) => {
          if (!mountedRef.current) {
            return;
          }
          handleError(error, setErrorMessage, setLoading);
        }
      );
    } else if (searchFields.find((fi) => fi.field.value)) {
      // If any search field has a value
      const externalDataFields = searchFields.map((fi) => ({
        name: fi.field.name,
        value: fi.field.value,
      }));

      setErrorMessage("");
      setLoading(true);

      voterRegistrationSearch(work.workId.toString(), externalDataFields).then(
        (externalData) => {
          if (!mountedRef.current) {
            return;
          }
          setLoading(false);
          console.log(`Initial search results`, externalData);
          setVoterRegistrations(externalData.results);
          setTotalHits(externalData.totalHits);
        },
        (error) => {
          if (!mountedRef.current) {
            return;
          }
          handleError(error, setErrorMessage, setLoading);
        }
      );
    }
  }, [work, getWorkVoter, voterRegistrationSearch, searchFields]);

  const handleNext = (formValues: ValidationForm) => {
    // Only update externalDataRecordId from form
    onNext?.(
      work.fields,
      formValues.externalDataRecordId ?? null,
      formValues.registeredVoterFlags
    );
  };

  const getFieldValues = (
    fields: ExternalDataField[],
    subset: { field: ExternalDataField; index: number }[]
  ) => {
    const values: string[] = [];
    for (const field of fields) {
      if (subset.map((fi) => fi.field.name).includes(field.name)) {
        values.push(field.value);
      }
    }
    return values.join(" ");
  };

  return (
    <div className="row mt-3">
      <Formik
        innerRef={formikRef}
        enableReinitialize
        initialValues={
          {
            fields: work.fields,
            externalDataRecordId: work.externalDataRecordId?.toString() || "",
            registeredVoterFlags: work.registeredVoterFlags,
            voterRegistrations,
            totalHits,
          } as ValidationForm
        }
        validate={(values) => {
          const errors = {} as any;
          if (
            values.registeredVoterFlags === RegisteredVoterFlags.Valid &&
            values.externalDataRecordId === ""
          ) {
            errors.fieldName =
              "A valid voter registration must have a selected voter";
          }
          return errors;
        }}
        onSubmit={handleNext}
      >
        {({ values, isSubmitting, setFieldValue }) => (
          <Form>
            <div className="row mb-2">
              {searchFields.map((fi) => (
                <div className="col form-group" key={fi.field?.id}>
                  <label
                    htmlFor={fi.field?.name}
                    style={{ whiteSpace: "nowrap" }}
                  >
                    {fi.field?.name}
                  </label>
                  <Field
                    name={`fields[${fi.index}].value`}
                    className="form-control"
                    placeholder={`Enter ${fi.field?.name}`}
                    type="text"
                  />
                </div>
              ))}
            </div>

            <div className="row mb-2">
              <div className="col-sm-4">
                <button
                  type="button"
                  className="btn btn-sw"
                  onClick={() => {
                    handleSearch(values.fields, setFieldValue);
                  }}
                  disabled={loading}
                >
                  {loading && <InlineSpinner />}
                  Search
                </button>
              </div>
              <div className="col-sm-8 pt-2 text-end">
                {totalHits > 0 && (
                  <span className="text-muted">
                    Showing {voterRegistrations?.length} of {totalHits} possible
                    matches
                  </span>
                )}
              </div>
            </div>
            <div className="row mb-2">
              <div className="col">
                <FieldArray name="voterRegistrations">
                  {() => (
                    <div className="border-left border-bottom border-right ps-2 rounded">
                      <div
                        style={{
                          maxHeight: 125,
                          overflowY: "auto",
                          overflowX: "hidden",
                        }}
                      >
                        {voterRegistrations &&
                          voterRegistrations.length > 0 && (
                            <div className="row" key="voterRegistration-none">
                              <div className="col-sm-6">
                                <div className="form-group">
                                  <div className="form-check form-check-inline">
                                    <Field
                                      id="externalDataRecordId-0"
                                      className="form-check-input"
                                      type="radio"
                                      name="externalDataRecordId"
                                      required
                                      onChange={() =>
                                        setFieldValue(
                                          "externalDataRecordId",
                                          ""
                                        )
                                      }
                                      checked={
                                        values.externalDataRecordId === ""
                                      }
                                    />
                                    <label
                                      className="m-0"
                                      htmlFor="externalDataRecordId-0"
                                    >
                                      None of these
                                    </label>
                                  </div>
                                </div>
                              </div>
                              <div className="col-sm-6"></div>
                            </div>
                          )}

                        {voterRegistrations &&
                          voterRegistrations.length > 0 &&
                          voterRegistrations?.map((voterRegistration) => (
                            <div
                              className="row"
                              key={`voterRegistration-${voterRegistration.id}`}
                            >
                              <div className="col-sm-6">
                                <div className="form-check form-check-inline">
                                  <Field
                                    id={`externalDataRecordId-${voterRegistration.id}`}
                                    className="form-check-input"
                                    type="radio"
                                    name="externalDataRecordId"
                                    required
                                    value={voterRegistration.id.toString()}
                                    onChange={() => {
                                      setFieldValue(
                                        "externalDataRecordId",
                                        voterRegistration.id.toString()
                                      );
                                      setFieldValue(
                                        "registeredVoterFlags",
                                        RegisteredVoterFlags.Valid
                                      );
                                    }}
                                  />
                                  <label
                                    className="m-0"
                                    htmlFor={`externalDataRecordId-${voterRegistration.id}`}
                                  >
                                    {getFieldValues(
                                      voterRegistration.fields,
                                      nameFields
                                    )}
                                  </label>
                                </div>
                              </div>
                              <div className="col-sm-6">
                                <label
                                  className="m-0"
                                  htmlFor={`externalDataRecordId-${voterRegistration.id}`}
                                >
                                  {getFieldValues(
                                    voterRegistration.fields,
                                    addressFields
                                  )}
                                </label>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </FieldArray>
              </div>
            </div>

            <div className="row mb-2">
              <div className="col-sm-3">
                <div className="form-group">
                  <div className="form-check form-check-inline">
                    <Field
                      id="noMismatch"
                      className="form-check-input"
                      type="radio"
                      name="registeredVoterFlags"
                      required
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        setFieldValue(
                          "registeredVoterFlags",
                          RegisteredVoterFlags.Valid
                        );
                      }}
                      checked={
                        values.registeredVoterFlags ===
                        RegisteredVoterFlags.Valid
                      }
                    />
                    <label className="m-0" htmlFor="noMismatch">
                      Valid/No Mismatch
                    </label>
                  </div>
                </div>
              </div>

              <div className="col-sm-3">
                <div className="form-group">
                  <div className="form-check form-check-inline">
                    <Field
                      id="mismatchedName"
                      className="form-check-input"
                      type="radio"
                      name="registeredVoterFlags"
                      required
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        setFieldValue(
                          "registeredVoterFlags",
                          RegisteredVoterFlags.MismatchedName
                        );
                      }}
                      checked={
                        values.registeredVoterFlags ===
                        RegisteredVoterFlags.MismatchedName
                      }
                    />
                    <label className="m-0" htmlFor="mismatchedName">
                      Mismatched Name
                    </label>
                  </div>
                </div>
              </div>

              <div className="col-sm-3">
                <div className="form-group">
                  <div className="form-check form-check-inline">
                    <Field
                      id="mismatchedAddress"
                      className="form-check-input"
                      type="radio"
                      name="registeredVoterFlags"
                      required
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        setFieldValue(
                          "registeredVoterFlags",
                          RegisteredVoterFlags.MismatchedAddress
                        );
                      }}
                      checked={
                        values.registeredVoterFlags ===
                        RegisteredVoterFlags.MismatchedAddress
                      }
                    />
                    <label className="m-0" htmlFor="mismatchedAddress">
                      Mismatched Address
                    </label>
                  </div>
                </div>
              </div>

              <div className="col-sm-3">
                <div className="form-group">
                  <div className="form-check form-check-inline">
                    <Field
                      id="notRegistered"
                      className="form-check-input"
                      type="radio"
                      name="registeredVoterFlags"
                      required
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        setFieldValue(
                          "registeredVoterFlags",
                          RegisteredVoterFlags.NotRegistered
                        );
                      }}
                      checked={
                        values.registeredVoterFlags ===
                        RegisteredVoterFlags.NotRegistered
                      }
                    />
                    <label className="m-0" htmlFor="notRegistered">
                      Not Registered
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {errorMessage && (
              <div className="row mt-1">
                <div className="col">
                  <div className="form-group">
                    <div className="alert alert-danger" role="alert">
                      {errorMessage}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="row text-center">
              <div className="col-sm-12 mt-1">
                <div className="form-group">
                  <button
                    type="submit"
                    className="btn btn-secondary"
                    disabled={isSubmitting}
                  >
                    {!isSubmitting && (
                      <FontAwesomeIcon icon={faArrowRight} className="me-2" />
                    )}
                    {isSubmitting && <InlineSpinner />}
                    {nextLabel || "Next"}
                  </button>
                </div>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}
