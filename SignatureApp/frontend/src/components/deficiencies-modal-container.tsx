import { useCallback, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { checkForRole } from "../common/utils";
import { Roles } from "../types/user.types";
import { useAuthContext } from "../AuthProvider";
import { useRuleService } from "../services/rule.service";
import { ViolationCriterion } from "../types/work.types";
import { useDeficiencyService } from "../services/deficiency.service";
import { RecordIdType } from "../types/deficiency.types";
import {
  DeficiencyModalRow,
  EditDeficienciesModal,
} from "./edit-deficiencies-modal";

export interface DeficienciesModalContainerProps {
  showDeficienciesModal: boolean;
  setShowDeficienciesModal: (isShown: boolean) => void;
  signatureSheetId: number | undefined;
}

export default function DeficienciesModalContainer({
  showDeficienciesModal,
  setShowDeficienciesModal,
  signatureSheetId,
}: DeficienciesModalContainerProps) {
  const navigate = useNavigate();
  const { matterId } = useParams();

  const { authenticatedUser } = useAuthContext();
  const { getRules, getSheetViolations } = useRuleService();
  const {
    getSheetDeficienciesById,
    postSheetDeficiency,
    deleteSheetDeficiency,
  } = useDeficiencyService();
  const [sheetDeficiencies, setSheetDeficiencies] = useState<
    DeficiencyModalRow[]
  >([]);
  const [sheetViolations, setSheetViolations] = useState<ViolationCriterion[]>(
    []
  );
  const [errorMessage, setErrorMessage] = useState("");

  const loadSheetDeficiencies = useCallback(() => {
    if (signatureSheetId) {
      getSheetDeficienciesById(signatureSheetId).then(
        (def) => {
          const deficiencies = def.map((d) => ({
            description: d.ruleName,
            ruleId: d.ruleId,
          }));
          setSheetDeficiencies(deficiencies);
          setErrorMessage("");
        },
        (error) => {
          setErrorMessage("Error loading deficiencies");
        }
      );
    }
  }, [getSheetDeficienciesById, signatureSheetId]);

  useEffect(() => {
    loadSheetDeficiencies();
  }, [loadSheetDeficiencies, signatureSheetId]);

  useEffect(() => {
    if (!checkForRole(Roles.Admin, authenticatedUser?.roleId)) {
      navigate("/login");
      return;
    }
    if (matterId) {
      getSheetViolations(matterId).then(
        (r) => {
          setSheetViolations(r);
        },
        (error) => {
          setErrorMessage("Error fetching");
        }
      );
    }
  }, [
    authenticatedUser?.roleId,
    matterId,
    getRules,
    navigate,
    getSheetViolations,
  ]);

  function handleAddSheetDeficiency(
    violationId: number,
    violationNote: string | null
  ) {
    if (!signatureSheetId) {
      return;
    }
    postSheetDeficiency(signatureSheetId, violationId, violationNote).then(
      (data) => loadSheetDeficiencies(),
      (error) => {
        setErrorMessage("Error adding");
      }
    );
  }

  const handleDeleteSheetDeficiency = (violationId: number) => {
    if (!signatureSheetId) {
      return;
    }
    deleteSheetDeficiency(signatureSheetId, violationId).then(
      (data) => {
        loadSheetDeficiencies();
      },
      (error) => {
        setErrorMessage("Error deleting");
      }
    );
  };

  return (
    <EditDeficienciesModal
      isOpen={showDeficienciesModal}
      deficiencies={sheetDeficiencies}
      type={RecordIdType.SignatureSheet}
      close={() => {
        setShowDeficienciesModal(false);
      }}
      addDeficiency={(form) => {
        handleAddSheetDeficiency(
          form.violationId as number,
          form.violationNote
        );
      }}
      deleteDeficiency={(violationId) => {
        handleDeleteSheetDeficiency(violationId);
      }}
      errorMessage={errorMessage}
      violationCriteria={sheetViolations}
    />
  );
}
