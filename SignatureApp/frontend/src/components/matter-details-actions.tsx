import { MouseEvent, useEffect, useRef, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { handleError } from "../common/utils";
import { useDeficiencyService } from "../services/deficiency.service";
import { useInvalidSheetService } from "../services/invalid-sheet.service";
import { useMatterService } from "../services/matter.service";
import { useWorkService } from "../services/work.service";
import { useRuleService } from "../services/rule.service";
import CircleCounterIcon from "../components/circle-counter-icon";

export default function MatterDetails() {
  const navigate = useNavigate();
  const { matterId } = useParams();
  const { getValidSignaturesCsv } = useMatterService();
  const { getReviewableDeficiencyCountByMatter } = useDeficiencyService();
  const { getFlaggedWorkCount } = useWorkService();
  const { getInvalidSheetCountByMatter } = useInvalidSheetService();
  const { runRules } = useRuleService();

  const [errorExport, setErrorExport] = useState("");
  const [deficiencyReviewCount, setDeficiencyReviewCount] = useState(0);
  const [transcriptionReviewCount, setTranscriptionReviewCount] = useState(0);
  const [invalidSheetCount, setInvalidSheetCount] = useState(0);

  const csvLink = useRef<HTMLAnchorElement>(null);

  useEffect(() => {
    if (matterId) {
      getFlaggedWorkCount(matterId).then((flaggedWorkCount) => {
        setTranscriptionReviewCount(flaggedWorkCount);
      });
    }
  }, [getFlaggedWorkCount, matterId]);

  useEffect(() => {
    if (matterId) {
      getReviewableDeficiencyCountByMatter(matterId).then((count) => {
        setDeficiencyReviewCount(count);
      });
    }
  }, [getReviewableDeficiencyCountByMatter, matterId]);

  useEffect(() => {
    if (matterId) {
      getInvalidSheetCountByMatter(matterId).then((count) => {
        setInvalidSheetCount(count);
      });
    }
  }, [getInvalidSheetCountByMatter, matterId]);

  const exportValidSignatures = () => {
    setErrorExport("");
    getValidSignaturesCsv(matterId!).then(
      (csv) => {
        csvLink.current?.setAttribute(
          "download",
          `Matter${matterId}ValidSignatures.csv`
        );
        csvLink.current?.setAttribute(
          "href",
          URL.createObjectURL(new Blob([csv], { type: "text/csv" }))
        );
        csvLink.current?.click();
      },
      (error) => {
        handleError(error, setErrorExport);
      }
    );
  };

  const handleForceRunRules = async () => {
    try {
      await runRules(matterId!, true);
      // Optionally show a success message or handle the response
    } catch (error) {
      handleError(error, setErrorExport);
    }
  };

  function handleNavigateToDeficiencyReview(
    e: MouseEvent<HTMLAnchorElement, globalThis.MouseEvent>
  ): void {
    e.preventDefault();
    navigate(`/admin/matters/${matterId}/deficiencies/review`, {
      state: { from: window.location.pathname },
    });
  }

  return (
    <div className="d-flex flex-row gap-2">
      <Link
        to={
          deficiencyReviewCount > 0
            ? `/admin/matters/${matterId}/deficiencies/review`
            : "#"
        }
        className={`btn btn-sw btn-sm btn-outline${
          deficiencyReviewCount <= 0 ? " disabled" : ""
        }`}
        aria-disabled={deficiencyReviewCount <= 0}
        onClick={(e) => {
          if (deficiencyReviewCount <= 0) {
            e.preventDefault();
            return;
          }
          handleNavigateToDeficiencyReview(e);
        }}
      >
        <div className="ps-2">
          <CircleCounterIcon count={deficiencyReviewCount} />
          Deficiency Review
        </div>
      </Link>
      <Link
        to={`/admin/matters/${matterId}/sheets/invalid`}
        className={`btn btn-sw btn-sm btn-outline${
          invalidSheetCount <= 0 ? " disabled" : ""
        }`}
        aria-disabled={invalidSheetCount <= 0}
      >
        <div className="ps-2">
          <CircleCounterIcon count={invalidSheetCount} />
          Invalid Sheet Review
        </div>
      </Link>
      <Link
        to={`/admin/matters/${matterId}/review/transcription`}
        className={`btn btn-sw btn-sm btn-outline${
          transcriptionReviewCount <= 0 ? " disabled" : ""
        }`}
        aria-disabled={transcriptionReviewCount <= 0}
      >
        <div className="ps-2">
          <CircleCounterIcon count={transcriptionReviewCount} />
          Transcription Review
        </div>
      </Link>
      <div className="ms-auto" style={{ marginRight: "-12px" }}>
        <div className="dropdown">
          <button
            className="btn btn-sw btn-sm btn-outline dropdown-toggle"
            type="button"
            id="dropdownMenuButton"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
          >
            Actions
          </button>
          <div className="dropdown-menu" aria-labelledby="dropdownMenuButton">
            <a ref={csvLink} style={{ display: "none" }} target="_blank"></a>

            <Link
              to={`/admin/matters/${matterId}/deficiencies`}
              className="dropdown-item"
            >
              {/* <FontAwesomeIcon icon={faCircleUp} className="me-2" /> */}
              Deficiencies
            </Link>

            <button onClick={exportValidSignatures} className="dropdown-item">
              Export Valid
            </button>

            <button onClick={handleForceRunRules} className="dropdown-item">
              Force Run Rules
            </button>

            <Link
              to={`/admin/matters/${matterId}/sheets`}
              className="dropdown-item"
            >
              Signature Sheets
            </Link>

            <Link
              to={`/admin/matters/${matterId}/upload-invalid`}
              className="dropdown-item"
            >
              {/* <FontAwesomeIcon icon={faCircleUp} className="me-2" /> */}
              Upload Invalid
            </Link>

            <Link
              to={`/admin/matters/${matterId}/free-form-voter-search`}
              className="dropdown-item"
            >
              {/* <FontAwesomeIcon icon={faCircleUp} className="me-2" /> */}
              Voter Search
            </Link>
            <a
              href={`${process.env.REACT_APP_BACKEND_URL?.replace(
                "/api",
                ""
              )}healthchecks-ui#/healthchecks`}
              target="_blank"
              rel="noopner noreferrer"
              className="dropdown-item"
            >
              Health Check
            </a>
            
          </div>
        </div>
      </div>
    </div>
  );
}
