import { faPlayCircle } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useTaskService } from "../services/task.service";
import SelectTemplate from "./select-template.component";

interface MatterCreateTasksProps {
  templateChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  createTasks: () => void;
}

export default function MatterCreateTasks(props: MatterCreateTasksProps) {
  const params = useParams();
  const matterId = params.matterId!;
  const {getTaskCount} = useTaskService();
  const [taskCount, setTaskCount] = useState(0);
  
  const handleCreateTasks = () => {
    props.createTasks();
    getCountOfTasks();
  }

  const getCountOfTasks = useCallback(() => {
    getTaskCount(matterId?.toString() || "").then(
      (count: number) => {
        setTaskCount(count);
      }
    );
  }, [getTaskCount, matterId]);

  useEffect(() => {
    getCountOfTasks();
  }, [getCountOfTasks]);  

  return (
    <div>
      <div className="d-flex left-right mt-4">
        <h3>Tasks</h3>
        <SelectTemplate templateChange={props.templateChange} />
        <button className="btn btn-sw btn-outline" onClick={handleCreateTasks}>
          <FontAwesomeIcon icon={faPlayCircle} className="me-2" />
          Create Tasks
        </button>
      </div>
      <div>
        <span className="small">{taskCount} tasks have been created.</span>
      </div>
    </div>
  );
}
