import { useCallback, useState } from "react";
import { TaskType, WorkDto } from "../types/work.types";
import { Loader } from "./loader";

interface PreviewWorkItemsProps {
  isRelease: boolean;
  isLoading: boolean;
  workItems: WorkDto[];
  workReleasable?: boolean[];
  setWorkReleasable?: (updater: (prev: boolean[]) => boolean[]) => void;
}

export function PreviewWorkItems({
  workItems,
  isRelease,
  isLoading,
  workReleasable: externalWorkReleasable,
  setWorkReleasable: externalSetWorkReleasable,
}: PreviewWorkItemsProps) {
  const [internalWorkReleasable, setInternalWorkReleasable] = useState<
    boolean[]
  >(workItems.map((wi) => true));

  // Use external state if provided, otherwise use internal state
  const workReleasable = externalWorkReleasable || internalWorkReleasable;
  const setWorkReleasable =
    externalSetWorkReleasable || setInternalWorkReleasable;

  const getId = useCallback((work: WorkDto): string => {
    let id = "";
    if (work.fieldNumber) {
      if (id) {
        id += "-";
      }
      id += `field-${work.fieldNumber}`;
    }
    if (work.rowNumber) {
      if (id) {
        id += "-";
      }
      id += `row-${work.rowNumber}`;
    }
    const firstWord = work.taskName.split(" ")[0];
    id += "-" + firstWord;
    return id;
  }, []);

  function handleWorkClick(work: WorkDto): void {
    const workIndex = workItems.findIndex((wi) => wi.workId === work.workId);
    if (workIndex !== -1) {
      setWorkReleasable((prev) => {
        const newState = [...prev];
        newState[workIndex] = !newState[workIndex];
        return newState;
      });
    }
  }

  if (isLoading) {
    return <Loader />;
  }

  return (
    <>
      {workItems.map((work, index) => (
        <div key={work.workId} className="row text-center">
          {isRelease && (
            <div className="col-sm-2 d-flex align-items-center">
              <input
                id={getId(work)}
                className="ms-1"
                type="checkbox"
                checked={workReleasable[index]}
                onChange={() => handleWorkClick(work)}
              />
              <label className="ms-1 me-1" htmlFor={getId(work)}>
                {work.rowNumber || ""}
                {work.fieldNumber || ""} - {work.taskName}
              </label>
            </div>
          )}
          <div className={isRelease ? "col-sm-10" : "col-sm-12"}>
            {!!work.image?.fileContents && (
              <img
                alt="transcribable field"
                style={{
                  objectFit: "scale-down",
                  width:
                    (work.taskTypeId === TaskType.SignatureTableColumn &&
                      !work.showWholeColumn) ||
                    work.taskTypeId === TaskType.TranscribableField
                      ? "100%"
                      : undefined,
                  border: "1px black solid",
                }}
                src={`data:image/png;base64,${work.image.fileContents}`}
              />
            )}
          </div>
        </div>
      ))}
    </>
  );
}
