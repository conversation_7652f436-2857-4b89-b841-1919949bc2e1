import { useRef, useState } from "react";
import { useAuthContext } from "../AuthProvider";
import { FileProgress, MultiFileProgress } from "../types/file.types";
import { useUploadFileService } from "../services/upload-file.service";

interface UploadProps {
  filePath?: string;
  uploadUrlPath: string;
  fileTypes: string;
  isMultiFile?: boolean;
  isValid?: () => boolean;
  isSynchronousUpload: boolean;
  onUploadProcessing?: () => void;
  onUploadComplete?: (result: string) => void;
  onUploadError?: (msg: string) => void;
  onFileSelected?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onReplaceFile?: () => void;
  additionalData?: { [key: string]: string };
}

export default function UploadFiles({
  filePath,
  uploadUrlPath,
  fileTypes,
  isMultiFile = false,
  isSynchronousUpload,
  isValid = () => {
    return true;
  },
  onUploadProcessing,
  onUploadComplete,
  onUploadError,
  onFileSelected,
  onReplaceFile,
  additionalData,
}: UploadProps) {
  const { authenticatedUser } = useAuthContext();
  const { uploadFile } = useUploadFileService();
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [multiFileProgress, setMultiFileProgress] = useState<MultiFileProgress>(
    {
      fileProgressArray: [],
    }
  );
  const [message, setMessage] = useState<string[]>([]);
  const multiFileProgressRef = useRef<MultiFileProgress>({
    fileProgressArray: [],
  });

  const selectFiles = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMultiFileProgress({ fileProgressArray: [] });
    setSelectedFiles(e.target.files);
    if (onFileSelected) {
      onFileSelected(e);
    }
  };

  const uploadFiles = () => {
    if (selectedFiles) {
      const files = Array.from(selectedFiles);
      let fileProgressArray = files.map((file) => ({
        percentage: 0,
        fileName: file.name,
      }));

      multiFileProgressRef.current = {
        fileProgressArray: fileProgressArray,
      };

      files.map((file, i) => upload(file, i));
      setMessage([]);
    }
  };

  const upload = (file: File, index: number) => {
    let fileProgressArray = [...multiFileProgressRef.current.fileProgressArray];

    uploadFile(
      file,
      uploadUrlPath,
      authenticatedUser?.accessToken!,
      {
        setUploadProgress: (percentage: number) => {
          if (percentage === 100 && isSynchronousUpload && onUploadProcessing) {
            onUploadProcessing();
          }
          fileProgressArray[index].percentage = percentage;
          setMultiFileProgress({
            fileProgressArray: fileProgressArray,
          });
        },
      },
      additionalData
    )
      .then((body) => {
        setMessage((prevMessage) => [
          ...prevMessage,
          `File uploaded successfully: ${file.name}`,
        ]);

        if (onUploadComplete) {
          onUploadComplete(body.data);
        }
      })
      .catch((error) => {
        fileProgressArray[index].percentage = 0;
        setMultiFileProgress({
          fileProgressArray: fileProgressArray,
        });
        const message = error.response?.data || error;
        setMessage((prevMessage) => [
          ...prevMessage,
          `Could not upload file:\nError: ${message}`,
        ]);

        if (onUploadError) {
          onUploadError(message);
        }
      });
  };

  return (
    <>
      {filePath ? (
        <div className="input-group">
          <input
            className="form-control"
            type="text"
            value={filePath}
            readOnly
          />
          <button className="btn btn-sw" onClick={onReplaceFile}>
            Change
          </button>
        </div>
      ) : (
        <>
          <div className="input-group">
            <input
              className="form-control"
              type="file"
              {...(isMultiFile === true ? { multiple: true } : {})}
              accept={fileTypes}
              onChange={selectFiles}
            />
            <button
              className="btn btn-sw"
              disabled={!selectedFiles || !isValid()}
              onClick={uploadFiles}
            >
              Upload
            </button>
          </div>
          <div style={{ maxHeight: 200 }} className="overflow-auto">
            {multiFileProgress &&
              multiFileProgress.fileProgressArray.length > 0 &&
              multiFileProgress.fileProgressArray.map(
                (fileProgress: FileProgress, index: number) => (
                  <div className="mb-2" key={index}>
                    <span>{fileProgress.fileName}</span>
                    <div className="progress">
                      <div
                        className="progress-bar"
                        role="progressbar"
                        aria-valuenow={fileProgress.percentage}
                        aria-valuemin={0}
                        aria-valuemax={100}
                        style={{ width: fileProgress.percentage + "%" }}
                      >
                        {fileProgress.percentage}%
                      </div>
                    </div>
                  </div>
                )
              )}
          </div>
        </>
      )}

      {message.length > 0 && (
        <div
          style={{ maxHeight: 100 }}
          className="alert alert-secondary overflow-auto p-1"
          role="alert"
        >
          <ul className="m-0 p-0 small">
            {message.map((item, i) => (
              <li key={i} className="list-unstyled">
                {item.split("\n").map((line, idx) => (
                  <div key={idx}>{line}</div>
                ))}
              </li>
            ))}
          </ul>
        </div>
      )}
    </>
  );
}
