import { useCallback, useEffect, useState, useRef } from "react";
import { FullSheetDeficiency, RecordIdType } from "../types/deficiency.types";
import { ViolationCriterion } from "../types/work.types";
import SheetDeficiencyItem from "./sheet-deficiency-item.component";
import {
  DeficiencyModalRow,
  EditDeficienciesModal,
} from "./edit-deficiencies-modal";
import { Loader } from "./loader";

export type DeficienciesBySheetProps = {
  matterId: string;
  loadingSheetDeficiencies: boolean;
  sheetDeficiencies: FullSheetDeficiency[];
  sheetViolations: ViolationCriterion[];
  errorSheetDeficiencies: string;
  addSheetDeficiency: (
    sheetId: number,
    violationId: number,
    violationNote: string | null
  ) => void;
  deleteSheetDeficiency: (sheetId: number, violationId: number) => void;
};

export default function DeficienciesBySheet({
  matterId,
  loadingSheetDeficiencies,
  sheetDeficiencies,
  sheetViolations,
  errorSheetDeficiencies,
  addSheetDeficiency,
  deleteSheetDeficiency,
}: DeficienciesBySheetProps) {

  const perfLoadSheetDeficiencies = useRef<number>(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeSheetId, setActiveSheetId] = useState<number | null>(null);
  const [activeSheetNumber, setActiveSheetNumber] = useState<number | null>(
    null
  );
  const [activeDeficiencies, setActiveDeficiencies] = useState<
    DeficiencyModalRow[]
  >([]);

  // log how long sheet data takes to load and render
  useEffect(() => {
    if (loadingSheetDeficiencies) {
      perfLoadSheetDeficiencies.current = performance.now();
    } else {
      const duration = performance.now() - perfLoadSheetDeficiencies.current;
      console.log(`SheetDeficiencyItems loaded in ${(duration/1000).toFixed(2)}s`);
      perfLoadSheetDeficiencies.current = 0; // reset for next load
    }
  }, [loadingSheetDeficiencies]);

  const handleOpenModal = (sheetId: number, sheetNumber: number) => {
    setActiveSheetId(sheetId);
    setActiveSheetNumber(sheetNumber);
    setIsModalOpen(true);
    updateActiveDeficiencies(sheetId, sheetNumber);
  };

  const updateActiveDeficiencies = useCallback(
    (sheetId: number, sheetNumber: number) => {
      const modalDeficiencies =
        sheetDeficiencies
          .find(
            (sheet) =>
              sheet.signatureSheetId === sheetId &&
              sheet.sheetNumber === sheetNumber
          )
          ?.deficiencyBadges.filter(
              (db) => db.recordIdType === RecordIdType.SignatureSheet && !!db.userName
          )
          .map((def) => ({
            description: def.badgeDescription,
            ruleId: def.ruleId,
          })) || [];
      setActiveDeficiencies(modalDeficiencies);
    },
    [sheetDeficiencies]
  );

  useEffect(() => {
    if (isModalOpen && activeSheetId && activeSheetNumber) {
      updateActiveDeficiencies(activeSheetId, activeSheetNumber);
    }
  }, [
    sheetDeficiencies,
    isModalOpen,
    activeSheetId,
    activeSheetNumber,
    updateActiveDeficiencies,
  ]);

  return (
    <div className="row mt-5">
      <div className="col-sm-12">
        {loadingSheetDeficiencies && (
          <div>
            <Loader />
          </div>
        )}

        {sheetDeficiencies?.length > 0
          ? sheetDeficiencies?.map((sheetDeficiency) => (
              <SheetDeficiencyItem
                key={sheetDeficiency.sheetNumber}
                matterId={matterId}
                sheetDeficiency={sheetDeficiency}
                onOpenModal={handleOpenModal}
              />
            ))
          : !loadingSheetDeficiencies && (
              <div className="row">
                <div className="col">
                  There are no sheet deficiencies to display
                </div>
              </div>
            )}

        {errorSheetDeficiencies && (
          <div className="alert alert-danger" role="alert">
            {errorSheetDeficiencies}
          </div>
        )}
      </div>
      <EditDeficienciesModal
        isOpen={isModalOpen}
        type={RecordIdType.SignatureSheet}
        deficiencies={activeDeficiencies}
        close={() => {
          setActiveSheetId(null);
          setActiveSheetNumber(null);
          setIsModalOpen(false);
        }}
        addDeficiency={(form) => {
          if (activeSheetId) {
            addSheetDeficiency(
              activeSheetId,
              form.violationId as number,
              form.violationNote
            );
          }
        }}
        deleteDeficiency={(violationId) => {
          if (activeSheetId) {
            deleteSheetDeficiency(activeSheetId, violationId);
          }
        }}
        errorMessage={""}
        violationCriteria={sheetViolations}
      />
    </div>
  );
}
