import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { FormikProps } from "formik";
import { ChangeEvent, useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { convertFieldValuesToStrings, convertFieldValuesToTypes, handleError } from "../common/utils";
import { useBoundaryService } from "../services/boundary.service";
import { useDeficiencyReviewService } from "../services/deficiency-review.service";
import { useDeficiencyService } from "../services/deficiency.service";
import { useWorkService } from "../services/work.service";
import { BoundaryPoint } from "../types/boundary.types";
import { Deficiency, DeficiencyReview, RecordIdType, RuleExpressionAndWork } from "../types/deficiency.types";
import { RuleSummary } from "../types/rule.types";
import { RegisteredVoterFlags, ValidationForm, WorkDto, WorkField, WorkStatus } from "../types/work.types";
import CirculatorView from "./circulator-view.component";
import FieldValidation from "./field-validation.component";
import { InlineSpinner } from "./inline-spinner";
import { Loader } from "./loader";
import { OutOfBoundsMap } from "./out-of-bounds-map.component";
import SignatoryView from "./signatory-view.component";
import { WorkForTaskType } from "./work-for-task-type";

export type DeficiencyViewProps = {
  matterId: string;
  deficiencyId: string;
  getNextDeficiencyReview: () => void;
  onNavigate: (sheetNumber: number) => void;
  onFinished: () => void;
  onWorkLoaded?: (work: WorkDto) => void;
};

export function DeficiencyView({
  matterId,
  deficiencyId,
  getNextDeficiencyReview,
  onNavigate,
  onFinished,
  onWorkLoaded,
}: DeficiencyViewProps) {
  const navigate = useNavigate();
  const { updateWork } = useWorkService();
  const { doesBoundaryExist } = useBoundaryService();
  const { getDeficiencyWithRuleById } = useDeficiencyService();
  const { postDeficiencyReview } = useDeficiencyReviewService();
  const [deficiency, setDeficiency] = useState<Deficiency | null>(null);
  const [rule, setRule] = useState<RuleSummary | null>(null);
  const [ruleExpressionAndWorks, setRuleExpressionAndWorks] = useState<RuleExpressionAndWork[]>([]);
  const formikRef = useRef<FormikProps<ValidationForm>>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isDeficient, setIsDeficient] = useState<boolean>(true);
  const [note, setNote] = useState<string>("");
  const [isBoundary, setIsBoundary] = useState<boolean>(false);
  const [geographicPoint, setGeographicPoint] = useState<BoundaryPoint>();

  useEffect(() => {
    if (matterId && deficiencyId) {
      setIsLoading(true);
      getDeficiencyWithRuleById(matterId, deficiencyId).then((data) => {
        setIsLoading(false);
        setRule(data.rule);
        setDeficiency(data.deficiency);
        setNote(data.deficiency.note || "");
        if (data.expressionAndWorks.length === 0) {
          return;
        }

        const modifiedExpressionAndWorks = data.expressionAndWorks.map((ew) => {
          if (ew.work?.fields) {
            ew.work.fields = convertFieldValuesToStrings(ew.work?.fields ?? []);
          }
          return ew;
        });
        setRuleExpressionAndWorks(modifiedExpressionAndWorks);

        const firstItemWithWork = modifiedExpressionAndWorks?.find((e) => e.work);
        if (firstItemWithWork?.work && onWorkLoaded) {
          onWorkLoaded(firstItemWithWork.work);
        }
        const matterVariable = modifiedExpressionAndWorks[1].matterVariable;
        if (matterVariable?.value) {
          doesBoundaryExist(3, matterVariable?.value)
            .then(() => {
              setGeographicPoint(modifiedExpressionAndWorks[0].signatoryPoint);
              setIsBoundary(true);
            })
            .catch(() => {
              setIsBoundary(false);
            });
        }
      });
    }
  }, [matterId, deficiencyId, getDeficiencyWithRuleById, onWorkLoaded, doesBoundaryExist]);

  const handleNext = useCallback(
    async (work: WorkDto, fields: WorkField[]) => {
      if (!work.workId) {
        return Promise.reject("No work");
      }
      return updateWork(work.workId.toString(), {
        workStatusId: WorkStatus.Completed,
        fields: convertFieldValuesToTypes(fields),
        externalDataRecordId: work.externalDataRecordId?.toString() || null,
        registeredVoterFlags: work.registeredVoterFlags || RegisteredVoterFlags.Unknown,
      }).then(
        () => {
          formikRef.current?.setSubmitting(false);
        },
        (error: any) => {
          handleError(error, (msg) => console.error(msg), formikRef.current?.setSubmitting);
        }
      );
    },
    [updateWork]
  );

  async function handleReviewDeficiency() {
    console.log("Submitting deficiency review", { deficiencyId, isDeficient, note });
    await postDeficiencyReview(matterId, {
      deficiencyId: parseInt(deficiencyId, 10),
      isDeficient,
      note,
    } as DeficiencyReview)
      .then((deficiencyReview) => {
        if (deficiencyReview.deficiencyId) {
          navigate(`/admin/matters/${matterId}/deficiencies/review/${deficiencyReview.deficiencyId}`);
        } else {
          onFinished();
        }
      })
      .catch((error) => {
        onFinished();
      });
  }

  function handleCheckboxChange(event: ChangeEvent<HTMLInputElement>): void {
    const isChecked = event.target.checked;
    setIsDeficient(isChecked);
  }

  const renderSide = useCallback(
    (ruleExpressionAndWork: RuleExpressionAndWork, index: number) => {
      const ruleExpression = ruleExpressionAndWork.expression;
      const work = ruleExpressionAndWork.work;
      const circulator = ruleExpressionAndWork.circulator;
      const signatory = ruleExpressionAndWork.signatory;
      const matterVariable = ruleExpressionAndWork.matterVariable;
      const isMatterVariable = ruleExpression?.startsWith("Matter.Variables[");
      const isCell = ruleExpression?.startsWith("Row.Cells[");
      const isField = ruleExpression?.startsWith("Sheet.Fields[");
      const isRegisteredVoter = ruleExpression?.startsWith("Row.RegisteredVoter.");

      if (isMatterVariable) {
        if (!matterVariable) return null;
        return (
          <>
            <p>{`Parameter: ${matterVariable.key}`}</p>
            <label>{matterVariable.key}</label>
            <input type="text" defaultValue={matterVariable.value} />
            {isBoundary && !!geographicPoint && (
              <div>
                <OutOfBoundsMap
                  outerBoundaryName={matterVariable.value}
                  innerBoundaryName={geographicPoint.pointName}
                  point={geographicPoint}
                />
              </div>
            )}
          </>
        );
      } else if ((isCell && work) || (isField && work)) {
        return (
          <>
            {work.sheetNumber && (
              <span>
                {`Sheet ${work.sheetNumber}`}
                {work?.rowNumber ? ` - Row ${work.rowNumber}` : ""}
                {work?.fields && work.fields.length > 0
                  ? ` - ${work.fields.map((f) => f.name).join(" | ")}`
                  : work?.fieldNumber
                  ? ` - Field ${work.fieldNumber}`
                  : ""}
              </span>
            )}
            <FieldValidation
              work={work}
              formikRef={formikRef}
              onNext={(fields) => handleNext(work, fields)}
              isSignatureTable={isCell}
              isRegistrationVerification={false}
              nextLabel="Submit Correction"
            />
          </>
        );
      } else if ((isRegisteredVoter && work) || (deficiency?.recordIdType === RecordIdType.SignatureSheetRow && work)) {
        return (
          <>
            {work.sheetNumber && (
              <span>
                {`Sheet ${work.sheetNumber}`}
                {work?.rowNumber ? ` - Row ${work.rowNumber}` : ""}
              </span>
            )}
            <WorkForTaskType
              work={work}
              formikRef={formikRef}
              onNext={(fields) => handleNext(work, fields)}
              onSave={(fields) => handleNext(work, fields)}
              nextLabel="Submit Correction"
            />
          </>
        );
      } else if (ruleExpression?.includes("FormLines")) {
        // We don't have one for this yet
        return (
          <div>
            <p>FormLines work: {ruleExpression}</p>
          </div>
        );
      } else if (ruleExpression?.includes("Sheet.Circulator") && circulator) {
        return <CirculatorView circulator={circulator} />;
      } else if (ruleExpression?.includes("Row.Signatory") && signatory) {
        return <SignatoryView signatory={signatory} />;
      } else {
        return (
          <div>
            <p>Rule Expression: {ruleExpression}</p>
          </div>
        );
      }
    },
    [deficiency?.recordIdType, geographicPoint, handleNext, isBoundary]
  );

  if (isLoading || !rule || !deficiency) {
    return <Loader />;
  }

  return (
    <div className="m-4">
      <div className="row">
        <div className="col">{rule?.name}</div>
        {(ruleExpressionAndWorks.at(-1)?.expression || ruleExpressionAndWorks.at(-1)?.work) && (
          <div className="col-6">{rule?.operationName}</div>
        )}
      </div>
      <div className="row">
        {ruleExpressionAndWorks
          .filter((expressionAndWork) => expressionAndWork.expression || expressionAndWork.work)
          .map((expressionAndWork, index) => (
            <div key={index} className="col">
              {renderSide(expressionAndWork, index)}
            </div>
          ))}
      </div>
      <hr />
      <textarea
        placeholder="Add deficiency note here"
        className="form-control"
        rows={3}
        value={note}
        onChange={(e) => setNote(e.target.value)}
      />
      <hr />
      <div className="row">
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            alignItems: "center",
          }}
        >
          <div>
            <input id="isDeficient" type="checkbox" checked={isDeficient} onChange={handleCheckboxChange}></input>
            <label className="ms-2" htmlFor="isDeficient">
              Is Deficient
            </label>
          </div>
          <button className="btn btn-sw btn-outline" onClick={handleReviewDeficiency}>
            {!formikRef.current?.isSubmitting && <FontAwesomeIcon icon={faArrowRight} className="me-2" />}
            {formikRef.current?.isSubmitting && <InlineSpinner />}
            Review Complete
          </button>
        </div>
      </div>
    </div>
  );
}
