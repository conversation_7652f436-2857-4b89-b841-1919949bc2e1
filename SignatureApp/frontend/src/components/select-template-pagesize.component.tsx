import { SupportedPageSize } from "../types/template.types";

interface SelectTemplatePageSizeProps {
  onPageSizeChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  selectedPageSize: SupportedPageSize | "";
}

export default function SelectTemplatePageSize(
  props: SelectTemplatePageSizeProps
) {
  return (
    <select
      data-testid="select-page-size"
      className="form-select w-50 mb-3"
      onChange={props.onPageSizeChange}
      value={props.selectedPageSize}
    >
      <option key="0" value="">
        Select Page Size
      </option>
      <option key={SupportedPageSize.Legal} value={SupportedPageSize.Legal}>
        {SupportedPageSize.Legal}
      </option>
      <option key={SupportedPageSize.Letter} value={SupportedPageSize.Letter}>
        {SupportedPageSize.Letter}
      </option>
    </select>
  );
}
