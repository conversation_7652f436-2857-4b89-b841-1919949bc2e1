import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { WorkDto } from "../types/work.types";
import { useWorkService } from "../services/work.service";
import { PreviewWorkItems } from "./preview-work-items";

interface PreviewRowWorkItemsProps {
  isRelease: boolean;
  rowNumber?: number;
  workReleasable?: boolean[];
  setWorkReleasable?: (updater: (prev: boolean[]) => boolean[]) => void;
}

export function PreviewRowWorkItems({
  isRelease,
  rowNumber,
  workReleasable,
  setWorkReleasable,
}: PreviewRowWorkItemsProps) {
  const params = useParams();
  const { matterId, sheetNumber } = params;
  if (!rowNumber) {
    rowNumber = params.rowNumber ? parseInt(params.rowNumber, 10) : 1;
  }
  const { getPreviewRowWorkItems } = useWorkService();
  const [workItems, setWorkItems] = useState<WorkDto[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!matterId || !sheetNumber || !rowNumber) return;
    setIsLoading(true);
    getPreviewRowWorkItems(matterId, sheetNumber, rowNumber.toString()).then(
      (workItems) => {
        setIsLoading(false);
        setWorkItems(workItems);
      }
    );
  }, [matterId, sheetNumber, rowNumber, getPreviewRowWorkItems]);

  return (
    <PreviewWorkItems
      isRelease={isRelease}
      isLoading={isLoading}
      workItems={workItems}
      workReleasable={workReleasable}
      setWorkReleasable={setWorkReleasable}
    />
  );
}
