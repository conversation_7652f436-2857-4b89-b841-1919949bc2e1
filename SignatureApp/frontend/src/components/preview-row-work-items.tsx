import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { WorkDto } from "../types/work.types";
import { useWorkService } from "../services/work.service";
import { PreviewWorkItems } from "./preview-work-items";
import { Loader } from "./loader";
import HeaderNavBar from "./header-nav-bar";

interface PreviewRowWorkItemsProps {
  isRelease: boolean;
  workReleasable?: boolean[];
  setWorkReleasable?: (updater: (prev: boolean[]) => boolean[]) => void;
}

export function PreviewRowWorkItems({ isRelease, workReleasable, setWorkReleasable }: PreviewRowWorkItemsProps) {
  const params = useParams();
  const { matterId, sheetNumber } = params;
  const { getPreviewRowWorkItems } = useWorkService();
  const [workItems, setWorkItems] = useState<WorkDto[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!matterId || !sheetNumber) return;
    setIsLoading(true);
    getPreviewRowWorkItems(matterId, sheetNumber).then((workItems) => {
      setIsLoading(false);
      setWorkItems(workItems);
    });
  }, [matterId, sheetNumber, getPreviewRowWorkItems]);

  if (isLoading) return <Loader />;

  return (
    <div className="container">
      <HeaderNavBar backTo="sheet" />

      <PreviewWorkItems
        isRelease={isRelease}
        isLoading={isLoading}
        workItems={workItems}
        workReleasable={isRelease ? workReleasable : undefined}
        setWorkReleasable={isRelease ? setWorkReleasable : undefined}
      />
    </div>
  );
}
