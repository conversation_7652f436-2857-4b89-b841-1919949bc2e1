import {ErrorMessage, Field} from "formik";
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";
import {faTrashCan} from "@fortawesome/free-regular-svg-icons";
import {MatterVariable} from "../types/matter.types";

export type MatterParameterProps = {
  index: number;
  variable: MatterVariable,
  showDelete: boolean;
  onValidate?: (variable: MatterVariable) => void;
  onDelete?: (variable: MatterVariable) => void;
}
export function MatterParameter({index, variable, onValidate, showDelete, onDelete}: MatterParameterProps) {
  return (
    <div className="row mb-1" key={index}>
      <div className="col-sm-5">
        <Field
          name={`matterParameters.${index}.key`}
          className="form-control form-control-sm d-inline"
          placeholder="Enter parameter name"
          type="text"
          validate={onValidate}
        />
        <ErrorMessage
          name={`matterParameters.${index}.key`}
          component="div"
          className="alert alert-danger w-auto p-2"
        />
      </div>

      <div className="col-sm-6">
        <Field
          name={`matterParameters.${index}.value`}
          className="form-control form-control-sm d-inline"
          placeholder="Enter parameter value"
          type="text"
        />
        <ErrorMessage
          name={`matterParameters.${index}.value`}
          component="div"
          className="alert alert-danger w-auto p-2"
        />
      </div>
      {showDelete && (
      <div className="col-sm-1">
        <a
          role="button"
          onClick={() => onDelete?.(variable)}
        >
          <FontAwesomeIcon
            icon={faTrashCan}
            className="pt-1"
          />
        </a>
      </div>
      )}
    </div>
  );
}
