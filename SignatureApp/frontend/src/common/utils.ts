import { WorkField } from "../types/work.types";
import { Roles } from "../types/user.types";
import { Column } from "../types/signature-sheet.types";

const handleError = (
  error: any,
  setErrorMessage?: (str: string) => void,
  setLoading?: (isLoading: boolean) => void
) => {
  const resMessage =
    (error.response && error.response.data && error.response.data.message) ||
    error.response.data ||
    error.message ||
    error.toString();

  setErrorMessage?.(resMessage);
  setLoading?.(false);
};

const convertFieldValuesToTypes = (fields: WorkField[]) => {
  return fields.map((field) => {
    return {
      ...field,
      violationId: field.violationId !== "" ? field.violationId : null,
      violationNote: field.violationNote !== "" ? field.violationNote : null,
    };
  });
};

const convertFieldValuesToStrings = (fields: WorkField[]) => {
  return fields.map((field) => {
    return {
      ...field,
      violationId: field.violationId || "",
      violationNote: field.violationNote || "",
    };
  });
};

const formatObjectAsDate = (d: string | Date | null | undefined) => {
  if (!d) {
    return "";
  }
  if (d instanceof Date) {
    return formatDate(d);
  }
  return formatDateString(d);
};

const formatDate = (d: Date | null | undefined) => {
  if (!d) {
    return "";
  }
  let month = "" + (d.getMonth() + 1);
  let day = "" + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) {
    month = "0" + month;
  }
  if (day.length < 2) {
    day = "0" + day;
  }

  return [year, month, day].join("-");
};

const formatDateString = (date: string | null | undefined) => {
  if (!date) {
    return "";
  }
  const d = new Date(date);
  return formatDate(d);
};

function checkForRole(requiredRole: Roles, actualRole?: Roles): boolean {
  if (actualRole === undefined || actualRole === Roles.None) return false;
  return actualRole <= requiredRole;
}

function fixColumnLeftAndRight(columns: Column[]) {
  for (var index = 1; index < columns.length; index++) {
    const column1 = columns[index - 1];
    const column2 = columns[index];
    if (column1.right !== column2.left) {
      const average = (column1.right + column2.left) / 2;
      column2.left = column1.right = average;
    }
  }
}

export {
  handleError,
  convertFieldValuesToTypes,
  convertFieldValuesToStrings,
  formatDate,
  formatDateString,
  formatObjectAsDate,
  checkForRole,
  fixColumnLeftAndRight,
};
