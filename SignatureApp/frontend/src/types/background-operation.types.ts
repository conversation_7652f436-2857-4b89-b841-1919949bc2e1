export enum ExecutionStatus {
  Unknown,
  Running,
  Succeded,
  Failed
}
export interface BackgroundOperation {
  id: number;
  executionStatus: ExecutionStatus
  modifiedOn: Date | null;
  modifiedBy: string;
  message: string;
}

export interface GetLastRunDTO {
  executionStatus: ExecutionStatus
  startedBy: string | null;
  startedOn: Date | null;
  secondsSinceStarted: number | null;
  endedOn: Date | null;
  secondsSinceCompletion: number | null;
}
