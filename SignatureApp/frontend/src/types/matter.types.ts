export enum MatterType {
  Unknown,
  Candidate,
  Initiative,
}

export interface Matter {
  id: number;
  type: MatterType;
  name: string;
  numberSignaturesRequired: number;
  isGoalDeficencies: boolean;
  isSheetReviewAdded: boolean;
  areTasksCreated: boolean;
  dueDate: string | null;
}

export enum ChecklistStep {
  UploadTemplates,
  SetupTemplates,
  SetupParameters,
  UploadCirculators,
  UploadVoters,
  CreateTasks,
  UploadSignatures,
  CreateRules,
  FinishTranscription,
}

export interface MatterVariable {
  matterVariableId: number | null;
  key: string;
  value: string;
}

export interface MatterForm {
  name: string;
  type: MatterType;
  numberSignaturesRequired: number | null;
  isGoalDeficencies: boolean;
  dueDate: string | null;
}

export interface DocumentUploadInfo {
  sheetCount: number;
  lastUploadId: string;
  lastUploadedBy: string;
  lastUploadedOn: string;
  uploadCount: number;
}

export interface DocumentUploadInfoByCounty {
  county: string;
  count: number;
  lastUploadId: string;
  lastUploadedBy: string;
  lastUploadedOn: string;
}

export interface Progress {
  reviewed: number;
  deficient: number;
  remaining: number;
  threshold: number;
}

export interface ChecklistItem {
  step: ChecklistStep;
  status: boolean;
}
