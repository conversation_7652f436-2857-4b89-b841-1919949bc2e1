export interface FileProgress {
  fileName: string;
  percentage: number;
}

export interface MultiFileProgress {
  fileProgressArray: FileProgress[];
}

export interface FileContentResult {
  contentType: string;
  enableRangeProcessing: boolean;
  entityTag: any;
  fileContents: string;
  fileDownloadName: string;
  lastModified?: string;
}

export interface UploadProgress {
  setUploadProgress(percentage: number): void;
}
