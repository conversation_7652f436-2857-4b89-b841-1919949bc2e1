import { FileContentResult } from "./file.types";
import { DataTransformationStatusDTO } from "./data-transformation-step";

export interface SignatureTable {
  signatureSheetId: number;
  sheetImage: FileContentResult | null;
  imageAspectRatio: number;
  pageNumber: number;
  templateId: string;
  rows: Row[];
  columns: Column[];
  left: number;
  top: number;
  right: number;
  bottom: number;
}

export interface SignatureSheetTable extends SignatureTable {
  signatureSheet: SignatureSheet;
}

export interface TemplateSignatureTable extends SignatureTable {
  template: Template;
}

export interface Template {
  id: number;
  pages: Page[];
}

export interface SignatureSheet {
  id: number;
  sheetNumber: number;
  pages: Page[];
}

export interface InvalidSheet {
  id: number;
  matterId: number;
  templateId: number;
  sheetNumber: number;
  filename: string;
}

export interface Page {
  pageNumber: number;
  height: number;
  width: number;
}

export interface Row {
  id: number;
  left: number;
  top: number;
  right: number;
  bottom: number;
  isReviewed: boolean;
  rowIndex: number;
  rowNumber: number;
  isMissing: boolean;
}

export interface Column {
  id: number;
  columnIndex: number;
  left: number;
  top: number;
  right: number;
  bottom: number;
  name?: string;
  isMissing: boolean;
}

export interface UploadHistoryDTO {
  signatureSheetUploadId: number;
  uploadUrl: string;
  uploadedBy: string;
  uploadedOn: string;
  templateName: string;
  status: DataTransformationStatusDTO;
}

export interface MinMaxSheets {
  minimumSheetNumber: number;
  maximumSheetNumber: number;
}

export interface WhichPartsAreValidDTO {
  columns: boolean[];
  rows: boolean[];
  fields: boolean[];
}

export enum Tab {
  VALID = "Valid Sheets",
  INVALID = "Invalid Sheets",
  RULE = "By Rule",
  SHEET = "By Sheet",
}
