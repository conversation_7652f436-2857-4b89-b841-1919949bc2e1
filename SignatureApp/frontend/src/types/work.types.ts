import { FileContentResult } from "./file.types";
import { InputType } from "./template.types";

export interface Work {
  id: number;
  matterId: number;
  sheetNumber: number;
  rowNumber: number;
  fieldNumber: number;
}

export interface WorkDto {
  workId: number | null;
  taskTypeId: number;
  externalDataRecordId: number | null;
  registeredVoterFlags: RegisteredVoterFlags;
  fields: WorkField[];
  image: FileContentResult;
  taskName: string;
  taskDescription: string;
  showWholeColumn: boolean;
  canBeInvalid: boolean;
  violationCriteria: ViolationCriterion[];
  matterId: number;
  matterName: string;
  sheetNumber: number;
  rowNumber: number;
  fieldNumber: number;
}

export enum Validity {
  Unknown,
  Valid,
  Invalid,
  Strikethrough,
}

export interface WorkField {
  id: number;
  name: string;
  value: string;
  inputType: InputType;
  canBeInvalid: boolean;
  validity: Validity;
  violationCriteria: ViolationCriterion[];
  violationId: number | string | null;
  violationNote: string | null;
}

export interface ViolationCriterion {
  ruleId: number;
  name: string;
}

export enum RegisteredVoterFlags {
  Unknown,
  Valid,
  Mismatched<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  NotRegistered,
}

export interface ValidationForm {
  fields: WorkField[];
  externalDataRecordId?: string;
  registeredVoterFlags: RegisteredVoterFlags;
}

export interface MatterWorkStatusDTO {
  matterId: number;
  matterName: string;
  matterDueDate: string;
  workStatus: WorkStatus;
  count: number;
}

export interface TaskWorkStatus {
  taskId: number;
  taskName: string;
  workStatus: WorkStatus;
  count: number;
}

export interface WorkSummaryByMatter {
  matterId: number;
  matterName: string;
  matterDueDate: string;
  completed: number;
  total: number;
}

export interface FinishWorkDTO {
  fields: WorkField[];
  externalDataRecordId: string | null;
  registeredVoterFlags: RegisteredVoterFlags;
  workStatusId: WorkStatus;
}

export enum WorkStatus {
  None = 0,
  Flagged = 1,
  Break = 2,
  Completed = 3,
  Assigned = 4,
}

export enum TaskType {
  None = 0,
  TranscribableField = 1,
  SignatureTableColumn = 2,
  ExternalDataSourceVerification = 4,
  SheetReview = 8,
}
