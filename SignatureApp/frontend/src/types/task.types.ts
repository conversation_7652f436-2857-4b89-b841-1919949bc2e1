export interface Task {
    id: number;
    transcribableFieldId: number;
    taskTypeId: number;
    firstSignatureColumnId: number;
    lastSignatureColumnId: number;
    firstColumnIndex: number;
    lastColumnIndex: number;
    showFirstColumnIndex: number;
    showLastColumnIndex: number;
    showSurroundingRows: boolean;
    showWholeColumn: boolean;
    name: string;
    description: string;
    priority: number;
  }