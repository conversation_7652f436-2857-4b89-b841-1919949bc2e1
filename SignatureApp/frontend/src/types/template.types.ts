import { FileContentResult } from "./file.types";

export interface Template {
  id: number;
  name: string;
  fileName: string;
  uploadedBy: string;
  uploadedOn: string;
  createdBy: string;
  createdOn: string;
  modifiedBy: string;
  modifiedOn: string;
  fieldsModifiedBy: string;
  fieldsModifiedOn: string;
  columnsModifiedBy: string;
  columnsModifiedOn: string;
  pageSize: SupportedPageSize;
  usStateId: number;
}

export enum InputType {
  Text,
  CheckBox,
  TextArea,
}

export interface GetTranscribableFieldDTO {
  id: string;
  image: FileContentResult;
  name: string;
  isSkipped: boolean;
  isHandwritten: boolean;
  canBeInvalid: boolean;
  isSignature: boolean;
  isDate: boolean;
  isPrefixed: boolean;
  prefix: string;
  inputType: InputType;
  groupName: string;
  pageNumber: number;
}

export interface UpdateTranscribableFieldDTO {
  name: string;
  isSkipped: boolean;
  isHandwritten: boolean;
  canBeInvalid: boolean;
  isSignature: boolean;
  isDate: boolean;
  isPrefixed: boolean;
  prefix: string;
  inputType: InputType;
  groupName: string;
}

export interface GetNextSignatureTableColumnDTO {
  id: string;
  image: FileContentResult;
  name: string;
  isSkipped: boolean;
  isHandwritten: boolean;
  canBeInvalid: boolean;
  isSignature: boolean;
  isName: boolean;
  isAddress: boolean;
  isDate: boolean;
  isVoterId: boolean;
}

export interface UpdateSignatureTableColumnDTO {
  name: string;
  isSkipped: boolean;
  isHandwritten: boolean;
  canBeInvalid: boolean;
  isSignature: boolean;
  isName: boolean;
  isAddress: boolean;
  isDate: boolean;
  isVoterId: boolean;
}

export interface TemplateIgnoredWordsDTO {
  id: number;
  word: string;
  templateId: number;
}

export enum SupportedPageSize {
  Unknown = "Unknown",
  Letter = "Letter",
  Legal = "Legal",
}
