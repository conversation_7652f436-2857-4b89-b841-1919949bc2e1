export interface ExternalDataSourcePart {
  id: number;
  fileName: string;
  originalUploadedFileName: string;
  uploadedBy: string;
  uploadedOn: string;
  partNumber: number;
}

export interface ExternalDataSource {
  id: number;
  fileName: string;
  uploadedBy: string;
  uploadedOn: string;
  county: string;
  totalParts: number;
  uploadType: UploadType;
  externalDataSourceParts: ExternalDataSourcePart[];
}

export enum UploadType {
  Unknown = "Unknown",
  Circulator = "Circulator",
  Voter = "Voter",
}

export interface SearchResults {
  results: ExternalData[];
  totalHits: number;
}

export interface ExternalData {
  id: number;
  fields: ExternalDataField[];
}

export interface ExternalDataField {
  name: string;
  value: string;
}
export interface VoterSearchDTO {
  firstName: string;
  lastName: string;
  streetAddress: string;
  city: string;
  state: string;
  zipCode: string;
  voterId: number;
  party: string;
  congressionalDistrict: string;
  legislativeDistrict: string;
}

export interface GetVoterSearchDTO {
  results: VoterSearchDTO[];
  totalHits: number;
}
