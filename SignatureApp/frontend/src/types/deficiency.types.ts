import { BoundaryPoint } from "./boundary.types";
import { Circulator, Signatory } from "./external-data.types";
import { MatterVariable } from "./matter.types";
import { RuleSummary } from "./rule.types";
import { WorkDto } from "./work.types";

export enum RecordIdType {
  None = 0,
  SignatureSheetField = 1,
  SignatureSheetCell = 2,
  SignatureSheetRow = 3,
  SignatureSheet = 4,
  Matter = 5,
}

export interface DeficiencyWithRule {
  deficiency: Deficiency;
  rule: RuleSummary;
  operationName: string;
  expressionAndWorks: RuleExpressionAndWork[];
}

export interface RuleExpressionAndWork {
  expression: string;
  work: WorkDto;
  matterVariable: MatterVariable;
  signatoryPoint: BoundaryPoint;
  circulator: Circulator | null;
  signatory: Signatory | null;
}

export interface Deficiency {
  id: number;
  recordId: number;
  recordIdType: RecordIdType;
  otherRecordId: number;
  otherRecordIdType: RecordIdType;
  ruleId: number;
  userName: string | null;
  ruleName: string;
  imageUrl: string;
  signatureCellId: number | null;
  signatureRowId: number | null;
  signatureSheetFieldId: number | null;
  sheetNumber: number | null;
  rowNumber: number | null;
  workId: number | null;
  note: string | null;
}

export interface DeficiencyReview {
  id: number;
  deficiencyId: number;
  isDeficient: boolean;
  note: string | null;
}

export interface FullSheetDeficiency {
  signatureSheetId: number;
  sheetNumber: number;
  imageUrl: string;
  deficiencyBadges: DeficiencyBadge[];
}

export interface DeficiencyBadge {
  deficiencyId: number | null;
  recordIdType: number;
  ruleId: number;
  workId: number | null;
  userName: string | null;
  badgeNumber: string;
  badgeDescription: string;
}
