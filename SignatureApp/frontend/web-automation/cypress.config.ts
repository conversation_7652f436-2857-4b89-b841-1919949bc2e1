import { defineConfig } from "cypress";
import {
  BlobServiceClient,
  ContainerSASPermissions,
  generateBlobSASQueryParameters,
  StorageSharedKeyCredential,
} from "@azure/storage-blob";
import "msnodesqlv8";
import { SqlClient } from "msnodesqlv8/types";
import { dbConnectionString } from "./cypress/cypress.env.json";

async function createBlobContainerWithFilesInternal(containerName: string, signatureSheetFiles: string[]) {
  const storageName = "devstoreaccount1";
  const storageKey = `Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==`;
  var sharedKeyCredential = new StorageSharedKeyCredential(
    storageName,
    storageKey
  );
  const blobServiceClient = new BlobServiceClient(
    `http://127.0.0.1:10000/devstoreaccount1`,
    sharedKeyCredential
  );
  const containerClient = blobServiceClient.getContainerClient(
    "cypresscontainer"
  );

  await containerClient.deleteIfExists(); //Clean out blobs if switching between Forms

  await containerClient.createIfNotExists();

  for (const file of signatureSheetFiles) {
    const blob = containerClient.getBlockBlobClient(file);
    await blob.uploadFile(`./cypress/fixtures/SignatureSheets/${file}`);
  }

  const sasOptions = {
    containerName: "cypresscontainer",
    permissions: ContainerSASPermissions.parse("lr"),
    startsOn: new Date(),
    expiresOn: new Date(new Date().valueOf() + 3600 * 1000),
  };

  const sasToken = generateBlobSASQueryParameters(
    sasOptions,
    sharedKeyCredential
  ).toString();

  const url = `${containerClient.url}?${sasToken}`;
  return url;
}

async function runSqlQuery(sql_query: string) {
  const connectionString = dbConnectionString;
  const sql: SqlClient = require("msnodesqlv8");
  return new Promise((resolve, reject) => {
    sql.query(connectionString, sql_query, (err, rows) => {
      if (err) {
        console.log(err);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

export default defineConfig({
  watchForFileChanges: false,
  e2e: {
    baseUrl: "http://localhost:3000",
    setupNodeEvents(on, config) {
      const template = config.env.template;
      on("task", {
        createBlobContainerWithFiles(params: { containerName: string; signatureSheetFiles: string[] }) {
          return createBlobContainerWithFilesInternal(params.containerName, params.signatureSheetFiles);
        },
      });
      on("task", {
        runSqlQuery(sql_query) {
          return runSqlQuery(sql_query);
        },
      });
      on("task", {
        nodelog(message) {
          console.log(message);
          return null;
        },
      });
      console.log(template);
      return config;
    },
  },
});

