/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />
declare namespace Cypress {
  interface Chainable {
    signIn(email: string, password: string): Chainable<Element>;
    reviewValid(): Chainable<Element>;
    task(event: "createBlobContainerWithFiles", containerName: string, signatureSheetFiles: string[]): Chainable<string>;
    task(event: "runSqlQuery", sql_query: string): Chainable<any>;
  }
}
