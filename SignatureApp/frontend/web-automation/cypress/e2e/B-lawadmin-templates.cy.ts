import 'cypress-network-idle';
import {
  createTemplate,
  identifyTemplateColumns,
  identifyTemplateFields,
  uploadTemplateFile
} from "../support/lawadmin-template";
import {deleteTemplateIfNecessary} from "../support/lawadmin-cleanup";
import {
  columnsFor2019Candidate, columnsFor2019Initiative, columnsFor2020EQualCandidate,
  columnsFor2022Initiative,
  fieldsFor2019Candidate, fieldsFor2019Initiative, fieldsFor2020EQualCandidate,
  fieldsFor2022Initiative
} from "../support/templates";

describe("Law Admin - Templates", () => {
  beforeEach(() => {
    cy.signIn("<EMAIL>", "admin");
    cy.visit("/admin");
    cy.waitForNetworkIdle(500);
  });

  describe("2019 Candidate Petition", () => {
    const templateName = "Cypress 2019 Candidate Template"
    before(() => {
      cy.signIn("<EMAIL>", "admin");
      deleteTemplateIfNecessary(templateName)
    });

    it("should be able to create and upload a new template", () => {
      createTemplate(templateName);
      uploadTemplateFile("./cypress/fixtures/Templates/2019 0731 - AZ Candidate Petition Form.pdf", "Letter");
    });

    it("should be able to identify template fields", () => {
      identifyTemplateFields(templateName, fieldsFor2019Candidate);
    });

    it("should be able to identify template columns", () => {
      identifyTemplateColumns(templateName, columnsFor2019Candidate);
    });
  });

  describe("2022 Initiative Petition", () => {
    const templateName = "Cypress 2022 Initiative Template"
    before(() => {
      cy.signIn("<EMAIL>", "admin");
      deleteTemplateIfNecessary(templateName)
    });

    it("should be able to create and upload a new template", () => {
      createTemplate(templateName);
      uploadTemplateFile("./cypress/fixtures/Templates/2022 Initiative Petition - fillable all fields.pdf", "Legal");
    });

    it("should be able to identify template fields", () => {
      identifyTemplateFields(templateName, fieldsFor2022Initiative);
    });

    it("should be able to identify template columns", () => {
      identifyTemplateColumns(templateName, columnsFor2022Initiative);
    });
  });

  describe("2019 Initiative Petition", () => {
    const templateName = "Cypress 2019 Initiative Template"
    before(() => {
      cy.signIn("<EMAIL>", "admin");
      deleteTemplateIfNecessary(templateName)
    });

    it("should be able to create and upload a new template", () => {
      createTemplate(templateName);
      uploadTemplateFile("./cypress/fixtures/Templates/2019 0325 - Statewide Initiative Petition w Additional Fields Final.pdf", "Legal");
    });

    it("should be able to identify template fields", () => {
      identifyTemplateFields(templateName, fieldsFor2019Initiative);
    });

    it("should be able to identify template columns", () => {
      identifyTemplateColumns(templateName, columnsFor2019Initiative);
    });
  });

  describe("2019 Candidate E-Qual Petition", () => {
    const templateName = "Cypress 2019 Candidate E-Qual Template"
    before(() => {
      cy.signIn("<EMAIL>", "admin");
      deleteTemplateIfNecessary(templateName)
    });

    it("should be able to create and upload a new template", () => {
      createTemplate(templateName);
      uploadTemplateFile("./cypress/fixtures/Templates/2020 EQual Template Blank.pdf", "Letter");
    });

    it("should be able to identify template fields", () => {
      identifyTemplateFields(templateName, fieldsFor2020EQualCandidate);
    });

    it("should be able to identify template columns", () => {
      identifyTemplateColumns(templateName, columnsFor2020EQualCandidate);
    });
  });

});
