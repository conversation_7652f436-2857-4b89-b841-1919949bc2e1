import 'cypress-network-idle';
import {
  deleteCypressMatters,
  deleteCypressTemplates,
  deleteMatterFromUI,
  deleteTemplateFromUI,
  deleteVoterRegistration,
} from "../support/lawadmin-cleanup";

describe("Law Admin - Cleanup", () => {
  beforeEach(() => {
    cy.signIn("<EMAIL>", "admin");
    cy.visit("/admin");
    cy.waitForNetworkIdle(500);
  });

  describe("Clean Up", () => {
    it("should be able to delete a matter from UI", () => {
      deleteMatterFromUI("Cypress 2019 Initiative Matter");
    });

    it("should be able to delete the 2022 Initiative template from UI", () => {
      deleteTemplateFromUI("Cypress 2022 Initiative Template");
    });

    it("should be able to delete the 2019 Initiative template from UI", () => {
      deleteTemplateFromUI("Cypress 2019 Initiative Template");
    });

    it("should be able to delete the candidate template from UI", () => {
      deleteTemplateFromUI("Cypress 2019 Candidate Template");
    });

    it("should be able to delete a voter file from UI", () => {
      deleteVoterRegistration("Pima.csv");
    });

    it("should be able to delete all Cypress Matters", () => {
      deleteCypressMatters();
    });

    it("should be able to delete all Cypress Templates", () => {
      deleteCypressTemplates();
    });
  });
});
