import 'cypress-network-idle';
describe("Law Admin - Review Defects", () => {
  let sasUrl: string;
  before(() => {
    cy.task("seedAzure").then((url) => {
      sasUrl = url as string;
      cy.log(sasUrl);
    });
  });

  beforeEach(() => {
    cy.signIn("<EMAIL>", "admin");
    cy.visit("/admin");
    cy.waitForNetworkIdle(500);
  });

  // Depending on the test that I reviewed
  // Should verify that the corresponding defects are shown in the by rule view and the by sheet view, and the exported CSV
});
