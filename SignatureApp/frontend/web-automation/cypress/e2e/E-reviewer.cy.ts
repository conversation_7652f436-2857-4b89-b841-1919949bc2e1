import 'cypress-network-idle';
import { RegisteredVoterFlags, Validity } from "../../../src/types/work.types";
import {
  reviewValidity,
  finishTasks,
  reviewColumn,
  reviewMultipleColumns,
  reviewVoterRegistration,
} from "../support/reviewer";

describe("Reviewer", () => {

  context.skip("Signature Review", () => {
    beforeEach(() => {
      cy.intercept("GET", "/api/work/next").as("getWork");
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
    });

    it("should be able to validate a signature", () => {
      cy.findByText(/Begin Task/i).should("be.visible");
      cy.findByRole("button", { name: /Continue/i }).click();
      cy.wait("@getWork");
      cy.reviewValid();
      cy.findByRole("button", { name: /Next/i }).should("be.enabled").click();
      cy.wait("@getWork");
    });

    it("should be able to reject a signature", () => {
      cy.findByText(/Begin Task/i).should("be.visible");
      cy.findByRole("button", { name: /Continue/i }).click();
      cy.findAllByRole("radio").should("not.be.checked");
      cy.findByRole("radio", { name: /Invalid/i })
        .should("be.visible")
        .check();
      cy.findByRole("combobox")
        .should("be.visible")
        .select("Invalid Signature");
      cy.findByRole("textbox").type("I cannot read this signature");
      cy.findByRole("button", { name: /Next/i }).should("be.enabled").click();
      cy.wait("@getWork");
    });

    it("should be able to strike a signature", () => {
      function strikeSignature() {
        cy.get('*[class="container"]')
          .should("be.visible")
          .then(() => {
            cy.findByRole("heading")
              .should("be.visible")
              .then((heading) => {
                if (heading.text().includes("DONE")) {
                  //This will exit the loop once we reach last screen
                  cy.log("No more fields, we are done");
                  return;
                } else {
                  cy.findAllByRole("radio")
                    .should("not.be.checked")
                    .eq(2)
                    .check();
                  cy.findByRole("button", { name: /Next/i })
                    .should("be.enabled")
                    .click();
                  return strikeSignature();
                }
              });
          });
      }

      cy.findByText(/Begin Task/i).should("be.visible");
      cy.findByRole("button", { name: /Continue/i }).click();
      strikeSignature();
    });
  });

  context.skip("Name Review", () => {
    beforeEach(() => {
      cy.intercept("GET", "/api/work/next").as("getWork");
      cy.signIn("<EMAIL>", "staff"); //Second group for 2019 initiative
      cy.visit("/work");
    });

    it("should be able to validate a name with three fields", () => {
      cy.findByText(/Begin Task/i).should("be.visible");
      cy.findByRole("button", { name: /Continue/i }).click();
      cy.findAllByTestId("CypressOptions").each((element) => {
        //TestId added in field-validation.component.tsx, column-validation.component.tsx
        reviewValidity(element);
      });
      cy.findByRole("button", { name: /Next/i }).should("be.enabled").click();
      cy.wait("@getWork");
    });

    it("should be able to validate rest of names", () => {
      cy.findByText(/Begin Task/i).should("be.visible");
      cy.findByRole("button", { name: /Continue/i }).click();
      finishTasks();
    });
  });

  context.only("Review all for 2022 Initiative", () => {
    it("should be able to review signature columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("Signature Column", [
        // Sheet 250 (duplicate of 251)
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Strikethrough },
        { validity: Validity.Valid },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        // Sheet 251
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Strikethrough },
        { validity: Validity.Valid },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review name columns for 2022 Initiative", () => {
      //First and Last Name
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Name Column", [
        // Sheet 250
        [
          { validity: Validity.Valid, text: "Scottie" },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        // Sheet 251
        [
          { validity: Validity.Valid, text: "Scottie J" },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review address columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Address", [
        // Sheet 250
        [
          { validity: Validity.Valid, text: "4042 E NISBET" }, // interesting case because there is a Nisbet Rd and Ct
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid, text: "2718 E 10th St" }, // this is an address line, zip, and city out of boundary
          { validity: Validity.Valid, text: "85719" },
          { validity: Validity.Valid, text: "Tucson" },
        ],
        [
          { validity: Validity.Valid, text: "14327 E Becker Ln" }, // 14327 W Becker Ln exists E Becker exists but not that street number
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid, text: "4911 E Doubletree Ranch Rd" }, // Vacant residential lot
          { validity: Validity.Valid, text: "85253" },
          { validity: Validity.Valid, text: "Paradise Valley" },
        ],
        [
          { validity: Validity.Valid, text: "4410 E Sunset Dr" }, //No house between 4400 and 4420 E. Sunset Dr.
          { validity: Validity.Valid, text: "85028" },
          { validity: Validity.Valid, text: "Phoenix" },
        ],
        [
          { validity: Validity.Valid, text: "10645 N Tatum Blvd #200" }, // Non-residential address - UPS Store.
          { validity: Validity.Valid, text: "85208" },
          { validity: Validity.Valid, text: "Phoenix" },
        ],
        // Sheet 251
        [
          { validity: Validity.Valid, text: "4042 E NISBET" }, // interesting case because there is a Nisbet Rd and Ct
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid, text: "2718 E 10th St" }, // this is an address line, zip, and city out of boundary
          { validity: Validity.Valid, text: "85719" },
          { validity: Validity.Valid, text: "Tucson" },
        ],
        [
          { validity: Validity.Valid, text: "14327 E Becker Ln" }, // 14327 W Becker Ln exists E Becker exists but not that street number
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [
          { validity: Validity.Valid, text: "4911 E Doubletree Ranch Rd" }, // Vacant residential lot
          { validity: Validity.Valid, text: "85253" },
          { validity: Validity.Valid, text: "Paradise Valley" },
        ],
        [
          { validity: Validity.Valid, text: "4410 E Sunset Dr" }, //No house between 4400 and 4420 E. Sunset Dr.
          { validity: Validity.Valid, text: "85028" },
          { validity: Validity.Valid, text: "Phoenix" },
        ],
        [
          { validity: Validity.Valid, text: "10645 N Tatum Blvd #200" }, // Non-residential address - UPS Store.
          { validity: Validity.Valid, text: "85208" },
          { validity: Validity.Valid, text: "Phoenix" },
        ],
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review date signed columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("Date Signed", [
        // Sheet 250
        { validity: Validity.Valid, text: "4/15/24" },
        { validity: Validity.Valid },
        { validity: Validity.Valid, text: "4/15/24" },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        // Sheet 251
        { validity: Validity.Valid, text: "4/15/24" },
        { validity: Validity.Valid },
        { validity: Validity.Valid, text: "4/15/24" },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it.skip("should be able to review all fields for 2022 Initative", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Fields", [
        [{ validity: Validity.Valid, text: "Maricopa" }], // //Initiative County
        [{ validity: Validity.Valid }, { validity: Validity.Valid }], //Paid or Volunteer Group
        [
          {
            validity: Validity.Invalid,
            text: "Description is too long",
            reason: "Incorrect",
            note: "TLDR",
          },
        ], //Initiative Description
        [{ validity: Validity.Valid }], //Initiative Type
        [{ validity: Validity.Valid }], //Elector County
        [{ validity: Validity.Valid }], //Circulator ID (Front)
        [{ validity: Validity.Valid }], //Initiative Serial Number (Front)
        [{ validity: Validity.Valid }], //Notarized County
        [{ validity: Validity.Valid }], //Circulator Name
        [{ validity: Validity.Valid }], //Circulator County
        [{ validity: Validity.Valid }], //Circulator Signature
        [{ validity: Validity.Valid }], //Circulator Address
        [{ validity: Validity.Valid }], //Circulator City State Zip
        [{ validity: Validity.Invalid, reason: "Other" }], //Notarization Date
        [{ validity: Validity.Valid }], //Notary Seal Expiration Date
        [{ validity: Validity.Valid }], //Notary Signature
        [{ validity: Validity.Valid, text: "AZ95389" }], //Circulator ID (Back)
        [{ validity: Validity.Invalid, reason: "Missing" }], //Initiative Serial Number (Back)
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review voter registration columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewVoterRegistration([
        RegisteredVoterFlags.Valid,
        RegisteredVoterFlags.NotRegistered,
        RegisteredVoterFlags.NotRegistered,
        RegisteredVoterFlags.NotRegistered,
        RegisteredVoterFlags.NotRegistered,
        RegisteredVoterFlags.NotRegistered,
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });
  });

  context("Review all for 2019 Initiative", () => {
    it("should be able to review signature columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("Signature Column", [
        // Sheet 1
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Strikethrough },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        // Sheet 2
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Strikethrough },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Valid },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review name columns for 2019 Initiative", () => {
      //First, Middle and Last Name
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Name Column", [
        // NOTE: for duplicate testing all names should match exactly
        // Sheet 1
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid, text: "Boettcher" }],
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid }],
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid }],
        // Strikethrough
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid }], // Beverly Sutton
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid }], // Stephanie Deppe
        [{ validity: Validity.Valid, text: "Patricia" }, { validity: Validity.Unknown }, { validity: Validity.Valid, text:"Orr" }],
        [{ validity: Validity.Valid, text: "Yolanda" }, { validity: Validity.Unknown }, { validity: Validity.Valid }],
        // Sheet 2
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid, text: "Boettcher" }],
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid }],
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid }],
        // Strikethrough
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid }],
        [{ validity: Validity.Valid }, { validity: Validity.Unknown }, { validity: Validity.Valid }],
        [{ validity: Validity.Valid, text: "Patricia" }, { validity: Validity.Unknown }, { validity: Validity.Valid, text:"Orr" }],
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review address columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Address", [
        // Sheet 1
        [ // Row 1
          { validity: Validity.Valid, text: "7887 E Uhl St #1607" },
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [ // Row 2
          { validity: Validity.Valid, text: "7202 E Chelsie Kaye Ln" },
          { validity: Validity.Valid, text: "85730" },
          { validity: Validity.Valid },
        ],
        [ // Row 3
          { validity: Validity.Valid, text: "8449 E. PENA BLANCA Dr" },
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        // Strikethrough
        [ // Row 5
          { validity: Validity.Valid },
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [ // Row 6
          { validity: Validity.Valid, text: "16420 N Starboard Dr" },
          { validity: Validity.Valid },
          { validity: Validity.Valid, text: "Tucson" },
        ],
        [ // Row 7
          { validity: Validity.Valid, text: "6710 E Golf Links Rd" },
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [ // Row 8 // changing to a completely different address outside of bounds
          { validity: Validity.Valid, text: "46467 W FARRELL RD" },
          { validity: Validity.Valid, text: "85139" },
          { validity: Validity.Valid, text: "MARICOPA" },
        ],

        // Sheet 2
        [ // Row 1
          { validity: Validity.Valid, text: "7887 E Uhl St #1607" },
          { validity: Validity.Valid },
          { validity: Validity.Valid },
        ],
        [ // Row 2, Exact match on address
          { validity: Validity.Valid, text: "7202 E Chelsie Kaye Ln" },
          { validity: Validity.Valid, text: "85730"},
          { validity: Validity.Valid },
        ],
        [ // Row 3, Only Street Address matches
          { validity: Validity.Valid, text: "8449 E. PENA BLANCA Dr" },
          { validity: Validity.Valid, text: "85250" },
          { validity: Validity.Valid, text: "Scottsdale" },
        ],
        // Strikethrough
        [ // Row 5, Only City Matches
          { validity: Validity.Valid, text: "2341 S Thomas Dr" },
          { validity: Validity.Valid, text: "85250" },
          { validity: Validity.Valid },
        ],
        [ // Row 6, Only Zip Matches
          { validity: Validity.Valid, text: "16421 E Starboards Dr" },
          { validity: Validity.Valid },
          { validity: Validity.Valid, text: "Scottsdale" },
        ],
        [ // Row 7, None of them match
          { validity: Validity.Valid, text: "6715 W Golf Jinks St" },
          { validity: Validity.Valid, text: "85250" },
          { validity: Validity.Valid, text: "Scottsdale" },
        ],
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review date signed columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("Date Signed", [
        // Sheet 1
        { validity: Validity.Valid },
        { validity: Validity.Valid, text: "1/25/2020" },
        { validity: Validity.Valid },
        // Strikethrough
        { validity: Validity.Valid, text: "1/25/2020" },
        { validity: Validity.Valid, text: "1/25/2020" },
        { validity: Validity.Valid, text: "1/25/2020" },
        { validity: Validity.Valid, text: "1/25/2020" },
        // Sheet 2
        { validity: Validity.Valid },
        { validity: Validity.Valid, text: "1/25/2020" },
        { validity: Validity.Valid },
        // Strikethrough
        { validity: Validity.Valid, text: "1/25/2020" },
        { validity: Validity.Valid, text: "1/25/2020" },
        { validity: Validity.Valid, text: "1/25/2020" },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review voter registration columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewVoterRegistration([
        // Sheet 1
        RegisteredVoterFlags.Valid,
        RegisteredVoterFlags.NotRegistered,
        RegisteredVoterFlags.Valid,
        // Strikethrough
        RegisteredVoterFlags.Valid,
        RegisteredVoterFlags.Valid,
        RegisteredVoterFlags.Valid,
        RegisteredVoterFlags.NotRegistered,

        // Sheet 2
        RegisteredVoterFlags.Valid,
        RegisteredVoterFlags.NotRegistered,
        RegisteredVoterFlags.NotRegistered,
        // Strikethrough
        RegisteredVoterFlags.NotRegistered,
        RegisteredVoterFlags.NotRegistered,
        RegisteredVoterFlags.NotRegistered,
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it.skip("should be able to review all fields for 2019 Initative", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Fields", [
        [
          {
            validity: Validity.Invalid,
            text: "Description is too long",
            reason: "Incorrect",
            note: "TLDR",
          },
        ], //Initiative Description
        [{ validity: Validity.Valid }, { validity: Validity.Valid }], //Paid Circulator / Volunteer
        [{ validity: Validity.Valid, text: "Maricopa" }], // //Initiative County
        [{ validity: Validity.Valid }], //Initiative Type
        [{ validity: Validity.Valid }], //Elector County
        [{ validity: Validity.Valid }], //Circulator ID (Front)
        [{ validity: Validity.Valid }], //Initiative Serial Number (Front)
        [{ validity: Validity.Valid }], //Notarized County
        [{ validity: Validity.Valid }], //Circulator Name
        [{ validity: Validity.Valid }], //Circulator County
        [{ validity: Validity.Valid }], //Circulator Signature
        [{ validity: Validity.Valid }], //Circulator Address
        [{ validity: Validity.Valid }], //Circulator City State Zip
        [{ validity: Validity.Invalid, reason: "Other" }], //Notarization Date
        [{ validity: Validity.Valid }], //Notary Seal Expiration Date
        [{ validity: Validity.Valid }], //Notary Signature
        [{ validity: Validity.Valid, text: "AZ95389" }], //Circulator ID (Back)
        [{ validity: Validity.Invalid, reason: "Missing" }], //Initiative Serial Number (Back)
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });
  });

  context.skip("Review all columns for 2019 Candidate", () => {
    it("should be able to review signature columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("Signature Column", [
        { validity: Validity.Valid },
        { validity: Validity.Invalid },
        {
          validity: Validity.Invalid,
          reason: "Missing",
          note: "What Happened?",
        },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review printed name columns for 2019 Candidate", () => {
      //Single Box
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Name Column", [
        [{ validity: Validity.Valid }],
        [
          {
            validity: Validity.Invalid,
            reason: "Illegible",
            note: "Back to School",
          },
        ],
        [{ validity: Validity.Valid }],
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review address columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Address", [
        [{ validity: Validity.Valid }],
        [
          {
            validity: Validity.Invalid,
            reason: "Illegible",
            note: "Is that an 8?",
          },
        ],
        [{ validity: Validity.Invalid, text: "San Diego", reason: "Invalid" }],
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review date signed columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("Date Signed", [
        { validity: Validity.Valid },
        {
          validity: Validity.Invalid,
          reason: "Missing",
          note: "Get a Calendar",
        },
        { validity: Validity.Valid },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review all fields for 2019 Candidate", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Fields", [
        [{ validity: Validity.Valid }], //Circulator County
        [
          {
            validity: Validity.Invalid,
            text: "Mars",
            reason: "Incorrect",
            note: "Space",
          },
        ], //District
        [{ validity: Validity.Valid, text: "All night long" }], //Party
        [{ validity: Validity.Valid }], //Candidate Name
        [{ validity: Validity.Valid }], //Candidate Address
        [{ validity: Validity.Valid }], //Candidate County
        [{ validity: Validity.Valid }], //Candidate Office
        [{ validity: Validity.Valid }], //Election Date
        [{ validity: Validity.Valid }], //Circulator Affidavit Name
        [{ validity: Validity.Valid }], //Circulator Affidavit County
        [{ validity: Validity.Valid }], //Circulator Signature
        [{ validity: Validity.Valid }], //Circulator Printed Name
        [{ validity: Validity.Valid }], //Circulator Address
        [{ validity: Validity.Valid }], //Circulator City Zip
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review voter registration columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewVoterRegistration([
        RegisteredVoterFlags.MismatchedName,
        RegisteredVoterFlags.MismatchedAddress,
        RegisteredVoterFlags.NotRegistered,
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });
  });

  context.skip("Review all columns for 2019 Candidate E-qual", () => {
    it("should be able to review signature columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("Signature Column", [
        { validity: Validity.Valid },
        { validity: Validity.Invalid },
        {
          validity: Validity.Invalid,
          reason: "Missing",
          note: "What Happened?",
        },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
        { validity: Validity.Strikethrough },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review printed name columns for 2019 Candidate E-qual", () => {
      //Single Box
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Name Column", [
        [{ validity: Validity.Valid }],
        [
          {
            validity: Validity.Invalid,
            reason: "Illegible",
            note: "Back to School",
          },
        ],
        [{ validity: Validity.Valid }],
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review actual address columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Address", [
        [{ validity: Validity.Valid }],
        [
          {
            validity: Validity.Invalid,
            reason: "Illegible",
            note: "Is that an 8?",
          },
        ],
        [{ validity: Validity.Invalid, text: "San Diego", reason: "Invalid" }],
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review date signed columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("Date Signed", [
        { validity: Validity.Valid },
        {
          validity: Validity.Invalid,
          reason: "Missing",
          note: "Get a Calendar",
        },
        { validity: Validity.Valid },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review Voter Id column", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewColumn("VoterID", [
        { validity: Validity.Valid },
        { validity: Validity.Invalid },
        { validity: Validity.Valid },
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review all fields for 2019 Candidate E-qual", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewMultipleColumns("Fields", [
        [
          {
            validity: Validity.Invalid,
            text: "Mars",
            reason: "Incorrect",
            note: "Space",
          },
        ], //District
        [{ validity: Validity.Valid, text: "All night long" }], //Party
        [{ validity: Validity.Valid }], //Circulation County
        [{ validity: Validity.Valid }], //Candidate County
        [{ validity: Validity.Valid }], //Candidate Name
        [{ validity: Validity.Valid }], //Candidate Address
        [{ validity: Validity.Valid }], //Candidate Office
        [{ validity: Validity.Valid }], //Election Date
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });

    it("should be able to review voter registration columns", () => {
      cy.signIn("<EMAIL>", "staff");
      cy.visit("/work");
      reviewVoterRegistration([
        RegisteredVoterFlags.MismatchedName,
        RegisteredVoterFlags.MismatchedAddress,
        RegisteredVoterFlags.NotRegistered,
      ]);
      cy.findByText(/ALL DONE/i).should("be.visible");
    });
  });
});
