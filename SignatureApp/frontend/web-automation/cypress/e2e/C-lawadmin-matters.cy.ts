import "cypress-network-idle";
import {
  chooseRegisteredVoterSet,
  createEntireMatter,
  createMatter,
  uploadCirculatorRegistrationFile,
  uploadSignatureSheet,
} from "../support/lawadmin-matter";
import { deleteMatterIfNecessary } from "../support/lawadmin-cleanup";
import { MatterType } from "../../../src/types/matter.types";

describe("Law Admin - Matters", () => {
  beforeEach(() => {
    cy.signIn("<EMAIL>", "admin");
    cy.visit("/admin");
    cy.waitForNetworkIdle(500);
  });

  describe("2019 Initiative Matter", () => {
    const matterName = "Cypress 2019 Initiative Matter";
    const voterFileName = "Pima";
    const templateName = "Cypress 2019 Initiative Template";
    const signatureSheetFiles = [
      "I-24-2020 cropped.00004.pdf",
      "I-24-2020 half page horiz.00005.pdf",
      "I-24-2020 half page vert.00006.pdf",
      "28330 I-24-2020 Pima.pdf",
      "28331 I-24-2020 Pima.pdf",
    ];

    let sasUrl: string;
    before(() => {
      cy.signIn("<EMAIL>", "admin");
      cy.visit("/admin");
      cy.waitForNetworkIdle(500);
      deleteMatterIfNecessary(matterName);

      const containerAndFiles = {
        containerName: matterName.toLowerCase().replace(" ", "-"),
        signatureSheetFiles,
      };
      cy.task("createBlobContainerWithFiles", containerAndFiles).then((url) => {
        sasUrl = url as string;
        cy.log(sasUrl);
      });
    });

    it("should be able to create a new matter", () => {
      createMatter(matterName, MatterType.Initiative);
    });

    it("should be able to view newly created matter", () => {
      cy.findByRole("cell", { name: matterName }).should("be.visible").click();
    });

    it("should be able to choose set of registered voters to verify against", () => {
      cy.findByRole("cell", { name: matterName }).should("be.visible").click();
      chooseRegisteredVoterSet(matterName, voterFileName);
    });

    it("should be able to upload a signed petition", () => {
      cy.findByRole("cell", { name: matterName }).should("be.visible").click();
      cy.waitForNetworkIdle(500);
      uploadSignatureSheet(matterName, templateName, sasUrl);
    });

    it("should be able to upload circulator registration information", () => {
      cy.findByRole("cell", { name: matterName }).should("be.visible").click();
      uploadCirculatorRegistrationFile(matterName);
    });
  });

  describe("2019 Candidate Matter", () => {
    const matterName = "Cypress 2019 Candidate Matter";
    const voterFileName = "Pima";
    const templateName = "Cypress 2019 Candidate Template";
    const signatureSheetFiles = ["2020.04.06-Soto-J-00001.pdf"];
    const eQualTemplateName = "Cypress 2019 Candidate E-Qual Template";
    const eQualSheetFiles = ["2020.04.06-Soto-J-EQual-00102.pdf"];

    it("should be able to create the entire matter", () => {
      createEntireMatter(
        matterName,
        voterFileName,
        templateName,
        signatureSheetFiles
      );
      /*return createContainerAndUpload(matterName, eQualSheetFiles).then((sasUrl) => {
        uploadSignatureSheet(matterName, eQualTemplateName, sasUrl);
      });*/ //Hitting a bug with mutiple tasks
    });
    it("should be able to create entire eQual matter", () => {
      createEntireMatter(
        "Cypress 2019 Candidate E-Qual Matter",
        voterFileName,
        eQualTemplateName,
        eQualSheetFiles
      );
    });
  });

  describe("2022 Initiative Matter", () => {
    const matterName = "Cypress 2022 Initiative Matter";
    const voterFileName = "Maricopa";
    const templateName = "Cypress 2022 Initiative Template";
    const signatureSheetFiles = [
      //"0I-05-2024-MAR-000006_6_F.tif",
      //"0I-05-2024-MAR-000006_6_B.tif",
      "000250.pdf",
      "000251.pdf",
      "000252_b.pdf",
      // "000263.pdf",
    ];

    it("should be able to create the entire matter", () => {
      createEntireMatter(
        matterName,
        voterFileName,
        templateName,
        signatureSheetFiles
      );
    });
  });
});
