import 'cypress-network-idle';
import {
  deleteInvitedUser,
  inviteTeamMember,
  uploadVoterFiles,
} from "../support/lawadmin-setup";
import {deleteVoterRegistrationIfNecessary} from "../support/lawadmin-cleanup";

describe("Law Admin - Setup", () => {
  beforeEach(() => {
    cy.signIn("<EMAIL>", "admin");
    cy.visit("/admin");
    cy.waitForNetworkIdle(500);
  });

  context("Authentication", () => {
    it("should be able to sign in and see home page", () => {
      cy.findByText(/Logged in as Test Admin/i).should("be.visible");
      cy.findByText(/Administrator/i).should("be.visible"); // This is the correct page
    });
    it("should be able to log out from home page", () => {
      cy.findByRole("button", {name: /Log out/i})
        .should("be.enabled")
        .click();
      cy.findByText(/Sign in/i).should("be.visible");
    });
    it("should not be able to access home page after logging out", () => {
      cy.findByRole("button", {name: /Log out/i})
        .should("be.enabled")
        .click();
      cy.visit("/admin");
      cy.findByText(/Sign in/i).should("be.visible"); //Should not be able to see admin page
    });
  });

  context("Inviting other users", () => {
    const inviteLawAdminEmail = "<EMAIL>"
    const inviteStaffManagerEmail = "<EMAIL>"

    before(() => {
      deleteInvitedUser(inviteLawAdminEmail);
      deleteInvitedUser(inviteStaffManagerEmail);
    });

    it("should be able to invite another law admin", () => {
      inviteTeamMember("Law Admin", inviteLawAdminEmail);
    });

    it("should be able to invite another staff manager", () => {
      inviteTeamMember("Staff Manager", inviteStaffManagerEmail);
    });
  });

  context("Upload Pima county voter file", () => {
    const county = "Pima";
    before(() => {
      cy.signIn("<EMAIL>", "admin");
      deleteVoterRegistrationIfNecessary(county);
    });

    it("should be able to upload Pima county voter files", () => {
      uploadVoterFiles(county,
        [...Array(2).keys()].map(index =>
          `./cypress/fixtures/VoterFiles/PimaCountyVoterRegistration_part${index + 1}.csv`),
        [{columnName: "Registration Status", mapsTo: "Status"}]
      );
    });
  });

  context("Upload Maricopa county voter files", () => {
    const county = "Maricopa";
    before(() => {
      cy.signIn("<EMAIL>", "admin");
      deleteVoterRegistrationIfNecessary(county);
    });

    it("should be able to upload Maricopa county voter files", () => {
      uploadVoterFiles("Maricopa",
        [...Array(28).keys()].map(index =>
          `./cypress/fixtures/VoterFiles/MaricopaCountyVoterRegistration_part${index + 1}.csv`),
        [{columnName: "RegistrationID", mapsTo: "VoterId"},
          {columnName: "DateOfRegistration", mapsTo: "RegistrationDate"},
          {columnName: "ResidenceStreetAddress", mapsTo: "StreetAddress"},
          {columnName: "ResidenceCity", mapsTo: "City"},
          {columnName: "ResidenceState", mapsTo: "State"},
          {columnName: "ResidenceZip", mapsTo: "ZipCode"},
          {columnName: "Federal", mapsTo: "FedIdOnly"},
          {columnName: "YearOfBirth", mapsTo: "BirthYear"},
        ]
      );
    });
  });
});
