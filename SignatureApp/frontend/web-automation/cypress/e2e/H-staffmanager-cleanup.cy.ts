import 'cypress-network-idle';
import {
  deleteCypressStaffGroups,
  deleteCypressReviewers,
} from "../support/staffmanager-cleanup";

describe("Staff Manager - Cleanup", () => {
  beforeEach(() => {
    cy.signIn("<EMAIL>", "manager");
    cy.visit("/manager");
  });

  it("should be able to delete all Cypress Staff Groups", () => {
    deleteCypressStaffGroups();
  });

  it("should be able to delete all Cypress reviewers", () => {
    deleteCypressReviewers();
  });
});
