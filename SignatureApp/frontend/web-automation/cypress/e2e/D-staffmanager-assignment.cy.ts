import 'cypress-network-idle';
import {
  assignTaskAndMembersToGroupForMatter,
  createStaffGroup,
  createUserforStaffGroup,
} from "../support/staffmanager-assignment";
import { deleteCypressStaffGroups } from "../support/staffmanager-cleanup";
import { electionMatters } from "../support/templates";

describe("Staff Manager - Assignment", () => {
  const matter = electionMatters.Initiative2022; //TODO Only working for Initiatives for now, need to change the how Fields are created for Candidate Matters
  const startingNumber: number = 1;

  before(() => {
    cy.signIn("<EMAIL>", "manager");
    deleteCypressStaffGroups();
  });

  beforeEach(() => {
    cy.signIn("<EMAIL>", "manager");
    cy.visit("/manager");
  });

  it("should be able to sign in and see home page", () => {
    cy.findAllByText(/Manager/i).should("be.visible");
  });

  it.skip("should be able to invite a new reviewer", () => {
    cy.findByRole("button", { name: /Invite reviewer/i })
      .should("be.enabled")
      .click();
    cy.findByRole("dialog", { name: /Invite Reviewer/i })
      .should("be.visible")
      .within(() => {
        cy.findByRole("textbox")
          .should("be.visible")
          .type("<EMAIL>");
        cy.findByRole("button", { name: /Invite/i })
          .should("be.enabled")
          .click();
      });
  });

  it.skip("should be able to create a new staff group", () => {
    createStaffGroup(`${startingNumber} - Cypress Sheet Review Group`);
    createStaffGroup(`${startingNumber + 1} - Cypress Signature Group`);
    createStaffGroup(`${startingNumber + 2} - Cypress Name Group`);
    createStaffGroup(`${startingNumber + 3} - Cypress Address Group`);
    createStaffGroup(`${startingNumber + 4} - Cypress Date Signed Group`);
    createStaffGroup(
      `${startingNumber + 5} - Cypress Voter Registration Group`
    );
    createStaffGroup(`${startingNumber + 6} - Cypress Field Group`);
    createStaffGroup(`${startingNumber + 7} - Cypress Voter ID Group`);
  });

  it.skip("should be able to assign a task to a group", () => {
    const allFieldNames = matter.fields.map((f) =>
      f.groupName ? `${f.groupName} field group` : `${f.find} field`
    );
    const fieldNames = [...new Set(allFieldNames)]; // de-dupe
    assignTaskAndMembersToGroupForMatter(
      ["Signature column"],
      [`Test Reviewer${startingNumber}`],
      `${startingNumber} - Cypress Signature Group`,
      matter.name
    );
    assignTaskAndMembersToGroupForMatter(
      ["Printed name column"],
      [`Test Reviewer${startingNumber + 1}`],
      `${startingNumber + 1} - Cypress Name Group`,
      matter.name
    );
    assignTaskAndMembersToGroupForMatter(
      ["Full address column"],
      [`Test Reviewer${startingNumber + 2}`],
      `${startingNumber + 2} - Cypress Address Group`,
      matter.name
    );
    assignTaskAndMembersToGroupForMatter(
      ["Date of signing column"],
      [`Test Reviewer${startingNumber + 3}`],
      `${startingNumber + 3} - Cypress Date Signed Group`,
      matter.name
    );
    assignTaskAndMembersToGroupForMatter(
      ["Voter ID column"],
      [`Test Reviewer${startingNumber + 6}`],
      `${startingNumber + 6} - Cypress Date Signed Group`,
      matter.name
    );
    assignTaskAndMembersToGroupForMatter(
      ["Match Name and Address to Registered Voter"],
      [`Test Reviewer${startingNumber + 4}`],
      `${startingNumber + 4} - Cypress Voter Registration Group`,
      matter.name
    );
    assignTaskAndMembersToGroupForMatter(
      fieldNames,
      [`Test Reviewer${startingNumber + 5}`],
      `${startingNumber + 5} - Cypress Field Group`,
      matter.name
    );
  });

  it("should be able to create a new staff and group based on matter", () => {
    let groupNumber = startingNumber;
    createUserforStaffGroup(groupNumber);
    createStaffGroup(`${groupNumber} - Cypress Sheet Review Group`); // Create a new group for "Match Name and Address to Registered Voter
    groupNumber++;

    // Creates groups based on the columns in the matter, create a user for each group
    const allColumns: string[] = matter.columns
      .filter((actualColumns) => actualColumns) // Filter out empty values
      .map((column) => {
        if (column.includes("Address") || column.includes("City"))
          return "Address";
        if (
          column.includes("Name") ||
          column.includes("First") ||
          column.includes("Last")
        )
          return "Name";
        return column;
      });
    const staffGroups = [...new Set(allColumns)]; // de-duplicate

    staffGroups.forEach((columnName) => {
      createStaffGroup(
        `${groupNumber} - Cypress ${columnName.toString()} Group`
      );
      createUserforStaffGroup(groupNumber);
      groupNumber++;
    });

    createUserforStaffGroup(groupNumber);
    createStaffGroup(`${groupNumber} - Cypress Voter Registration Group`); // Create a new group for "Match Name and Address to Registered Voter
    createUserforStaffGroup(groupNumber + 1);
    createStaffGroup(`${groupNumber + 1} - Cypress Field Group`); // Create a new group for fields
  });

  it("should be able to assign tasks to a existing groups", () => {
    //Assign tasks to groups created above to a user
    cy.findAllByRole("row", { name: /Cypress.*Group/i }).each(($row, index) => {
      const currentGroup = startingNumber + index;
      const groupName = $row.text().trim();
      const taskName =
        $row
          .text()
          .trim()
          .match(/Cypress (.*?) Group/)?.[1] + " column" || ""; // Task name is between Cypress and Group, +column because fields contain some of the same Tasks (i.e. Address vs County Address)

      if (taskName.includes("Field")) {
        // If field group, assign all fields to the group
        const allFieldNames = matter.fields.map((f) =>
          f.groupName ? `${f.groupName} field group` : `${f.find} field`
        );
        const fieldNames = [...new Set(allFieldNames)]; // de-dupe

        assignTaskAndMembersToGroupForMatter(
          fieldNames,
          [`Cypress Reviewer${currentGroup}`],
          `${currentGroup} - Cypress Field Group`,
          matter.name
        );
      }
      else if (taskName.includes("Sheet Review")) {
          assignTaskAndMembersToGroupForMatter(
            ["Signature Sheet Review"],
            [`Cypress Reviewer${currentGroup}`],
            `${currentGroup} - Cypress Sheet Review Group`,
            matter.name
          );
      }
      else if (taskName.includes("Voter Registration")) {
        assignTaskAndMembersToGroupForMatter(
          ["Match Name and Address to Registered Voter"],
          [`Cypress Reviewer${currentGroup}`],
          `${currentGroup} - Cypress Voter Registration Group`,
          matter.name
        );
      } else {
        assignTaskAndMembersToGroupForMatter(
          [taskName],
          [`Cypress Reviewer${currentGroup}`],
          groupName,
          matter.name
        );
      }
    });
  });
});
