import "cypress-network-idle";
import { deleteMatterIfNecessary } from "./lawadmin-cleanup";
import { MatterType } from "../../../src/types/matter.types";

export function createContainerAndUpload(matterName: string, signatureSheetFiles: string[]): Cypress.Chainable<string> {
  const containerAndFiles = {
    containerName: matterName.toLowerCase().replace(" ", "-"),
    signatureSheetFiles,
  };
  return cy.task("createBlobContainerWithFiles", containerAndFiles);
}

export function createMatter(name: string, type: MatterType, numSignaturesRequired: number = 10, dateFiled?: string) {
  const date = dateFiled ? new Date(dateFiled) : new Date();
  if (!dateFiled) {
    date.setMonth(date.getMonth() + 1);
  }

  cy.findByRole("link", { name: /new matter/i })
    .should("be.visible")
    .click();
  cy.url().should("include", "/admin/matters/new");
  cy.findByPlaceholderText(/Enter matter name/i)
    .should("be.visible")
    .type(name);
  cy.findByRole("combobox").should("be.visible").select(type);
  cy.findByPlaceholderText(/Enter signatures required/i)
    .should("be.visible")
    .type(numSignaturesRequired.toString());
  cy.findByPlaceholderText(/Enter date file/i)
    .should("be.visible")
    .type(date.toISOString().substring(0, 10));
  cy.findByRole("button", { name: /Save/i }).should("be.enabled").click();
  cy.url().should("include", "/details");
}

export function chooseRegisteredVoterSet(matterName: string, voterFileName: string) {
  cy.findByRole("button", { name: /Select Files/i })
    .should("be.visible")
    .click();
  cy.url().should("include", "/voters");
  cy.waitForNetworkIdle(500);
  cy.findByRole("checkbox", { name: `${voterFileName}.csv` })
    .should("be.enabled", { timeout: 30000 })
    .check();
  cy.findByRole("checkbox", { name: `${voterFileName}.csv` }).should("be.checked");

  cy.intercept("GET", "/api/templates").as("getTemplates");
  cy.intercept("GET", "api/matters/*/status/progress").as("getProgress");
  cy.intercept("GET", "api/matters/*/status/checklist").as("getChecklist");
  cy.findByRole("button", { name: /Save/i }).should("be.enabled").click();
  cy.url().should("include", "/details", { timeout: 30000 });
  cy.wait(["@getTemplates", "@getProgress", "@getChecklist"], {
    timeout: 30000,
  });
}

export function uploadSignatureSheet(matterName: string, templateName: string, sasUrl: string) {
  cy.get("select").should(($optionsArray) => {
    expect($optionsArray.get(0)).contain(templateName);
  });
  cy.get("select").select(templateName, { force: true });
  cy.get("option:selected").contains(templateName);

  cy.findByPlaceholderText("Download URL").should("be.visible").clear().type(sasUrl);
  cy.findByRole("button", { name: /Start/i }).should("be.enabled").click();
  cy.get(".spinner-border", { timeout: 100_000 }).should("not.exist");

  cy.findByText(/Total Uploads: 1. Total Valid Sheets: [1-9][0-9]*/i, {
    timeout: 100_000,
  }).should("be.visible");
  cy.findByText(/[1-9][0-9]* rules have been created/i).should("be.visible");
}

export function uploadCirculatorRegistrationFile(
  matterName: string,
  circulatorFileName: string = "./cypress/fixtures/AZCirculatorRegistry2022.csv"
) {
  cy.findByRole("button", { name: /Upload Circulators/i })
    .should("be.visible")
    .click();

  cy.get("input[type=file]").eq(0).selectFile(circulatorFileName);
  cy.findByRole("button", { name: /Upload/i })
    .should("be.enabled")
    .click();
  cy.findByText(/File uploaded successfully/i, { timeout: 45000 }).should("be.visible");
  cy.findByRole("link", { name: /Back to Matter/i })
    .should("be.visible")
    .click();
}

export function createEntireMatter(
  matterName: string,
  voterFileName: string,
  templateName: string,
  signatureSheetFiles: string[]
) {
  cy.signIn("<EMAIL>", "admin");
  cy.waitForNetworkIdle(500);

  deleteMatterIfNecessary(matterName);
  return createContainerAndUpload(matterName, signatureSheetFiles).then((sasUrl) => {
    const matterType = matterName.includes("Candidate") ? MatterType.Candidate : MatterType.Initiative;
    createMatter(matterName, matterType);
    chooseRegisteredVoterSet(matterName, voterFileName);
    uploadSignatureSheet(matterName, templateName, sasUrl);
    uploadCirculatorRegistrationFile(matterName);
  });
}
