export const fieldsFor2019Candidate = [
  { find: "Circulation County" },
  { find: "District" },
  { find: "Party" },
  { find: "Candidate Name" },
  { find: "Candidate Address" },
  { find: "Candidate County" },
  { find: "Candidate Office" },
  { find: "Election Date" },
  { find: "Circulator Affidavit Name" },
  { find: "Circulator Affidavit County" },
  { find: "Circulator Signature" },
  { find: "Circulator Printed Name" },
  { find: "Circulator Address" },
  { find: "Circulator City Zip" },
];

export const columnsFor2019Candidate = [
  "",
  "Signature",
  "Printed name",
  "Full address",
  "Date of signing",
];

export const fieldsFor2020EQualCandidate = [
  { find: "District" },
  { find: "Party" },
  { find: "Circulation County" },
  { find: "Candidate County" },
  { find: "Candidate Name" },
  { find: "Candidate Address" },
  { find: "Candidate Office" },
  { find: "Election Date" },
];

export const columnsFor2020EQualCandidate = [
  "",
  "Signature",
  "Printed name",
  "Full address",
  "Date of signing",
  "Voter ID",
];

export const fieldsFor2022Initiative = [
  // front
  { find: "COUNTY" },
  {
    find: "PAID CIRCULATOR",
    inputType: "Checkbox",
    groupName: "PAID or VOLUNTEER",
  },
  { find: "VOLUNTEER", inputType: "Checkbox", groupName: "PAID or VOLUNTEER" },
  { find: "Initiative Description", inputType: "TextArea" },
  { find: "Initiative Type" },
  { find: "Elector County" },
  { find: "Circulator ID (Front)", prefix: "AZ" },
  { find: "Initiative Serial Number (Front)", prefix: "I-" },
  // back
  { find: "Notarized County" },
  { find: "Circulator Name" },
  { find: "Circulator County" },
  { find: "Circulator Signature" },
  { find: "Circulator Address" },
  { find: "Circulator City State Zip" },
  { find: "Notarization Date" },
  { find: "Notary Seal Expiration Date" },
  { find: "Notary Signature" },
  { find: "Circulator ID (Back)", prefix: "AZ" },
  { find: "Initiative Serial Number (Back)", prefix: "I-" },
];

export const columnsFor2022Initiative = [
  "",
  "Signature",
  "First Name",
  "Last Name",
  "Street Address",
  "PO Address & zip code",
  "City or Town",
  "Date signed",
];

export const fieldsFor2019Initiative = [
  // front
  { find: "Initiative Description", inputType: "TextArea" },
  {
    find: "Paid Circulator",
    inputType: "Checkbox",
    groupName: "PAID or VOLUNTEER",
  },
  { find: "Volunteer", inputType: "Checkbox", groupName: "PAID or VOLUNTEER" },
  { find: "Initiative County" },
  { find: "Initiative Type" },
  { find: "Elector County" },
  { find: "Circulator ID (Front)", prefix: "AZ" },
  { find: "Initiative Serial Number (Front)", prefix: "I-" },
  // back
  { find: "Notarized County" },
  { find: "Circulator Name" },
  { find: "Circulator County" },
  { find: "Circulator Signature" },
  { find: "Circulator Address" },
  { find: "Circulator City State Zip" },
  { find: "Notarization Date" },
  { find: "Notary Seal Expiration Date" },
  { find: "Notary Signature" },
  { find: "Circulator ID (Back)", prefix: "AZ" },
  { find: "Initiative Serial Number (Back)", prefix: "I-" },
];

export const columnsFor2019Initiative = [
  "",
  "Signature",
  "First Name",
  "Middle Name",
  "Last Name",
  "Street Address",
  "PO Address & zip code",
  "City or Town",
  "Date signed",
];

export const electionMatters = {
  Candidate2019: {
    name: "Cypress 2019 Candidate Matter",
    columns: columnsFor2019Candidate,
    fields: fieldsFor2019Candidate,
  },
  Candidate2019Equal: {
    name: "Cypress 2019 Candidate E-Qual Matter",
    columns: columnsFor2020EQualCandidate,
    fields: fieldsFor2020EQualCandidate,
  },
  Initiative2019: {
    name: "Cypress 2019 Initiative Matter",
    columns: columnsFor2019Initiative,
    fields: fieldsFor2019Initiative,
  },
  Initiative2022: {
    name: "Cypress 2022 Initiative Matter",
    columns: columnsFor2022Initiative,
    fields: fieldsFor2022Initiative,
  },
};
