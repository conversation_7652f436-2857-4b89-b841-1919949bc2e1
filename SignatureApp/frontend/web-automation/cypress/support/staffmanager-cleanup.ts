import 'cypress-network-idle';

export function deleteReviewerFromUI(email: string) {
  cy.contains("tr", email)
    .should("be.visible")
    .within(() => {
      cy.findByRole("img", { hidden: true }).should("exist").click(); //Trash Can Icon
    });
  cy.findByRole("dialog", { name: /Confirmation Modal/i })
    .should("be.visible")
    .within(() => {
      cy.findByRole("button", { name: /Confirm/i })
        .should("be.enabled")
        .click();
    });
  cy.contains("tr", email).should("not.exist");
}

export function deleteStaffGroupFromUI(groupName: string) {
  cy.contains("tr", groupName)
    .should("be.visible")
    .within(() => {
      cy.findByRole("button").should("exist").click();
    });
  cy.findByRole("dialog", { name: /Confirmation Modal/i })
    .should("be.visible")
    .within(() => {
      cy.findByRole("button", { name: /Confirm/i })
        .should("be.enabled")
        .click();
    });
  cy.contains("tr", groupName).should("not.exist");
}

export function deleteStaffGroupIfNecessary(groupName: string) {
  cy.visit("/manager");
  cy.waitForNetworkIdle(500);
  return cy
    .get(`tr:contains('${groupName}')`)
    .should("have.length.gte", 0)
    .then(($tr) => {
      if (!$tr.length) {
        return;
      }
      deleteStaffGroupFromUI(groupName);
    });
}

export function deleteCypressStaffGroups() {
  cy.visit("/manager");
  cy.waitForNetworkIdle(500);
  cy.get("body").then(($body) => {
    if ($body.find("tr").length > 0) {
      cy.get("tr").each(($tr) => {
        if ($tr.text().match(/Cypress.*Group/i)) {
          deleteStaffGroupFromUI($tr.text());
        }
      });
    }
  });
}

export function deleteCypressReviewers() {
  const deleteReviewerWorks = `DELETE t1 FROM dbo.Works t1 JOIN dbo.Users t2 ON t1.UserId = t2.ID;`;
  cy.task("runSqlQuery", deleteReviewerWorks);
  cy.visit("/manager");
  cy.waitForNetworkIdle(500);

  cy.get("body").then(($body) => {
    if ($body.find("tr").length > 0) {
      //Conditional check for empty table
      cy.get("tr").each(($tr) => {
        if ($tr.text().match(/Cypress.*com/i)) {
          deleteReviewerFromUI($tr.text());
        }
      });
    }
  });
}
