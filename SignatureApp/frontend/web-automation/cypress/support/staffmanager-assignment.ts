import 'cypress-network-idle';

export function createStaffGroup(groupName: string) {
  cy.findByRole("button", { name: /new staff group/i })
    .should("be.enabled")
    .click();
  cy.findByRole("dialog", { name: /New Staff Group Modal/i })
    .should("be.visible")
    .within(() => {
      cy.findByRole("textbox").should("be.visible").type(groupName);
      cy.findByRole("button", { name: /Create/i })
        .should("be.enabled")
        .click();
    });
  cy.contains("tr", groupName).should("exist");
}

export function assignTaskAndMembersToGroupForMatter(
  taskNames: string[],
  reviewerNames: string[],
  groupName: string,
  matterName: string
) {
  cy.contains("tr", groupName).should("exist").click();
  cy.waitForNetworkIdle(500);

  // select the matter
  cy.findByRole("combobox")
    .should("be.visible")
    .find("option")
    .its("length")
    .should("be.gte", 2);

  cy.findByRole("combobox").select(matterName);

  // select the task(s)
  cy.findByRole("button", { name: /Add Tasks/i })
    .should("be.visible")
    .click();
  cy.findByRole("dialog")
    .should("be.visible")
    .within(() => {
      taskNames.forEach((task) => {
        cy.findAllByRole("option").contains(task).should("exist").click(); //Switched to contains for Regex match
      });
      cy.findByRole("button", { name: /Save/i }).should("be.enabled").click();
    });

  taskNames.forEach((task) => {
    cy.contains("tr", task).should("exist");
  });

  // select the member(s)
  cy.findByRole("button", { name: /Add Members/i })
    .should("be.visible")
    .click();
  cy.findByRole("dialog")
    .should("be.visible")
    .within(() => {
      reviewerNames.forEach((reviewer) => {
        cy.findByRole("option", { name: reviewer }).should("exist").click();
      });
      cy.findByRole("button", { name: /Save/i }).should("be.enabled").click();
    });
  reviewerNames.forEach((reviewer) => {
    cy.contains("tr", reviewer).should("exist");
  });

  cy.findByRole("columnheader", { name: /Speed/i }).should("be.visible");
  cy.findByRole("columnheader", { name: /Def/i }).should("be.visible");
  cy.findByRole("columnheader", { name: /Time/i }).should("be.visible");
  cy.findByRole("columnheader", { name: /Ffr/i }).should("be.visible");
  cy.findByRole("progressbar").should("be.visible");

  cy.findByRole("link", { name: /Back to Home/i })
    .should("be.visible")
    .click();
}

export function createUserforStaffGroup(user: number) {
  const query = `
      IF NOT EXISTS (SELECT 1 FROM dbo.Users WHERE email = 'cypressreviewer${user}@test.com')
      BEGIN
      INSERT INTO dbo.Users
      VALUES('cypressreviewer${user}@test.com', 'Cypress', 'Cypress Reviewer${user}', 3, 'VNwzJWdgdd2Jq0fT7jPoFS1Rl27vqTS/zEdOfCBf8bQ=', 1, 1, 1, GETDATE(), 0, NULL, 0, 0, 0.00 , 0, GETDATE())
      END
    `;
  cy.task("runSqlQuery", query);
}
