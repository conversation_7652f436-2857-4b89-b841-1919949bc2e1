import {RegisteredVoterFlags, Validity} from "../../../src/types/work.types";

type ColumnReview = {
  validity: Validity;
  text?: string;
} & (
  | { validity: Validity.Invalid; reason?: string; note?: string }
  | {
      validity: Exclude<Validity, Validity.Invalid>;
      reason?: never;
      note?: never;
    }
); //Cant have notes or reason if not invalid

type MultipleColumnReview = [ColumnReview, ColumnReview?, ColumnReview?]; //There are some fields that have only two option groups

export function reviewSingleColumn(
  columnName: string,
  reviews: ColumnReview[]
) {
  var validityName = columnName.replace(" ", "");
  cy.findByText(/Begin Task/i).should("be.visible");
  cy.findByRole("button", { name: /Continue/i }).click();
  cy.findByRole("img", { name: /transcribable/i }).should("be.visible");

  reviews.forEach((row) => {
    var validity = Validity[row.validity].toString();
    var validityDataTestId = validityName + validity;
    cy.findByTestId(validityDataTestId).should("be.visible").check();
    if (validity == "Invalid") {
      cy.findByRole("combobox")
        .find("option")
        .contains(row.reason || "Invalid")
        .then((option) => {
          cy.findByRole("combobox").select(option.text());
        });
    }
    if (row.note) {
      cy.findByRole("textbox", { name: "Note" }).type(row.note);
    }
    cy.findByRole("button", { name: /Next/i }).should("be.enabled").click();
  });
}

export function reviewValidity(optionsGroup: JQuery<HTMLElement>) {
  cy.wrap(optionsGroup).within(() => {
    //wrap the element to work with it
    if (optionsGroup.find('input[type="text"]').length > 0) {
      // Some options have no text input
      cy.findByRole("textbox")
        .invoke("val")
        .then((value) => {
          if (value) {
            cy.findByRole("radio", { name: "Valid" }).check();
          } else {
            cy.findByRole("radio", { name: "Invalid" }).check();
            cy.findByRole("combobox")
              .find("option")
              .contains("Missing")
              .then((option) => {
                cy.findByRole("combobox").select(option.text());
              });
          }
        });
    } else {
      cy.findByRole("radio", { name: "Valid" }).check(); //or Strike it
    }
  });
}

export function finishTasks() {
  cy.intercept("GET", "/api/work/next").as("getWork");
  cy.get('*[class="container"]')
    .should("be.visible")
    .then(() => {
      cy.findByRole("heading")
        .should("be.visible")
        .then((heading) => {
          if (heading.text().includes("DONE")) {
            return; //Exit the loop once we reach last screen
          } else {
            cy.findAllByTestId("CypressOptions").each((element) => {
              reviewValidity(element);
            });

            cy.findByRole("button", { name: /Next/i })
              .should("be.enabled")
              .click();
            cy.wait("@getWork");
          }

          return finishTasks();
        });
    });
}

export function setValidity(
  optionGroup: JQuery<HTMLElement>,
  review: ColumnReview
) {
  const validity = Validity[review.validity].toString();

  cy.wrap(optionGroup).within(() => {
    if (review.text) {
      cy.findByRole("textbox").clear();
      cy.findByRole("textbox").type(review.text);
    } //Set Text if provided first as there is only one text box before setting validity

    cy.findByTestId(new RegExp(validity)).should("be.visible").check(); //Set validity

    if (
      validity == "Valid" &&
      optionGroup.find('input[type="text"]').length > 0
    ) {
      cy.findByRole("textbox")
        .invoke("val")
        .then((value) => {
          if (!value) {
            cy.findByRole("textbox").type("Valid");
          } //If no optional text but marked as valid, need to fill empty textbox
        });
    }
    if (validity == "Invalid") {
      // If Invalid must select reason why from dropdown
      cy.findByRole("combobox")
        .find("option")
        .contains(review.reason || "Other")
        .then((option) => {
          cy.findByRole("combobox").select(option.text());
        });
    }
    if (review.note) {
      cy.findByPlaceholderText(/Notes/).type(review.note);
    }
  });
}

export function reviewColumn(columnName: string, reviews: ColumnReview[]) {
  cy.intercept("GET", "/api/work/next").as("getWork");
  cy.findByText(/Begin Task/i).should("be.visible");
  cy.findByRole("button", { name: /Continue/i }).click();
  cy.wait("@getWork");
  cy.log("columnName ", columnName); //ColumnName is not used, eventually match enum or maybe pass in a user , or remove??
  reviews.forEach((row) => {
    cy.log("Single Column Review");
    cy.findByTestId("CypressOptions").then((element) => {
      setValidity(element, row);
    });
    cy.findByRole("button", { name: /Next/i }).should("be.enabled").click();
    cy.wait("@getWork");
  });
}

export function reviewMultipleColumns(
  columnName: string,
  reviews: MultipleColumnReview[]
) {
  cy.intercept("GET", "/api/work/next").as("getWork");
  cy.findByText(/Begin Task/i).should("be.visible");
  cy.findByRole("button", { name: /Continue/i }).click();
  cy.wait("@getWork");
  cy.log("columnName ", columnName); //ColumnName is not used, eventually match enum or maybe pass in a user , or remove??
  reviews.forEach((row) => {
    row.forEach((columnReview, index = 0) => {
      if (columnReview && columnReview.validity !== Validity.Unknown) {
        cy.findAllByTestId("CypressOptions")
          .eq(index)
          .then((element) => {
            setValidity(element, columnReview);
          });
      }
    });
    cy.findByRole("button", { name: /Next/i }).should("be.enabled").click();
    cy.wait("@getWork");
  });
}

export function reviewVoterRegistration(voterFlag: RegisteredVoterFlags[]) {
  cy.intercept("GET", "/api/work/next").as("getWork");
  cy.findByText(/Begin Task/i).should("be.visible");
  cy.findByRole("button", { name: /Continue/i }).click();
  cy.wait("@getWork");
  voterFlag.forEach((flagIndex) => {
    cy.findByRole("button", { name: /Search/i, timeout: 15000 }).should(
      "be.enabled"
    );
    /*cy.get('[id="noMismatch"]').should("be.visible")
     cy.get('[id="mismatchedName"]').should("be.visible")
     cy.get('[id="mismatchedAddress"]').should("be.visible")
     cy.get('[id="notRegistered"]').should("be.visible")
     */
    cy.get('[name="registeredVoterFlags"]')
      .should("have.length", 4)
      .as("voterFlags");
    if (flagIndex == 1) {
      // Valid / No Mismatch
      cy.get('[name="externalDataRecordId"]').last().check();
    }
    cy.get("@voterFlags")
      .eq(flagIndex - 1) // No Unknown option on page so index is -1
      .check();
    cy.findByRole("button", { name: /Next/i }).should("be.enabled").click();
    cy.wait("@getWork");
  });
}
