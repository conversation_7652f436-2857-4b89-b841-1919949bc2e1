export function deleteVoterRegistration(voterRegistrationFileName: string) {
  cy.contains("tr", voterRegistrationFileName)
    .should("be.visible")
    .within(() => {
      cy.findByRole("button").should("be.enabled").click(); //Trash Can Icon
    });
  cy.findByRole("dialog", { name: /Confirmation modal/i })
    .should("be.visible")
    .within(() => {
      cy.findByRole("button", { name: /Confirm/i })
        .should("be.enabled")
        .click();
    });

  cy.contains("tr", voterRegistrationFileName, { timeout: 90000 }).should(
    "not.exist"
  );
}

export function deleteVoterRegistrationIfNecessary(county: string) {
  cy.visit("/admin");
  cy.waitForNetworkIdle(500);

  cy.get(`tr:contains('${county}.csv')`)
    .should("have.length.gte", 0)
    .then(($tr) => {
      if (!$tr.length) {
        return;
      }
      deleteVoterRegistration(county);
    });
}

export function deleteTemplateFromUI(templateName: string) {
  cy.contains("tr", templateName)
    .should("be.visible")
    .within(() => {
      cy.findByRole("button").should("be.enabled").click(); //Trash Can Icon
    });
  cy.findByRole("dialog", { name: /Confirmation modal/i })
    .should("be.visible")
    .within(() => {
      cy.findByRole("button", { name: /Confirm/i })
        .should("be.enabled")
        .click();
    });
  cy.contains("tr", templateName).should("not.exist");
}

export function deleteTemplateIfNecessary(templateName: string) {
  cy.visit("/admin");
  cy.waitForNetworkIdle(500);

  cy.get(`tr:contains('${templateName}')`)
    .should("have.length.gte", 0)
    .then(($tr) => {
      if (!$tr.length) {
        return;
      }
      deleteTemplateFromUI(templateName);
    });
}

export function deleteMatterFromUI(matterName: string) {
  cy.contains("tr", matterName)
    .should("be.visible")
    .within(() => {
      cy.findByRole("button").should("be.enabled").click(); //Trash Can Icon
    });
  cy.findByRole("dialog", { name: /Confirmation modal/i })
    .should("be.visible")
    .within(() => {
      cy.findByRole("button", { name: /Confirm/i })
        .should("be.enabled")
        .click();
    });
  cy.waitForNetworkIdle(500);
  cy.contains("tr", matterName).should("not.exist");
}

export function deleteMatterIfNecessary(matterName: string) {
  cy.visit("/admin");
  cy.waitForNetworkIdle(500);

  cy.get(`tr:contains('${matterName}')`)
    .should("have.length.gte", 0)
    .then(($tr) => {
      if (!$tr.length) {
        return;
      }
      deleteMatterFromUI(matterName);
    });
}

export function deleteCypressMatters() {
  cy.visit("/admin");
  cy.waitForNetworkIdle(500);

  cy.get("body").then(($body) => {
    if ($body.find("tr").length > 0) {
      //Conditional check for empty table
      cy.get("tr").each(($tr) => {
        if ($tr.text().match(/Cypress.*Matter/i)) {
          deleteMatterFromUI($tr.text());
        }
      });
    }
  });
}

export function deleteCypressTemplates() {
  cy.visit("/admin");
  cy.waitForNetworkIdle(500);

  cy.get("body").then(($body) => {
    if ($body.find("tr").length > 0) {
      //Conditional check for empty table
      cy.get("tr").each(($tr) => {
        if ($tr.text().match(/Cypress.*Template/i)) {
          deleteTemplateFromUI($tr.text());
        }
      });
    }
  });
}
