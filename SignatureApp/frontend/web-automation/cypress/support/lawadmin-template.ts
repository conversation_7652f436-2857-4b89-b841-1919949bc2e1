export function createTemplate(name: string) {
  cy.findByRole("link", {name: /new template/i})
    .should("be.visible")
    .click();
  cy.url().should("include", "/admin/templates/new");
  cy.findByPlaceholderText(/Enter template name/i)
    .should("be.visible")
    .type(name);
  cy.findByRole("button", {name: /Save/i}).should("be.enabled").click();
  cy.url().should("include", "/admin/templates");
  cy.findByRole("alert")
    .should("be.visible")
    .and("have.text", "Template created successfully");
}

export function uploadTemplateFile(filePath: string, pageSize: string, usState: string = "AZ") {
  cy.findByTestId("select-page-size").should("be.visible").select(pageSize);
  // cy.findByTestId("select-us-state").should("be.visible").select(usState);
  cy.get("input[type=file]").selectFile(filePath);
  cy.findByRole("button", {name: /Upload/i})
    .should("be.enabled")
    .click();
  cy.findByRole("link", {
    name: /Identify Fields/i,
    timeout: 30000,
  }).should("exist");
  cy.findByRole("link", {name: /Identify Columns/i}).should("exist");
}

type FieldDefinition = {
  find: string;
  inputType?: string;
  groupName?: string;
  prefix?: string;
};

export function identifyTemplateFields(
  name: string,
  fields: FieldDefinition[]
) {
  cy.contains("tr", name).should("be.visible").click();
  cy.findByRole("link", {name: /Identify Fields/i})
    .should("be.visible")
    .click();
  cy.url().should("include", "/field-identification");

  fields.forEach((field) => {
    cy.findAllByDisplayValue(field.find).should("be.visible");
    cy.findByRole("checkbox", {name: /is handwritten/i}).should("be.checked");
    cy.findByRole("checkbox", {name: /can be invalid/i}).should("be.checked");

    const regexSignature = /\bsignature\b/i;
    if (regexSignature.test(field.find)) {
      cy.findByRole("checkbox", {name: /is signature/i}).should("be.checked");
    } else {
      cy.findByRole("checkbox", {name: /is signature/i}).should(
        "not.be.checked"
      );
    }

    const regexDate = /\bdate\b/i;
    if (regexDate.test(field.find)) {
      cy.findByRole("checkbox", {name: /is date/i}).should("be.checked");
    } else {
      cy.findByRole("checkbox", {name: /is date/i}).should("not.be.checked");
    }

    if (field.inputType === "Checkbox") {
      cy.findByRole("combobox").select("CheckBox");
    } else if (field.inputType === "TextArea") {
      cy.findByRole("combobox").select("TextArea");
    }

    if (field.groupName) {
      cy.findByPlaceholderText(/Enter group name/i)
        .should("be.visible")
        .clear()
        .type(field.groupName);
    }

    cy.findByRole("button", {name: /next/i}).should("be.enabled").click();
  });

  cy.findByRole("link", {name: /click here/i})
    .should("be.visible")
    .click();
}

export function identifyTemplateColumns(name: string, columns: string[]) {
  cy.contains("tr", name).should("be.visible").click();
  cy.findByRole("link", {name: /Identify Columns/i})
    .should("be.visible")
    .click();
  cy.url().should("include", "/column-identification");

  columns.forEach((column) => {
    cy.findByRole("img", {name: /signature-form/i}).should("be.visible"); // Give some time for the image to load

    if (!column) {
      cy.findByRole("button", {name: /Skip this field/i})
        .should("be.enabled")
        .click();
    } else {
      cy.findByPlaceholderText(/Enter field name/i)
        .should("be.visible")
        .clear()
        .type(column);

      cy.findByRole("checkbox", {name: /is handwritten/i}).should(
        "be.checked"
      );
      cy.findByRole("checkbox", {name: /can be invalid/i}).should(
        "be.checked"
      );

      if (/signature/i.test(column)) {
        cy.findByRole("checkbox", {name: /is signature/i}).should(
          "be.checked"
        );
      } else {
        cy.findByRole("checkbox", {name: /is signature/i})
          .uncheck()
          .should("not.be.checked");
      }

      if (/name/i.test(column)) {
        cy.findByRole("checkbox", {name: /is name/i}).should("be.checked"); // EDIT First Name is failing to be checked 2019 initiative
      } else {
        cy.findByRole("checkbox", {name: /is name/i})
          .uncheck()
          .should("not.be.checked");
      }

      if (/address/i.test(column) || /city or town/i.test(column)) {
        cy.findByRole("checkbox", {name: /is address/i})
          .check()
          .should("be.checked");
      } else {
        cy.findByRole("checkbox", {name: /is address/i})
          .uncheck()
          .should("not.be.checked");
      }

      if (/date/i.test(column)) {
        cy.findByRole("checkbox", {name: /is date/i}).should("be.checked");
      } else {
        cy.findByRole("checkbox", {name: /is date/i})
          .uncheck()
          .should("not.be.checked");
      }

      if (/voter\s?id/i.test(column)) {
        cy.findByRole("checkbox", {name: /is voter id/i}).should(
          "be.checked"
        ); // EDIT VoterID is failing to be checked Equal
      } else {
        cy.findByRole("checkbox", {name: /is voter id/i})
          .uncheck()
          .should("not.be.checked");
      }

      cy.findByRole("button", {name: /next/i}).should("be.enabled").click();
    }
  });

  cy.findByRole("link", {name: /click here/i})
    .should("be.visible")
    .click();
}
