/// <reference types='cypress' />
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands

import "@testing-library/cypress/add-commands";

Cypress.Commands.add("signIn", (email, password) => {
  const loginEmail: string = email;
  const loginPassword: string = password;

  if (!loginEmail || !loginPassword) {
    throw new Error("Email and Password are required, update cypress.env.json");
  }

  cy.intercept("/api/authenticate").as("signIn");
  cy.session([loginEmail, loginPassword], () => {
    cy.visit("/login");
    cy.findByRole("heading", { name: /Sign In/i }).should("be.visible");
    cy.findByPlaceholderText("Email").type(loginEmail);
    cy.findByPlaceholderText("Password").type(loginPassword);
    cy.findByRole("button", { name: /Go/i }).should("be.enabled").click();
    cy.wait("@signIn").then((res) => {
      if (res.response) {
        expect(res.response.statusCode).to.eq(200);
      }
    });
  });
});

Cypress.Commands.add("reviewValid", () => {
  cy.findAllByRole("radio").should("not.be.checked");
  cy.findByRole("radio", { name: "Valid" }).should("be.visible").check();
});
