export function inviteTeamMember(role: string, email: string) {
  cy.findByRole("link", { name: /invite team member/i })
    .should("be.visible")
    .click();
  cy.url().should("include", "/admin/invite");
  cy.findByRole("heading", { name: /Invite/i }).should("be.visible");
  cy.findByPlaceholderText(/Enter email address/i)
    .should("be.visible")
    .type(email);
  cy.findByRole("combobox").should("be.visible").select(role);
  cy.findByRole("button", { name: /Invite/i })
    .should("be.enabled")
    .click();
  cy.findByText(/An invitation has been sent/i).should("be.visible");
}

export function deleteInvitedUser(email: string) {
  const sql_query = `DELETE
                     FROM dbo.Users
                     where Email = '${email}'`;
  cy.log(sql_query);
  cy.task("runSqlQuery", sql_query);
}

export type VoterColumnMapping = {
  columnName: string;
  mapsTo: string;
};

export function uploadVoterFiles(
  name: string,
  filePaths: string[],
  mappings: VoterColumnMapping[]
) {
  cy.findByRole("link", { name: /new voter file/i })
    .should("be.visible")
    .click();
  cy.url().should("include", "/admin/voters/new");
  cy.findAllByRole("textbox").eq(0).type(name); //County
  cy.findAllByRole("textbox").eq(1).clear().type(filePaths.length.toString()); //Number of parts

  filePaths.forEach((filePath, fileIndex) => {
    cy.get("input[type=file]").eq(fileIndex).selectFile(filePath);

    cy.findByRole("table").should("be.visible");
    if (fileIndex == 0) {
      mappings.forEach((mapping) => {
        // TODO: this part should probably be a command
        cy.get("table thead tr").then(($headerRow) => {
          cy.wrap($headerRow)
            .eq(1)
            .find("th")
            .each(($headerCell, colIndex) => {
              if ($headerCell.text() == mapping.columnName) {
                cy.wrap($headerRow)
                  .eq(0)
                  .find("th")
                  .eq(colIndex)
                  .then((arg) => {
                    cy.wrap(arg).find("select").select(mapping.mapsTo);
                    cy.wrap(arg)
                      .get("option:selected")
                      .contains(mapping.mapsTo);
                  });
              }
            });
        });
      });
    }
    cy.findAllByRole("button", { name: /Upload/i })
      .eq(fileIndex)
      .click();
    cy.findAllByText(/File uploaded successfully/i)
      .eq(fileIndex)
      .should("be.visible", {
        timeout: 15000,
      });
    cy.get('*[class="spinner-border"]').should("not.exist", {
      timeout: 60000,
    });
  });
  cy.findByRole("link", { name: /Back to dashboard/i })
    .should("be.visible")
    .click();
  cy.contains("tr", `${name}.csv`).should("be.visible");
}

export function uploadMultiPartVoterFile(name: string, filePath: string[]) {
  cy.findByRole("link", { name: /new voter file/i })
    .should("be.visible")
    .click();
  cy.url().should("include", "/admin/voters/new");

  cy.pause(); // TODO: need to set the required number of signatures

  cy.findAllByRole("textbox").eq(0).type(name); //County
  cy.get("input[type=file]").selectFile(filePath[0]); // TODO: iterate one at a time
  cy.findByRole("table").should("be.visible");
  // TODO: set all the parameters
  cy.findByRole("button", { name: /Upload/i }).click();
  cy.findByText(/File uploaded successfully/i).should("be.visible", {
    timeout: 10000,
  });
  cy.get('*[class="spinner-border"]').should("not.exist", {
    timeout: 30000,
  });
  cy.findByRole("link", { name: /Back to dashboard/i })
    .should("be.visible")
    .click();
  cy.contains("tr", `${name}.csv`).should("be.visible");
}
