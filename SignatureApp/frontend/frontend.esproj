<Project Sdk="Microsoft.VisualStudio.JavaScript.Sdk/0.4.0-alpha">
  <PropertyGroup Label="Globals">
    <ProjectGuid>72067536-26a6-477d-bf28-9375b07c1715</ProjectGuid>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <StartupCommand>set BROWSER=none&amp;&amp;npm start</StartupCommand>
    <JavaScriptTestRoot>src\</JavaScriptTestRoot>
    <JavaScriptTestFramework>Jest</JavaScriptTestFramework>
  </PropertyGroup>
  <ItemGroup>
	  <Script Include="**"/>
	  <Script Remove="**.d.ts"/>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="src\common\" />
    <Folder Include="src\components\" />
    <Folder Include="src\services\" />
	<Folder Include="src\types\" />
  </ItemGroup>
  <!-- This target is copied from the ASP.NET SPA template in order to ensure node_modules are in place. -->
  <Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') ">
    <!-- Ensure Node.js is installed -->
    <Exec Command="node --version" ContinueOnError="true">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
    </Exec>
    <Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build and run this project. To continue, please install Node.js from https://nodejs.org/, and then restart your command prompt or IDE." />
    <Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
  </Target>
</Project>