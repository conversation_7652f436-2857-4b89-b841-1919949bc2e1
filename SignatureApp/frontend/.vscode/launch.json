{"version": "0.2.0", "configurations": [{"type": "chrome", "request": "launch", "name": "localhost (Chrome)", "preLaunchTask": "npm: start", "url": "https://localhost:3000", "webRoot": "${workspaceFolder}"}, {"type": "msedge", "request": "launch", "name": "localhost (Edge)", "preLaunchTask": "npm: start", "url": "https://localhost:3000", "webRoot": "${workspaceFolder}"}, {"type": "firefox", "request": "launch", "name": "localhost (Firefox Dev)", "preLaunchTask": "npm: start", "url": "https://localhost:3000", "webRoot": "${workspaceFolder}", "firefoxExecutable": "/Applications/Firefox Developer Edition.app/Contents/MacOS/firefox"}]}