# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the frontend for SignatureApp - a React-based web application for managing signature collection petitions in the legal/election domain. The application handles petition templates, signature sheet processing, voter validation, deficiency tracking, and review workflows with different user roles (<PERSON><PERSON>, Manager, Reviewer).

## Development Commands

### Build and Development
- `npm start` - Development server with HTTPS enabled (requires SSL cert setup via aspnetcore-https.js)
- `npm run build:dev` - Production build for development environment to ./build_dev
- `npm run build:prod` - Production build for production environment to ./build_prod  
- `npm test` - Run Jest tests in watch mode

### Testing
- `npm test` - Unit tests with Jest/React Testing Library
- `cd web-automation && npm run cypress:open` - End-to-end tests with Cypress
- Cypress tests include Azure Blob Storage integration for file uploads and SQL Server integration for database operations

## Architecture

### Application Structure
- **React 17** with TypeScript
- **React Router v6** for routing with role-based access (admin, manager, reviewer routes)
- **Bootstrap 5** for styling
- **Axios** for HTTP requests with custom service layer

### Key Directories
- `src/pages/` - Route components organized by user role (admin/, manager/, auth/, review/)
- `src/components/` - Reusable UI components
- `src/services/` - API service layer with authentication and base HTTP utilities
- `src/types/` - TypeScript type definitions
- `src/contexts/` - React contexts (AuthProvider, MatterContext)
- `src/hooks/` - Custom React hooks
- `web-automation/` - Cypress E2E test suite

### Authentication & Context
- JWT-based authentication stored in localStorage
- `AuthProvider` context manages user state across the application
- `MatterProvider` context provides matter-specific data to nested routes
- Automatic logout on 401 responses via axios interceptors

### Service Layer Pattern
- All API calls go through services that extend `useAuthenticatedRequestService()`
- Base service provides authenticated HTTP methods (authGet, authPost, etc.)
- Environment variable `REACT_APP_BACKEND_URL` configures backend API endpoint

### User Roles & Routing
- **Admin**: Full access to templates, matters, deficiency review, user management
- **Manager**: Matter management, group assignments, flagged work review  
- **Reviewer**: Work item review and completion

### Matter-Centric Workflow
- "Matters" are petition campaigns with associated templates, signature sheets, and voters
- Complex routing structure under `/admin/matters/:matterId/` for different matter views
- MatterProvider context automatically loads matter data based on URL parameter

### File Processing
- Signature sheet upload and processing (PDF/TIFF files)
- Voter file upload (CSV format)
- Template management for signature sheet layouts
- Azure Blob Storage integration for file storage

### Testing Infrastructure
- Cypress tests use Azure Storage Emulator (Azurite) for blob operations
- SQL Server integration for database state management during tests
- Test data fixtures include sample signature sheets, templates, and voter files